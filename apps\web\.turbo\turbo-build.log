
> web@1.0.0 build
> next build

   ▲ Next.js 15.3.1

   Linting and checking validity of types ...
Failed to compile.

./components/quiz/QuizShell.tsx:57:30
Type error: Argument of type 'string' is not assignable to parameter of type '"EN" | "ZH" | "MS"'.

[0m [90m 55 |[39m       [36mif[39m (data[33m.[39mchildLanguagePreferences) {[0m
[0m [90m 56 |[39m         console[33m.[39mlog([32m'QuizShell: Setting child language preferences:'[39m[33m,[39m data[33m.[39mchildLanguagePreferences)[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m 57 |[39m         setChildQuizLanguage(data[33m.[39mchildLanguagePreferences[33m.[39mquizLanguage)[33m;[39m[0m
[0m [90m    |[39m                              [31m[1m^[22m[39m[0m
[0m [90m 58 |[39m         setChildMenuLanguage(data[33m.[39mchildLanguagePreferences[33m.[39mmenuLanguage)[33m;[39m[0m
[0m [90m 59 |[39m[0m
[0m [90m 60 |[39m         [90m// Store language preferences in localStorage for other components to access[39m[0m
Next.js build worker exited with code: 1 and signal: null
npm error Lifecycle script `build` failed with error:
npm error code 1
npm error path D:\projects\GitHub\my-quiz-app\apps\web
npm error workspace web@1.0.0
npm error location D:\projects\GitHub\my-quiz-app\apps\web
npm error command failed
npm error command C:\WINDOWS\system32\cmd.exe /d /s /c next build
