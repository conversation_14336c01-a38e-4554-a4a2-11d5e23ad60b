"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/quiz-type";
exports.ids = ["pages/api/quiz-type"];
exports.modules = {

/***/ "(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz-type&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz-type.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz-type&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz-type.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_quiz_type_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\quiz-type.ts */ \"(api-node)/./pages/api/quiz-type.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_quiz_type_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_quiz_type_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/quiz-type\",\n        pathname: \"/api/quiz-type\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_quiz_type_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnF1aXotdHlwZSZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTVDYXBpJTVDcXVpei10eXBlLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNFO0FBQzFEO0FBQ3VEO0FBQ3ZEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyxvREFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMsb0RBQVE7QUFDcEM7QUFDTyx3QkFBd0IseUdBQW1CO0FBQ2xEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlc1xcXFxhcGlcXFxccXVpei10eXBlLnRzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGhhbmRsZXIgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc0FQSVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFU19BUEksXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9xdWl6LXR5cGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9xdWl6LXR5cGVcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz-type&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz-type.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/../../packages/db/index.ts":
/*!**********************************!*\
  !*** ../../packages/db/index.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _prisma_client__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"prisma\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _prisma_client__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'error'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n// Re-export Prisma types for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uLi8uLi9wYWNrYWdlcy9kYi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FDWEYsZ0JBQWdCRSxNQUFNLElBQ3RCLElBQUlILHdEQUFZQSxDQUFDO0lBQ2ZJLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUc7QUFFTCxJQUFJQyxJQUFxQyxFQUFFSixnQkFBZ0JFLE1BQU0sR0FBR0E7QUFFcEUsaUVBQWVBLE1BQU1BLEVBQUM7QUFFdEIseUNBQXlDO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxccGFja2FnZXNcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbCBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHxcbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ11cbiAgfSk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7XG5cbi8vIFJlLWV4cG9ydCBQcmlzbWEgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgKiBmcm9tICdAcHJpc21hL2NsaWVudCc7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/../../packages/db/index.ts\n");

/***/ }),

/***/ "(api-node)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   hashPin: () => (/* binding */ hashPin),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var argon2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! argon2 */ \"argon2\");\n/* harmony import */ var argon2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(argon2__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function hashPassword(password) {\n    const salt = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(16).toString('base64');\n    const hash = await argon2__WEBPACK_IMPORTED_MODULE_0__.hash(password + salt, {\n        type: argon2__WEBPACK_IMPORTED_MODULE_0__.argon2id,\n        memoryCost: 65536,\n        timeCost: 3,\n        parallelism: 4\n    });\n    return {\n        hash,\n        salt\n    };\n}\nasync function verifyPassword(password, hash, salt) {\n    return await argon2__WEBPACK_IMPORTED_MODULE_0__.verify(hash, password + salt);\n}\nasync function hashPin(pin, salt) {\n    // Reuse parent's salt if provided, otherwise generate new one\n    const pinSalt = salt || crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(16).toString('base64');\n    const hash = await argon2__WEBPACK_IMPORTED_MODULE_0__.hash(pin + pinSalt, {\n        type: argon2__WEBPACK_IMPORTED_MODULE_0__.argon2id,\n        memoryCost: 65536,\n        timeCost: 3,\n        parallelism: 4\n    });\n    return {\n        hash,\n        salt: pinSalt\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/auth.ts\n");

/***/ }),

/***/ "(api-node)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _quiz_db__WEBPACK_IMPORTED_MODULE_0__.prisma)\n/* harmony export */ });\n/* harmony import */ var _quiz_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @quiz/db */ \"(api-node)/../../packages/db/index.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkMiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxcYXBwc1xcd2ViXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBwcmlzbWEgYXMgZGVmYXVsdCB9IGZyb20gJ0BxdWl6L2RiJzsiXSwibmFtZXMiOlsicHJpc21hIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/prisma.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"next-auth/providers/credentials\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/prisma */ \"(api-node)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(api-node)/./lib/auth.ts\");\n\n\n\n // Assuming these are needed for verification\n// Define the configuration and export it\nconst authOptions = {\n    providers: [\n        next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1___default()({\n            name: 'Credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'text'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                },\n                username: {\n                    label: 'Username',\n                    type: 'text'\n                },\n                pin: {\n                    label: 'PIN',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials, req) {\n                if (!credentials) {\n                    return null;\n                }\n                const { email, password, username, pin } = credentials;\n                // Parent login with email/password\n                if (email && password) {\n                    const account = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].account.findUnique({\n                        where: {\n                            email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            role: true,\n                            password_hash: true,\n                            salt: true,\n                            status: true\n                        }\n                    });\n                    if (!account || account.status !== 'ACTIVE') {\n                        return null; // Account not found or not active\n                    }\n                    if (!account.password_hash || !account.salt) {\n                        console.error(`Invalid account state: Missing hash/salt for ${email}`);\n                        return null;\n                    }\n                    const isValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyPassword)(password, account.password_hash, account.salt);\n                    if (!isValid) {\n                        return null; // Invalid password\n                    }\n                    // Return user data to be stored in the session\n                    return {\n                        id: account.id.toString(),\n                        name: account.name,\n                        email: account.email,\n                        role: account.role\n                    };\n                }\n                // Child login with username/pin\n                if (username && pin) {\n                    const child = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].child.findUnique({\n                        where: {\n                            username\n                        },\n                        select: {\n                            id: true,\n                            name: true,\n                            username: true,\n                            pin_hash: true,\n                            salt: true,\n                            accountId: true,\n                            account: {\n                                select: {\n                                    status: true\n                                }\n                            }\n                        }\n                    });\n                    if (!child || !child.account || child.account.status !== 'ACTIVE') {\n                        return null; // Child or associated account not found or not active\n                    }\n                    if (!child.pin_hash || !child.salt) {\n                        console.error(`Invalid account state: Missing hash/salt for child ${username}`);\n                        return null;\n                    }\n                    const isValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyPassword)(pin, child.pin_hash, child.salt);\n                    if (!isValid) {\n                        return null; // Invalid PIN\n                    }\n                    // Return user data to be stored in the session\n                    return {\n                        id: child.id.toString(),\n                        name: child.name,\n                        username: child.username,\n                        role: 'CHILD',\n                        parentId: child.accountId?.toString()\n                    };\n                }\n                return null; // Invalid credentials provided\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Add user role and other properties to the token\n            if (user) {\n                token.role = user.role;\n                if (user.parentId) {\n                    token.parentId = user.parentId;\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Add user role and other properties to the session\n            if (token) {\n                session.user.id = token.sub; // sub is the user id from authorize\n                session.user.role = token.role;\n                if (token.parentId) {\n                    session.user.parentId = token.parentId;\n                }\n            }\n            return session;\n        }\n    },\n    // Optional: Add pages for custom sign-in, sign-out, error\n    pages: {\n        signIn: '/login'\n    },\n    // Optional: Configure session\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    // Optional: Add secret for signing and encrypting tokens\n    secret: process.env.NEXTAUTH_SECRET\n};\n// Export the NextAuth handler\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/quiz-type.ts":
/*!********************************!*\
  !*** ./pages/api/quiz-type.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../lib/prisma */ \"(api-node)/./lib/prisma.ts\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n\n\n\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            message: 'Method Not Allowed'\n        });\n    }\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session) {\n        return res.status(401).json({\n            message: 'Unauthorized'\n        });\n    }\n    const { attemptId } = req.query;\n    if (!attemptId || typeof attemptId !== 'string') {\n        return res.status(400).json({\n            message: 'Quiz attempt ID is required'\n        });\n    }\n    try {\n        // Get the quiz attempt\n        const quizAttempt = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].quizAttempt.findUnique({\n            where: {\n                id: Number(attemptId)\n            },\n            select: {\n                id: true,\n                unitId: true,\n                quizType: true,\n                studentAnswers: true\n            }\n        });\n        if (!quizAttempt) {\n            return res.status(404).json({\n                message: 'Quiz attempt not found'\n            });\n        }\n        // Get the quiz type directly from the database\n        // The quizType field is an enum in the database (MASTERY, TEST, QUICK)\n        // Convert it to lowercase for consistency with the frontend\n        const quizType = quizAttempt.quizType.toLowerCase();\n        // Return the quiz type\n        res.status(200).json({\n            quizType\n        });\n    } catch (error) {\n        console.error('Error inferring quiz type:', error);\n        res.status(500).json({\n            message: 'Internal Server Error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/quiz-type.ts\n");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "argon2":
/*!*************************!*\
  !*** external "argon2" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("argon2");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/credentials":
/*!**************************************************!*\
  !*** external "next-auth/providers/credentials" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/credentials");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz-type&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz-type.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();