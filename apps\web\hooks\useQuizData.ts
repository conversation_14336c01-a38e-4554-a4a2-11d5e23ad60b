import { useState, useEffect } from 'react';
import use<PERSON><PERSON> from 'swr';
import { useSession } from 'next-auth/react';
import { Question, QuizAttempt } from '../types/quiz';

interface QuizData {
  quizAttempt: QuizAttempt;
  questions: Question[];
}

interface UseQuizDataReturn {
  attempt: QuizAttempt | undefined;
  questions: Question[];
  isLoading: boolean;
  error: Error | undefined;
  quizType: 'MASTERY' | 'TEST' | 'QUICK';
  mutate: () => Promise<QuizData | undefined>;
}

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Error fetching quiz data: ${response.statusText}`);
  }
  return response.json();
};

/**
 * Hook to fetch and manage quiz data
 * @param attemptId The quiz attempt ID to fetch
 * @param authReady Whether authentication is ready (session loaded)
 * @returns Quiz data, loading state, and error
 */
export function useQuizData(
  attemptId: string | string[] | undefined,
  authReady: boolean = true
): UseQuizDataReturn {
  const { status } = useSession();
  const [quizType, setQuizType] = useState<'MASTERY' | 'TEST' | 'QUICK'>('TEST');

  // Only fetch if authenticated, auth is ready, and attemptId exists and is a string
  const shouldFetch = 
    authReady && 
    status === 'authenticated' && 
    attemptId && 
    !Array.isArray(attemptId);

  // Use SWR to fetch quiz data
  const { data, error, mutate } = useSWR<QuizData>(
    shouldFetch ? `/api/quiz/${attemptId}` : null,
    fetcher
  );

  // Fetch quiz type if not included in the response
  useEffect(() => {
    if (data?.quizAttempt?.quizType) {
      setQuizType(data.quizAttempt.quizType);
    } else if (shouldFetch && attemptId) {
      const fetchQuizType = async () => {
        try {
          const quizTypeResponse = await fetch(`/api/quiz-type?attemptId=${attemptId}`);
          if (quizTypeResponse.ok) {
            const quizTypeData = await quizTypeResponse.json();
            setQuizType(quizTypeData.quizType.toUpperCase());
          }
        } catch (error) {
          console.error('Error fetching quiz type:', error);
          // Default to TEST if there's an error
          setQuizType('TEST');
        }
      };
      fetchQuizType();
    }
  }, [data, shouldFetch, attemptId]);

  // Save quiz state when navigating away (only if authenticated)
  useEffect(() => {
    if (status !== 'authenticated' || !data) return;

    const quizAttemptId = data.quizAttempt?.id;
    const currentIndex = data.quizAttempt?.currentQuestionIndex || 0;
    const total = data.questions?.length || 0;

    const handleBeforeUnload = async () => {
      if (quizAttemptId && currentIndex < total && total > 0) {
        // Save current progress
        try {
          await fetch('/api/log-quiz-attempt', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              quizAttemptId: quizAttemptId,
              currentQuestionIndex: currentIndex,
            }),
          });
        } catch (error) {
          console.error('Error saving quiz progress on beforeunload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup function: remove listener and save on unmount/navigation
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Save progress on component unmount (for client-side navigation)
      if (quizAttemptId && currentIndex < total && total > 0) {
        fetch('/api/log-quiz-attempt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            quizAttemptId: quizAttemptId,
            currentQuestionIndex: currentIndex,
          }),
        }).catch(error => console.error('Error saving quiz progress on unmount:', error));
      }
    };
  }, [data, status]);

  return {
    attempt: data?.quizAttempt,
    questions: data?.questions || [],
    isLoading: !error && !data,
    error,
    quizType,
    mutate
  };
}

export default useQuizData;
