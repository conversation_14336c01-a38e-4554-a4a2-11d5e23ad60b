import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Dashboard from '../components/Dashboard';
import { useSession } from 'next-auth/react';

const DashboardPage = () => {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  const handleStartQuiz = (questionTypes: ('multiple_choice' | 'short_answer')[]) => {
    const typesQuery = questionTypes.join(',');
    router.push(`/quiz?types=${typesQuery}`); // Navigate to the quiz page with selected types
  };

  // Show a loading state or null while authentication is being checked
  if (status === 'loading' || status === 'unauthenticated') {
    return <div>Loading...</div>; // Or a more sophisticated loading component
  }

  return <Dashboard />; // Remove onStartQuiz prop
};

export default DashboardPage;
