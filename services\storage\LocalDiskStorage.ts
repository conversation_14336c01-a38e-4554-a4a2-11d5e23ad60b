import fs from 'fs/promises';
import path from 'path';
import { StorageDriver } from './StorageDriver';

/**
 * Local disk storage implementation of StorageDriver
 * Stores files in the 'uploads' directory at the project root
 */
export class LocalDiskStorage implements StorageDriver {
  private baseDir: string;

  constructor() {
    // Use project root/uploads as the base directory
    this.baseDir = path.join(process.cwd(), 'uploads');
  }

  /**
   * Save a file to local disk
   * @param file Buffer containing file data
   * @param key Unique identifier/path for the file
   * @param mime MIME type of the file (not used for local storage but kept for interface compatibility)
   * @returns Promise resolving to the public URL of the saved file
   */
  async save(file: Buffer, key: string, mime: string): Promise<string> {
    // Create the uploads directory if it doesn't exist
    await fs.mkdir(this.baseDir, { recursive: true });
    
    // Create the full path to the file
    const dest = path.join(this.baseDir, key);
    
    // Write the file to disk
    await fs.writeFile(dest, file);
    
    // Return the public URL (relative to the server root)
    return `/uploads/${key}`;
  }

  /**
   * Delete a file from local disk
   * @param key Unique identifier/path of the file to delete
   */
  async delete(key: string): Promise<void> {
    try {
      // Delete the file
      await fs.unlink(path.join(this.baseDir, key));
    } catch (error) {
      // Ignore errors if the file doesn't exist
      // This makes the delete operation idempotent
    }
  }
}
