-- CreateTable
CREATE TABLE "TG_TranslationLog" (
    "id" SERIAL NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "translatedText" TEXT NOT NULL,
    "childId" INTEGER NOT NULL,
    "questionId" INTEGER NOT NULL,

    CONSTRAINT "TG_TranslationLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_QuizAttempt" (
    "id" SERIAL NOT NULL,
    "startTime" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "endTime" TIMESTAMP(3),
    "score" DOUBLE PRECISION,
    "childId" INTEGER NOT NULL,
    "subjectId" INTEGER NOT NULL,
    "unitId" INTEGER NOT NULL,

    CONSTRAINT "TG_QuizAttempt_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TG_StudentAnswer" (
    "id" SERIAL NOT NULL,
    "submittedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "submittedAnswer" TEXT NOT NULL,
    "isCorrect" BOOLEAN NOT NULL,
    "childId" INTEGER NOT NULL,
    "questionId" INTEGER NOT NULL,
    "quizAttemptId" INTEGER NOT NULL,

    CONSTRAINT "TG_StudentAnswer_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "TG_TranslationLog" ADD CONSTRAINT "TG_TranslationLog_childId_fkey" FOREIGN KEY ("childId") REFERENCES "TG_Child"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_TranslationLog" ADD CONSTRAINT "TG_TranslationLog_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "TG_Question"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_QuizAttempt" ADD CONSTRAINT "TG_QuizAttempt_childId_fkey" FOREIGN KEY ("childId") REFERENCES "TG_Child"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_QuizAttempt" ADD CONSTRAINT "TG_QuizAttempt_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "TG_Subject"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_QuizAttempt" ADD CONSTRAINT "TG_QuizAttempt_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "TG_Unit"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_StudentAnswer" ADD CONSTRAINT "TG_StudentAnswer_childId_fkey" FOREIGN KEY ("childId") REFERENCES "TG_Child"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_StudentAnswer" ADD CONSTRAINT "TG_StudentAnswer_questionId_fkey" FOREIGN KEY ("questionId") REFERENCES "TG_Question"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_StudentAnswer" ADD CONSTRAINT "TG_StudentAnswer_quizAttemptId_fkey" FOREIGN KEY ("quizAttemptId") REFERENCES "TG_QuizAttempt"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
