import AdminDashboard from '../../components/AdminDashboard';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

export default function AdminPage() {
  const router = useRouter();

  // Client-side redirect as a fallback
  useEffect(() => {
    const checkSession = async () => {
      const res = await fetch('/api/auth/session');
      const session = await res.json();

      if (!session || !session.user || session.user.role !== 'ADMIN') {
        router.push('/login');
      }
    };

    checkSession();
  }, [router]);

  return (
    <div className="container mx-auto">
      <AdminDashboard />
    </div>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (!session || session.user?.role !== 'ADMIN') {
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
}