import { grade } from '../lib/grading';
import { Answer, AnswerType, StudentAnswer } from '@prisma/client';

// Mock Answer and StudentAnswer types
const createMockAnswer = (
  type: AnswerType,
  key?: string,
  text?: string,
  answerSpec?: any
): Answer => {
  return {
    id: 1,
    questionId: 1,
    type,
    key,
    textEn: text,
    textZh: null,
    textMs: null,
    answerSpec,
    createdAt: new Date()
  } as Answer;
};

const createMockStudentAnswer = (
  submittedKey?: string,
  submittedText?: string,
  submittedJson?: any
): StudentAnswer => {
  return {
    id: 1,
    questionId: 1,
    childId: 1,
    quizAttemptId: 1,
    submittedKey,
    submittedText,
    submittedJson,
    isCorrect: false,
    submittedAt: new Date()
  } as StudentAnswer;
};

describe('grade function', () => {
  describe('SINGLE_CHOICE answers', () => {
    test('should correctly grade a correct single choice answer', () => {
      const answer = createMockAnswer('SINGLE_CHOICE', 'A');
      const studentAnswer = createMockStudentAnswer('A');
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade an incorrect single choice answer', () => {
      const answer = createMockAnswer('SINGLE_CHOICE', 'A');
      const studentAnswer = createMockStudentAnswer('B');
      
      expect(grade(answer, studentAnswer)).toBe(false);
    });
  });
  
  describe('MULTI_CHOICE answers', () => {
    test('should correctly grade a correct multi-choice answer', () => {
      const answer = createMockAnswer(
        'MULTI_CHOICE', 
        null, 
        null, 
        { keys: ['A', 'C', 'D'] }
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { keys: ['A', 'C', 'D'] }
      );
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade a correct multi-choice answer with different order', () => {
      const answer = createMockAnswer(
        'MULTI_CHOICE', 
        null, 
        null, 
        { keys: ['A', 'C', 'D'] }
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { keys: ['D', 'A', 'C'] }
      );
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade an incorrect multi-choice answer (missing key)', () => {
      const answer = createMockAnswer(
        'MULTI_CHOICE', 
        null, 
        null, 
        { keys: ['A', 'C', 'D'] }
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { keys: ['A', 'C'] }
      );
      
      expect(grade(answer, studentAnswer)).toBe(false);
    });
    
    test('should correctly grade an incorrect multi-choice answer (extra key)', () => {
      const answer = createMockAnswer(
        'MULTI_CHOICE', 
        null, 
        null, 
        { keys: ['A', 'C'] }
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { keys: ['A', 'C', 'D'] }
      );
      
      expect(grade(answer, studentAnswer)).toBe(false);
    });
  });
  
  describe('SHORT_TEXT answers', () => {
    test('should correctly grade a correct short text answer', () => {
      const answer = createMockAnswer('SHORT_TEXT', null, 'Paris');
      const studentAnswer = createMockStudentAnswer(null, 'Paris');
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade a correct short text answer with different casing', () => {
      const answer = createMockAnswer('SHORT_TEXT', null, 'Paris');
      const studentAnswer = createMockStudentAnswer(null, 'paris');
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade a correct short text answer with extra whitespace', () => {
      const answer = createMockAnswer('SHORT_TEXT', null, 'Paris');
      const studentAnswer = createMockStudentAnswer(null, '  Paris  ');
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade an incorrect short text answer', () => {
      const answer = createMockAnswer('SHORT_TEXT', null, 'Paris');
      const studentAnswer = createMockStudentAnswer(null, 'London');
      
      expect(grade(answer, studentAnswer)).toBe(false);
    });
  });
  
  describe('MATCHING answers', () => {
    test('should correctly grade a correct matching answer', () => {
      const answer = createMockAnswer(
        'MATCHING', 
        null, 
        null, 
        { pairs: [
          { left: 'France', right: 'Paris' },
          { left: 'UK', right: 'London' },
          { left: 'Japan', right: 'Tokyo' }
        ]}
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { pairs: [
          { left: 'France', right: 'Paris' },
          { left: 'UK', right: 'London' },
          { left: 'Japan', right: 'Tokyo' }
        ]}
      );
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade an incorrect matching answer', () => {
      const answer = createMockAnswer(
        'MATCHING', 
        null, 
        null, 
        { pairs: [
          { left: 'France', right: 'Paris' },
          { left: 'UK', right: 'London' },
          { left: 'Japan', right: 'Tokyo' }
        ]}
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { pairs: [
          { left: 'France', right: 'London' }, // Incorrect
          { left: 'UK', right: 'Paris' },      // Incorrect
          { left: 'Japan', right: 'Tokyo' }
        ]}
      );
      
      expect(grade(answer, studentAnswer)).toBe(false);
    });
  });
  
  describe('SEQUENCING answers', () => {
    test('should correctly grade a correct sequencing answer', () => {
      const answer = createMockAnswer(
        'SEQUENCING', 
        null, 
        null, 
        { order: [1, 2, 3, 4, 5] }
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { order: [1, 2, 3, 4, 5] }
      );
      
      expect(grade(answer, studentAnswer)).toBe(true);
    });
    
    test('should correctly grade an incorrect sequencing answer', () => {
      const answer = createMockAnswer(
        'SEQUENCING', 
        null, 
        null, 
        { order: [1, 2, 3, 4, 5] }
      );
      const studentAnswer = createMockStudentAnswer(
        null, 
        null, 
        { order: [1, 3, 2, 4, 5] }
      );
      
      expect(grade(answer, studentAnswer)).toBe(false);
    });
  });
});
