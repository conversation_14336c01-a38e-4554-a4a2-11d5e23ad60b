import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      console.log('Fetching syllabus data...');

      // Fetch years with better error handling
      let years = [];
      try {
        years = await prisma.year.findMany({
          orderBy: {
            yearNumber: 'asc',
          },
        });
        console.log(`Successfully fetched ${years.length} years`);
      } catch (yearError) {
        console.error('Error fetching years:', yearError);
        // Continue execution to fetch other data
      }

      // Fetch subjects with better error handling
      let subjects = [];
      try {
        subjects = await prisma.subject.findMany({
          orderBy: {
            name: 'asc',
          },
        });
        console.log(`Successfully fetched ${subjects.length} subjects`);
      } catch (subjectError) {
        console.error('Error fetching subjects:', subjectError);
        // Continue execution to fetch other data
      }

      // Process subject filter if provided
      const selectedSubjectName = req.query.subject as string;
      let subjectId = null;

      if (selectedSubjectName && subjects.length > 0) {
        const selectedSubject = subjects.find(subject => subject.name === selectedSubjectName);
        if (selectedSubject) {
          subjectId = selectedSubject.id;
          console.log(`Filtering units by subject: ${selectedSubjectName} (ID: ${subjectId})`);
        } else {
          console.log(`Subject not found: ${selectedSubjectName}`);
        }
      }

      // Fetch units with better error handling
      let units = [];
      try {
        // Only filter by subjectId if it's not null
        const whereClause = subjectId ? { subjectId } : {};

        units = await prisma.unit.findMany({
          where: whereClause,
          orderBy: {
            unitNumber: 'asc',
          },
          include: {
            subject: {
              select: {
                id: true,
                name: true,
              },
            },
            year: {
              select: {
                id: true,
                yearNumber: true,
              },
            },
          },
        });
        console.log(`Successfully fetched ${units.length} units`);
      } catch (unitError) {
        console.error('Error fetching units:', unitError);
        // Continue execution to return partial data
      }

      // Return the data we were able to fetch
      res.status(200).json({
        years,
        subjects,
        units,
        // Include metadata to help with debugging
        meta: {
          yearCount: years.length,
          subjectCount: subjects.length,
          unitCount: units.length,
          timestamp: new Date().toISOString(),
        }
      });
    } catch (error) {
      console.error('Database error in syllabus API:', error);
      res.status(500).json({
        error: 'Failed to load syllabus data',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    } finally {
      // Ensure Prisma connection is properly closed
      try {
        await prisma.$disconnect();
      } catch (disconnectError) {
        console.error('Error disconnecting from database:', disconnectError);
      }
    }
  } else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
