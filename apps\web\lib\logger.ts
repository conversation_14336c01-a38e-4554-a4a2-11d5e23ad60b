/**
 * Frontend logger utility with optional Sentry integration
 * 
 * Features:
 * - Console logging in development
 * - Optional Sentry error reporting in production
 * - Consistent logging interface across frontend components
 */

interface LogContext {
  [key: string]: any;
}

class FrontendLogger {
  private isDevelopment: boolean;
  private sentryEnabled: boolean;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.sentryEnabled = !!process.env.NEXT_PUBLIC_SENTRY_DSN;
  }

  /**
   * Log info message
   */
  info(message: string, context?: LogContext) {
    if (this.isDevelopment) {
      console.log(`[INFO] ${message}`, context || '');
    }
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: LogContext) {
    if (this.isDevelopment) {
      console.warn(`[WARN] ${message}`, context || '');
    }
  }

  /**
   * Log error message and optionally send to Sentry
   */
  error(error: Error | string, context?: LogContext) {
    const errorMessage = error instanceof Error ? error.message : error;
    const errorObj = error instanceof Error ? error : new Error(error);

    // Always log to console in development
    if (this.isDevelopment) {
      console.error(`[ERROR] ${errorMessage}`, context || '', errorObj);
    }

    // Send to Sentry in production if configured
    if (this.sentryEnabled && typeof window !== 'undefined') {
      try {
        // Placeholder for Sentry integration
        // When Sentry is properly configured, this would be:
        // import * as Sentry from '@sentry/nextjs';
        // Sentry.captureException(errorObj, { extra: context });
        
        console.warn('Sentry not configured. Error would be sent to Sentry:', {
          error: errorObj,
          context
        });
      } catch (sentryError) {
        console.error('Failed to send error to Sentry:', sentryError);
      }
    }
  }

  /**
   * Log debug message (only in development)
   */
  debug(message: string, context?: LogContext) {
    if (this.isDevelopment) {
      console.debug(`[DEBUG] ${message}`, context || '');
    }
  }

  /**
   * Set user context for error reporting
   */
  setUserContext(user: { id: string; email?: string; [key: string]: any }) {
    if (this.sentryEnabled && typeof window !== 'undefined') {
      try {
        // Placeholder for Sentry user context
        // When Sentry is properly configured, this would be:
        // import * as Sentry from '@sentry/nextjs';
        // Sentry.setUser(user);
        
        console.debug('User context would be set in Sentry:', user);
      } catch (error) {
        console.error('Failed to set user context in Sentry:', error);
      }
    }
  }

  /**
   * Add breadcrumb for debugging
   */
  addBreadcrumb(message: string, category: string = 'custom', level: 'info' | 'warning' | 'error' = 'info') {
    if (this.sentryEnabled && typeof window !== 'undefined') {
      try {
        // Placeholder for Sentry breadcrumb
        // When Sentry is properly configured, this would be:
        // import * as Sentry from '@sentry/nextjs';
        // Sentry.addBreadcrumb({ message, category, level });
        
        if (this.isDevelopment) {
          console.debug(`[BREADCRUMB] ${category}: ${message}`);
        }
      } catch (error) {
        console.error('Failed to add breadcrumb to Sentry:', error);
      }
    }
  }
}

// Create singleton instance
export const frontendLogger = new FrontendLogger();

// Export convenience methods
export const logInfo = (message: string, context?: LogContext) => frontendLogger.info(message, context);
export const logWarn = (message: string, context?: LogContext) => frontendLogger.warn(message, context);
export const logError = (error: Error | string, context?: LogContext) => frontendLogger.error(error, context);
export const logDebug = (message: string, context?: LogContext) => frontendLogger.debug(message, context);

export default frontendLogger;
