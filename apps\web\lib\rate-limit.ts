import { NextApiRequest, NextApiResponse } from 'next';

interface RateLimitWindow {
  count: number;
  resetTime: number;
}

const WINDOW_SIZE_MS = 15 * 60 * 1000; // 15 minutes
const MAX_REQUESTS = 10;

const ipWindows = new Map<string, RateLimitWindow>();

export function getRateLimitResetTime(ip: string): number {
  const window = ipWindows.get(ip);
  return window ? window.resetTime : 0;
}

export function rateLimit(req: NextApiRequest, res: NextApiResponse): boolean {
  const ip = req.headers['x-forwarded-for'] as string || req.socket.remoteAddress;
  const now = Date.now();

  // Clean up expired windows
  for (const [key, window] of ipWindows.entries()) {
    if (window.resetTime <= now) {
      ipWindows.delete(key);
    }
  }

  let window = ipWindows.get(ip);

  // Create new window if none exists or if current one has expired
  if (!window || window.resetTime <= now) {
    window = {
      count: 0,
      resetTime: now + WINDOW_SIZE_MS
    };
    ipWindows.set(ip, window);
  }

  // Increment count and check limit
  window.count++;

  if (window.count > MAX_REQUESTS) {
    const remainingTime = Math.ceil((window.resetTime - now) / 1000);
    res.setHeader('X-RateLimit-Limit', MAX_REQUESTS);
    res.setHeader('X-RateLimit-Remaining', 0);
    res.setHeader('X-RateLimit-Reset', window.resetTime);
    res.status(429).json({
      message: `Too many login attempts. Please try again in ${remainingTime} seconds.`
    });
    return false;
  }

  // Set rate limit headers
  res.setHeader('X-RateLimit-Limit', MAX_REQUESTS);
  res.setHeader('X-RateLimit-Remaining', MAX_REQUESTS - window.count);
  res.setHeader('X-RateLimit-Reset', window.resetTime);

  return true;
}