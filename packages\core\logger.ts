import pino from 'pino';

/**
 * Centralized logger configuration using Pino
 * 
 * Features:
 * - Environment-controlled log level via LOG_LEVEL
 * - Pretty printing in development
 * - Structured JSON logging in production
 * - Consistent logging across all applications
 */
export const logger = pino({
  level: process.env.LOG_LEVEL || 'info',
  transport: process.env.NODE_ENV === 'development'
    ? { 
        target: 'pino-pretty', 
        options: { 
          translateTime: 'HH:MM:ss',
          colorize: true,
          ignore: 'pid,hostname'
        } 
      }
    : undefined
});

/**
 * Create a child logger with additional context
 * @param context - Additional context to include in all log messages
 */
export const createChildLogger = (context: Record<string, any>) => {
  return logger.child(context);
};

/**
 * Log levels available:
 * - trace (10): Very detailed debug information
 * - debug (20): Debug information
 * - info (30): General information
 * - warn (40): Warning messages
 * - error (50): Error messages
 * - fatal (60): Fatal errors that cause application to exit
 */

export default logger;
