// <PERSON>ript to create the queue directory
const fs = require('fs');
const path = require('path');

// Define the queue directory path
const queueDir = path.join(process.cwd(), 'queue');

console.log(`Creating queue directory at: ${queueDir}`);

// Create the directory if it doesn't exist
try {
  fs.mkdirSync(queueDir, { recursive: true });
  console.log('Queue directory created successfully!');
} catch (error) {
  if (error.code === 'EEXIST') {
    console.log('Queue directory already exists.');
  } else {
    console.error('Error creating queue directory:', error.message);
    process.exit(1);
  }
}

console.log('Done!');
