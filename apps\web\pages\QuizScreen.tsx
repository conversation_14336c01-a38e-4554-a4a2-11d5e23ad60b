import React, { useEffect, useState } from 'react';
import { CheckIcon, XIcon, VolumeIcon } from 'lucide-react';
const quizQuestions = {
  Math: {
    'Numbers 1-10': [{
      type: 'multiple-choice',
      question: 'What number comes after 7?',
      options: ['6', '7', '8', '9'],
      correctAnswer: '8'
    }, {
      type: 'matching',
      question: 'Match the number with the correct number of dots',
      items: [{
        id: 1,
        text: '5',
        match: '•••••'
      }, {
        id: 2,
        text: '3',
        match: '•••'
      }, {
        id: 3,
        text: '1',
        match: '•'
      }]
    }, {
      type: 'fill-blank',
      question: 'Fill in the missing numbers: 1, 2, 3, ___, 5',
      correctAnswer: '4'
    }],
    Addition: [{
      type: 'multiple-choice',
      question: 'What is 2 + 3?',
      options: ['4', '5', '6', '7'],
      correctAnswer: '5'
    }, {
      type: 'multiple-choice',
      question: 'What is 4 + 1?',
      options: ['4', '5', '6', '7'],
      correctAnswer: '5'
    }],
    Subtraction: [{
      type: 'multiple-choice',
      question: 'What is 5 - 2?',
      options: ['2', '3', '4', '5'],
      correctAnswer: '3'
    }, {
      type: 'multiple-choice',
      question: 'What is 7 - 3?',
      options: ['3', '4', '5', '6'],
      correctAnswer: '4'
    }]
  },
  Science: {
    Plants: [{
      type: 'multiple-choice',
      question: 'What do plants need to grow?',
      options: ['Only water', 'Only sunlight', 'Water and sunlight', 'Only soil'],
      correctAnswer: 'Water and sunlight'
    }, {
      type: 'multiple-choice',
      question: 'Which part of the plant absorbs water?',
      options: ['Leaves', 'Stem', 'Roots', 'Flowers'],
      correctAnswer: 'Roots'
    }],
    Animals: [{
      type: 'multiple-choice',
      question: 'Which animal can fly?',
      options: ['Fish', 'Bird', 'Cat', 'Dog'],
      correctAnswer: 'Bird'
    }, {
      type: 'multiple-choice',
      question: 'Which animal lives in water?',
      options: ['Bird', 'Fish', 'Dog', 'Rabbit'],
      correctAnswer: 'Fish'
    }]
  },
  Chinese: {
    'Basic Characters': [{
      type: 'multiple-choice',
      question: 'What does 人 mean?',
      options: ['Person', 'Tree', 'Water', 'Mountain'],
      correctAnswer: 'Person'
    }, {
      type: 'multiple-choice',
      question: 'What does 山 mean?',
      options: ['River', 'Mountain', 'Tree', 'Sky'],
      correctAnswer: 'Mountain'
    }]
  }
};
interface QuizScreenProps {
  unit: {
    id: number;
    name: string;
    subject: string;
  };
  difficulty: string;
  onComplete: (earnedXp: number, earnedGems: number) => void;
  hearts: number;
  setHearts: (hearts: number) => void;
}
export const QuizScreen: React.FC<QuizScreenProps> = ({
  unit,
  difficulty,
  onComplete,
  hearts,
  setHearts
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState('');
  const [isCorrect, setIsCorrect] = useState<boolean | null>(null);
  const [progress, setProgress] = useState(0);
  const questions = quizQuestions[unit.subject]?.[unit.name] || [];
  const currentQuestion = questions[currentQuestionIndex];
  useEffect(() => {
    setProgress(currentQuestionIndex / questions.length * 100);
  }, [currentQuestionIndex, questions.length]);
  const handleAnswer = (answer: string) => {
    setSelectedAnswer(answer);
    const correct = answer === currentQuestion.correctAnswer;
    setIsCorrect(correct);
    if (!correct) {
      setHearts(hearts - 1);
    }
    
    // Only auto-advance for incorrect answers
    if (!correct) {
      setTimeout(() => {
        if (currentQuestionIndex < questions.length - 1) {
          setCurrentQuestionIndex(currentQuestionIndex + 1);
          setSelectedAnswer('');
          setIsCorrect(null);
        } else {
          // Quiz completed
          onComplete(10, 5);
        }
      }, 1500);
    }
  };
  if (hearts <= 0) {
    return <div className="flex-1 flex flex-col items-center justify-center p-4 bg-white">
        <div className="text-6xl mb-4">💔</div>
        <h2 className="text-2xl font-bold mb-2">Out of Hearts!</h2>
        <p className="text-center mb-6">
          You've run out of hearts. Practice makes perfect! Try again later.
        </p>
        <button onClick={() => onComplete(0, 0)} className="px-6 py-3 bg-[#58cc02] text-white font-bold rounded-lg">
          Back to Home
        </button>
      </div>;
  }
  return <div className="flex-1 flex flex-col p-4 bg-white">
      <div className="flex justify-between items-center mb-4">
        <div className="text-sm font-medium text-gray-600">
          Difficulty: {difficulty}
        </div>
        <div className="bg-[#04B2D9]/10 px-3 py-1 rounded-full text-sm font-medium text-[#0F5FA6]">
          {unit.subject} - {unit.name}
        </div>
      </div>
      {/* Update progress bar color */}
      <div className="mb-4">
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div className="bg-[#0F5FA6] h-2.5 rounded-full transition-all duration-300" style={{
          width: `${progress}%`
        }}></div>
        </div>
      </div>
      {/* Question */}
      <div className="flex-1">
        <h2 className="text-xl font-bold mb-6">{currentQuestion.question}</h2>
        {currentQuestion.type === 'multiple-choice' && <div className="space-y-3">
            {currentQuestion.options.map((option, index) => <button key={index} onClick={() => handleAnswer(option)} disabled={isCorrect !== null} className={`w-full p-4 rounded-lg border-2 text-left ${selectedAnswer === option ? isCorrect ? 'border-green-500 bg-green-100' : 'border-red-500 bg-red-100' : 'border-gray-300 hover:border-gray-400'}`}>
                {option}
                {selectedAnswer === option && <span className="float-right">
                    {isCorrect ? <CheckIcon size={24} className="text-green-500" /> : <XIcon size={24} className="text-red-500" />}
                  </span>}
              </button>)}
          </div>}
        {currentQuestion.type === 'fill-blank' && <div>
            <input type="text" placeholder="Type your answer" className="w-full p-4 border-2 border-gray-300 rounded-lg mb-4" value={selectedAnswer} onChange={e => setSelectedAnswer(e.target.value)} disabled={isCorrect !== null} />
            <button onClick={() => handleAnswer(selectedAnswer)} disabled={isCorrect !== null || !selectedAnswer} className="w-full p-4 bg-[#04B2D9] text-white font-bold rounded-lg disabled:bg-gray-300">
              Check
            </button>
          </div>}
      </div>
      {/* Feedback */}
      {isCorrect !== null && <div className={`p-4 mb-4 rounded-lg ${isCorrect ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          <div className="flex items-center font-bold">
            {isCorrect ? <>
                <CheckIcon size={24} className="mr-2" />
                Correct!
              </> : <>
                <XIcon size={24} className="mr-2" />
                Incorrect. The correct answer is {currentQuestion.correctAnswer}
                .
              </>}
          </div>
        </div>}
      {/* Continue button */}
      {isCorrect !== null && <button onClick={() => {
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        setSelectedAnswer('');
        setIsCorrect(null);
      } else {
        onComplete(10, 5);
      }
    }} className="w-full p-4 bg-[#0F5FA6] text-white font-bold rounded-lg hover:bg-[#0A8CBF] transition-colors">
          Continue
        </button>}
    </div>;
};
