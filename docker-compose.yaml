version: '3.8'

services:
  db:
    image: postgres:16   
    container_name: quiz-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "5433:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5

  backup:
    image: postgres:16
    depends_on:
      - db
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - pgdata:/var/lib/postgresql/data:ro
      - ./backups:/backups
    entrypoint: >
      bash -c "
        pg_dump -h db -U ${POSTGRES_USER} ${POSTGRES_DB} \
          > /backups/${POSTGRES_DB}-$(date +%F_%H%M%S).sql
      "

volumes:
  pgdata:
