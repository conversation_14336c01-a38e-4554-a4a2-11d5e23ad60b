import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import formidable from 'formidable';
import fs from 'fs/promises';
import { storage } from '../../../services/storage';
import prisma, { NoteType } from '../../../lib/prisma';

// Disable the default body parser to handle multipart/form-data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check authentication and admin role
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (session.user?.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Forbidden - Admin access required' });
    }

    // Parse the multipart form data
    const { fields, files } = await parseForm(req);

    // Validate required fields
    const yearId = parseInt(fields.yearId?.[0] || '');
    const subjectId = parseInt(fields.subjectId?.[0] || '');
    const unitId = parseInt(fields.unitId?.[0] || '');
    const uploadMethod = fields.uploadMethod?.[0] || 'file';
    const kind = fields.kind?.[0] || '';

    if (isNaN(yearId) || isNaN(subjectId) || isNaN(unitId)) {
      return res.status(400).json({ message: 'Missing or invalid yearId, subjectId, or unitId' });
    }

    // Validate kind field
    if (kind && !['FILE', 'MARKDOWN', 'SAMPLE'].includes(kind)) {
      return res.status(400).json({ message: 'Invalid kind value. Must be FILE, MARKDOWN, or SAMPLE' });
    }

    let fileUrl = '';
    let filename = '';
    let mimeType = '';
    let fileSize = 0;
    let contentType: NoteType = 'OTHER';

    if (uploadMethod === 'file') {
      // Handle file upload
      const file = files.file?.[0];
      if (!file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      // Read the file
      const fileBuffer = await fs.readFile(file.filepath);

      // Generate a unique key for the file
      const key = `${Date.now()}-${file.originalFilename}`;

      // Save the file to storage
      fileUrl = await storage.save(fileBuffer, key, file.mimetype || 'application/octet-stream');
      filename = file.originalFilename || 'unnamed-file';
      mimeType = file.mimetype || 'application/octet-stream';
      fileSize = file.size || 0;

      // Determine content type based on MIME type and kind
      if (kind === 'SAMPLE') {
        contentType = 'SAMPLE';
      } else if (mimeType.startsWith('image/')) {
        contentType = 'IMAGE';
      } else if (mimeType === 'application/pdf') {
        contentType = 'PDF';
      } else if (mimeType === 'text/plain' || mimeType === 'text/html') {
        contentType = 'TEXT';
      } else {
        contentType = 'OTHER';
      }

      // Clean up the temporary file
      await fs.unlink(file.filepath);
    } else if (uploadMethod === 'markdown') {
      // Handle markdown text
      const markdownText = fields.markdownText?.[0];
      if (!markdownText) {
        return res.status(400).json({ message: 'No markdown content provided' });
      }

      // Convert markdown text to buffer
      const markdownBuffer = Buffer.from(markdownText, 'utf-8');

      // Generate a unique key for the markdown file
      const timestamp = Date.now();
      const key = `${timestamp}-markdown-note.md`;

      // Save the markdown content to storage
      fileUrl = await storage.save(markdownBuffer, key, 'text/markdown');
      filename = `note-${timestamp}.md`;
      mimeType = 'text/markdown';
      fileSize = markdownBuffer.length;
      contentType = kind === 'SAMPLE' ? 'SAMPLE' : 'MARKDOWN';
    } else {
      return res.status(400).json({ message: 'Invalid upload method' });
    }

    // Create a database record for the note
    const note = await prisma.note.create({
      data: {
        filename,
        fileUrl,
        mimeType,
        fileSize,
        yearId,
        subjectId,
        unitId,
        contentType,
      },
    });

    // Return success response
    return res.status(200).json({
      ok: true,
      note,
    });
  } catch (error) {
    console.error('Error uploading note:', error);
    return res.status(500).json({ message: 'Error uploading note' });
  }
}

/**
 * Parse the multipart form data
 */
function parseForm(req: NextApiRequest): Promise<formidable.FormidableResult> {
  return new Promise((resolve, reject) => {
    const form = formidable({
      multiples: true,
      keepExtensions: true,
    });

    form.parse(req, (err, fields, files) => {
      if (err) {
        reject(err);
      } else {
        resolve({ fields, files });
      }
    });
  });
}
