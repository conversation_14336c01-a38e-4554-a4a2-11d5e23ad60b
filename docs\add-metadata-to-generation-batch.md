# Adding Metadata Field to GenerationBatch

This document provides step-by-step instructions for adding the `metadata` field to the `GenerationBatch` model to support storing `tpDistribution` data.

## Background

The question generation API attempts to store `tpDistribution` in a `metadata` field in the `GenerationBatch` model, but this field doesn't exist in the current database schema, causing the following error:

```
Unknown argument `metadata`. Available options are marked with ?.
```

## Temporary Fix (Already Applied)

A temporary fix has been applied to the API endpoint (`pages/api/admin/generate-questions.ts`) to remove the metadata field from the create operation. The `tpDistribution` is still stored in the queue file, which the worker script already knows how to read.

## Permanent Solution

To properly add the `metadata` field to your database, follow these steps:

### Step 1: Update the Prisma Schema

The Prisma schema has already been updated to include the `metadata` field in the `GenerationBatch` model:

```prisma
model GenerationBatch {
  // ... existing fields
  metadata          Json?         // Store additional metadata like tpDistribution
  // ... other fields
}
```

### Step 2: Create a Migration

There are two approaches to apply this change to your database:

#### Option A: Using Prisma Migrate (Recommended for Development)

1. Create a migration:
   ```bash
   npx prisma migrate dev --name add_metadata_to_generation_batch
   ```

2. This command will:
   - Create a new migration file
   - Apply the migration to your development database
   - Generate an updated Prisma client

#### Option B: Using Prisma Push (Quick Solution)

If you're in a development environment and don't need to track migrations:

1. Push the schema changes directly to the database:
   ```bash
   npx prisma db push
   ```

2. Generate the updated Prisma client:
   ```bash
   npx prisma generate
   ```

### Step 3: Restart Your Development Server

After applying the migration and generating the updated client, restart your development server:

```bash
npm run dev
```

### Step 4: Restore the Original Code

Once the migration has been applied and the server restarted, you can restore the original code in the API endpoint to use the metadata field:

```typescript
// In pages/api/admin/generate-questions.ts
notes: {
  connect: notes.map(note => ({ id: note.id })),
},
// Store tpDistribution in metadata if provided
metadata: tpDistribution ? { tpDistribution } : undefined
```

## Troubleshooting

If you encounter issues:

1. **Verify the migration was applied**: Check your database to confirm the `metadata` column exists in the `TG_GenerationBatch` table.

2. **Check the Prisma client**: Make sure the Prisma client was regenerated after the schema change:
   ```bash
   npx prisma generate
   ```

3. **Clear Node.js module cache**: Sometimes you need to clear the Node.js module cache:
   ```bash
   rm -rf node_modules/.prisma
   npm install
   ```

4. **Restart your development server**: Always restart your server after schema changes:
   ```bash
   npm run dev
   ```

## Production Deployment

For production environments, you should:

1. Create a proper migration using `prisma migrate dev`
2. Test the migration in a staging environment
3. Apply the migration to production using `prisma migrate deploy`

## Additional Notes

- The `metadata` field is of type `Json?` (optional JSON), which can store complex data structures like arrays and objects.
- The worker script already knows how to read `tpDistribution` from the queue file, so it will continue to work even without the metadata field.
- In the future, you can use the metadata field to store other configuration data for question generation.
