import { prisma } from '@quiz/db';
import { hashPassword, hashPin } from '../apps/web/lib/auth';

async function createTestStudent() {
  try {
    console.log('Creating test student account...');

    // Check if test account already exists
    const existingAccount = await prisma.account.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingAccount) {
      console.log('Test account already exists. Checking for test student...');

      const existingChild = await prisma.child.findUnique({
        where: { username: 'teststudent' }
      });

      if (existingChild) {
        console.log('Test student already exists.');
        console.log('Login credentials:');
        console.log('Username: teststudent');
        console.log('PIN: 1234');
        return;
      }
    }

    let parentAccount;

    if (!existingAccount) {
      // Create parent account first
      console.log('Creating parent account...');
      const { hash: passwordHash, salt } = await hashPassword('password123');

      parentAccount = await prisma.account.create({
        data: {
          email: '<EMAIL>',
          name: 'Test Parent',
          password_hash: passwordHash,
          salt: salt,
          role: 'PARENT',
          status: 'ACTIVE',
        }
      });
      console.log('Parent account created.');
    } else {
      parentAccount = existingAccount;
    }

    // Create test student (child)
    console.log('Creating test student...');
    const { hash: pinHash } = await hashPin('1234', parentAccount.salt);

    const testStudent = await prisma.child.create({
      data: {
        name: 'Test Student',
        username: 'teststudent',
        pin_hash: pinHash,
        salt: parentAccount.salt,
        year: 'Year 5',
        accountId: parentAccount.id,
        menuLanguage: 'EN',
        quizLanguage: 'EN',
        showDualLanguage: false,
      }
    });

    console.log('Test student created successfully!');
    console.log('');
    console.log('=== LOGIN CREDENTIALS ===');
    console.log('Username: teststudent');
    console.log('PIN: 1234');
    console.log('========================');
    console.log('');
    console.log('You can now log in at http://localhost:3000/login');

  } catch (error) {
    console.error('Error creating test student:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestStudent()
  .then(() => console.log('Test student creation completed.'))
  .catch(error => console.error('Error in test student creation:', error));
