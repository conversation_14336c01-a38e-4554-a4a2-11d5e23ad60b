import React from 'react';
import { XIcon } from 'lucide-react';
interface StreakModalProps {
  streak: number;
  onClose: () => void;
}
export const StreakModal: React.FC<StreakModalProps> = ({
  streak,
  onClose
}) => {
  const milestones = [{
    days: 7,
    label: '1 Week',
    status: streak >= 7 ? 'reached' : 'upcoming'
  }, {
    days: 14,
    label: '2 Weeks',
    status: streak >= 14 ? 'reached' : 'upcoming'
  }, {
    days: 30,
    label: '1 Month',
    status: streak >= 30 ? 'reached' : 'upcoming'
  }, {
    days: 100,
    label: '100 Days',
    status: streak >= 100 ? 'reached' : 'upcoming'
  }];
  return <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full p-6 shadow-xl">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold flex items-center">
            <div size={28} className="text-[#0F5FA6] mr-2" />
            Your Streak
          </h2>
          <button onClick={onClose} className="p-1">
            <XIcon size={24} />
          </button>
        </div>
        <div className="text-center mb-6">
          <div className="text-6xl font-bold text-[#0F5FA6] mb-2">{streak}</div>
          <div className="text-gray-600">
            {streak === 1 ? 'day' : 'days'} in a row
          </div>
        </div>
        <div className="space-y-4 mb-6">
          {milestones.map(milestone => <div key={milestone.days} className={`flex justify-between items-center p-4 rounded-lg ${milestone.status === 'reached' ? 'bg-[#04B2D9]/10' : 'bg-gray-100'}`}>
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${milestone.status === 'reached' ? 'bg-[#0F5FA6] text-white' : 'bg-gray-300'}`}>
                  {milestone.status === 'reached' ? '✓' : ''}
                </div>
                <span className="font-medium">{milestone.label}</span>
              </div>
              <div className="font-bold">{milestone.days} days</div>
            </div>)}
        </div>
        <div className="text-center text-gray-600 text-sm">
          Keep learning daily to build your streak!
        </div>
      </div>
    </div>;
};