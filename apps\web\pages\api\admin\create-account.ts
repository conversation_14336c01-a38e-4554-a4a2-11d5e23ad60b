import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { Prisma } from '@prisma/client';
import { hashPassword, hashPin } from '../../../lib/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { name, children, license } = req.body;

    // Validate request body
    if (!name || !req.body.email || !req.body.password) {
      return res.status(400).json({ message: 'Invalid account details' });
    }

    if (!Array.isArray(children) || children.length === 0) {
      return res.status(400).json({ message: 'At least one child is required' });
    }

    for (const child of children) {
      if (!child.name || !child.year || !child.username || !child.pin) {
        return res.status(400).json({ message: 'Invalid child details' });
      }
    }

    // Hash the account password
    const { hash: passwordHash, salt } = await hashPassword(req.body.password);

    // Create account with children
    const createdAccount = await prisma.account.create({
      data: {
        name,
        email: req.body.email,
        password_hash: passwordHash,
        salt: salt,
        role: 'PARENT',
        status: 'ACTIVE',
        children: {
          create: await Promise.all(children.map(async child => {
            // Hash each child's PIN using the parent's salt
            const { hash: pinHash } = await hashPin(child.pin, salt);
            return {
              name: child.name,
              year: child.year,
              username: child.username,
              pin_hash: pinHash,
              salt: salt // Use same salt as parent for easier management
            };
          }))
        },
        license: license ? {
          create: {
            type: license.type,
            duration: license.duration,
          },
        } : undefined,
      },
    });

    res.status(201).json({ message: 'Account created successfully' });
  } catch (error) {
    console.error('Error creating account:', error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        const target = error.meta?.target as string[];
        if (target?.includes('username')) {
          return res.status(409).json({ message: 'Username already exists. Please choose a different one.' });
        }
        if (target?.includes('email')) {
          return res.status(409).json({ message: 'Email already exists for another account.' });
        }
      }
    }

    res.status(500).json({ message: 'Failed to create account. Please try again.' });
  }
}