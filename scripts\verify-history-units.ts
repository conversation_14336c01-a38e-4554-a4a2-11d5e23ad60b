import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Verifying Year 5 History units...');

    // Find History subject record
    const historySubject = await prisma.subject.findUnique({
      where: { name: 'History' },
      select: { id: true }
    });

    if (!historySubject) {
      console.error('History subject not found');
      return;
    }

    // Find Year 5 record
    const year5 = await prisma.year.findUnique({
      where: { yearNumber: 5 },
      select: { id: true }
    });

    if (!year5) {
      console.error('Year 5 not found');
      return;
    }

    // Query the units
    const units = await prisma.unit.findMany({
      where: {
        subjectId: historySubject.id,
        yearId: year5.id
      },
      orderBy: {
        unitNumber: 'asc'
      }
    });

    console.log(`Found ${units.length} units for Year 5 History:`);
    
    // Display the units in a table format
    console.log('Unit\tChinese Topic\tMalay Topic\tEnglish Topic');
    console.log('----\t------------\t-----------\t-------------');
    
    units.forEach(unit => {
      console.log(`${unit.unitNumber}\t${unit.topicZh}\t${unit.topicMs || 'N/A'}\t${unit.topicEn}`);
    });
  } catch (error) {
    console.error('Error verifying History units:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error('Fatal error:', e);
    process.exit(1);
  });
