import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import prisma from '../../lib/prisma';
import { QuizMode } from '@prisma/client';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { mode } = req.query;

    // Mode is required
    if (!mode || typeof mode !== 'string') {
      return res.status(400).json({ message: 'Quiz mode is required' });
    }

    // Fetch the config for the specified mode
    const quizConfig = await prisma.quizConfig.findUnique({
      where: { mode: mode as QuizMode },
    });

    if (!quizConfig) {
      return res.status(404).json({ message: `Quiz config for mode ${mode} not found` });
    }

    return res.status(200).json(quizConfig);
  } catch (error) {
    console.error('Error fetching quiz config:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
