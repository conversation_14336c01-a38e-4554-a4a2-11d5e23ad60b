import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateMathTopicsMs() {
  try {
    console.log('Starting to update Mathematics topics with Malay translations...');
    
    const mathematicsSubject = await prisma.subject.findUnique({
      where: { name: 'Mathematics' },
    });
    
    if (!mathematicsSubject) {
      console.error('Mathematics subject not found');
      return;
    }
    
    const topicMsMap = [
      { unitNumber: 1, topicMs: 'Nombor Bulat dan <PERSON>' },
      { unitNumber: 2, topicMs: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> dan <PERSON>' },
      { unitNumber: 3, topicMs: 'Wang' },
      { unitNumber: 4, topicMs: '<PERSON><PERSON> dan <PERSON>' },
      { unitNumber: 5, topicMs: '<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> dan <PERSON>' },
      { unitNumber: 6, topicMs: 'Ruang' },
      { unitNumber: 7, topicMs: 'Koordinat, Nisbah dan <PERSON>' },
      { unitNumber: 8, topicMs: 'Pengurusan Data' },
    ];
    
    for (const { unitNumber, topicMs } of topicMsMap) {
      await prisma.unit.updateMany({
        where: {
          unitNumber,
          subjectId: mathematicsSubject.id,
        },
        data: {
          topicMs,
        },
      });
      console.log(`Updated Unit ${unitNumber} with Malay topic: ${topicMs}`);
    }
    
    console.log('Update completed successfully!');
  } catch (error) {
    console.error('Error updating Mathematics topics:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateMathTopicsMs()
  .catch(e => {
    console.error(e);
    process.exit(1);
  });