{"D:\\projects\\GitHub\\my-quiz-app\\lib\\api.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\api.ts", "statementMap": {"0": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 7}}, "1": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 7}}, "2": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 7}}, "3": {"start": {"line": 46, "column": 2}, "end": {"line": 55, "column": 3}}, "4": {"start": {"line": 47, "column": 21}, "end": {"line": 47, "column": 49}}, "5": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}, "6": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 73}}, "7": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 33}}, "8": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 54}}, "9": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 14}}, "10": {"start": {"line": 64, "column": 2}, "end": {"line": 73, "column": 3}}, "11": {"start": {"line": 65, "column": 21}, "end": {"line": 65, "column": 70}}, "12": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "13": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 71}}, "14": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 33}}, "15": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 77}}, "16": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 14}}, "17": {"start": {"line": 82, "column": 2}, "end": {"line": 100, "column": 3}}, "18": {"start": {"line": 83, "column": 21}, "end": {"line": 90, "column": 6}}, "19": {"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": 5}}, "20": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 77}}, "21": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": 33}}, "22": {"start": {"line": 98, "column": 4}, "end": {"line": 98, "column": 59}}, "23": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 16}}}, "fnMap": {"0": {"name": "getSubjects", "decl": {"start": {"line": 45, "column": 22}, "end": {"line": 45, "column": 33}}, "loc": {"start": {"line": 45, "column": 33}, "end": {"line": 56, "column": 1}}}, "1": {"name": "getTopics", "decl": {"start": {"line": 63, "column": 22}, "end": {"line": 63, "column": 31}}, "loc": {"start": {"line": 63, "column": 49}, "end": {"line": 74, "column": 1}}}, "2": {"name": "createQuizAttempt", "decl": {"start": {"line": 81, "column": 22}, "end": {"line": 81, "column": 39}}, "loc": {"start": {"line": 81, "column": 73}, "end": {"line": 101, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}, "type": "if", "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 5}}]}, "1": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 5}}]}, "2": {"loc": {"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": 5}}, "type": "if", "locations": [{"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0], "1": [0], "2": [0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\auth.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\auth.ts", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 7}}, "1": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 7}}, "2": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 7}}, "3": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "4": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 28}}, "5": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 56}}, "6": {"start": {"line": 6, "column": 15}, "end": {"line": 11, "column": 4}}, "7": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 24}}, "8": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 52}}, "9": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 67}}, "10": {"start": {"line": 22, "column": 15}, "end": {"line": 27, "column": 4}}, "11": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 33}}}, "fnMap": {"0": {"name": "hashPassword", "decl": {"start": {"line": 4, "column": 22}, "end": {"line": 4, "column": 34}}, "loc": {"start": {"line": 4, "column": 51}, "end": {"line": 13, "column": 1}}}, "1": {"name": "verifyPassword", "decl": {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 36}}, "loc": {"start": {"line": 15, "column": 81}, "end": {"line": 17, "column": 1}}}, "2": {"name": "hashPin", "decl": {"start": {"line": 19, "column": 22}, "end": {"line": 19, "column": 29}}, "loc": {"start": {"line": 19, "column": 56}, "end": {"line": 29, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 22}}, {"start": {"line": 21, "column": 26}, "end": {"line": 21, "column": 67}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {"0": [0, 0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\grading.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\grading.ts", "statementMap": {"0": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 16}}, "1": {"start": {"line": 12, "column": 20}, "end": {"line": 12, "column": 33}}, "2": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 54}}, "3": {"start": {"line": 16, "column": 2}, "end": {"line": 30, "column": 5}}, "4": {"start": {"line": 33, "column": 2}, "end": {"line": 36, "column": 3}}, "5": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 54}}, "6": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 17}}, "7": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 60}}, "8": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 23}}, "9": {"start": {"line": 44, "column": 27}, "end": {"line": 44, "column": 47}}, "10": {"start": {"line": 47, "column": 2}, "end": {"line": 51, "column": 5}}, "11": {"start": {"line": 53, "column": 2}, "end": {"line": 234, "column": 3}}, "12": {"start": {"line": 56, "column": 25}, "end": {"line": 56, "column": 54}}, "13": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 47}}, "14": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 87}}, "15": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 23}}, "16": {"start": {"line": 63, "column": 6}, "end": {"line": 66, "column": 7}}, "17": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 56}}, "18": {"start": {"line": 65, "column": 8}, "end": {"line": 65, "column": 21}}, "19": {"start": {"line": 68, "column": 6}, "end": {"line": 95, "column": 7}}, "20": {"start": {"line": 69, "column": 29}, "end": {"line": 69, "column": 61}}, "21": {"start": {"line": 72, "column": 28}, "end": {"line": 72, "column": 58}}, "22": {"start": {"line": 73, "column": 8}, "end": {"line": 79, "column": 9}}, "23": {"start": {"line": 74, "column": 10}, "end": {"line": 78, "column": 11}}, "24": {"start": {"line": 75, "column": 12}, "end": {"line": 75, "column": 73}}, "25": {"start": {"line": 77, "column": 12}, "end": {"line": 77, "column": 71}}, "26": {"start": {"line": 81, "column": 8}, "end": {"line": 84, "column": 9}}, "27": {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 69}}, "28": {"start": {"line": 83, "column": 10}, "end": {"line": 83, "column": 23}}, "29": {"start": {"line": 86, "column": 30}, "end": {"line": 86, "column": 55}}, "30": {"start": {"line": 89, "column": 8}, "end": {"line": 89, "column": 83}}, "31": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 122}}, "32": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 25}}, "33": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 67}}, "34": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": 21}}, "35": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 87}}, "36": {"start": {"line": 102, "column": 28}, "end": {"line": 102, "column": 64}}, "37": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 89}}, "38": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 88}}, "39": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 23}}, "40": {"start": {"line": 112, "column": 25}, "end": {"line": 112, "column": 54}}, "41": {"start": {"line": 113, "column": 6}, "end": {"line": 113, "column": 47}}, "42": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 84}}, "43": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 23}}, "44": {"start": {"line": 120, "column": 6}, "end": {"line": 146, "column": 7}}, "45": {"start": {"line": 122, "column": 8}, "end": {"line": 136, "column": 9}}, "46": {"start": {"line": 123, "column": 36}, "end": {"line": 123, "column": 80}}, "47": {"start": {"line": 125, "column": 32}, "end": {"line": 125, "column": 68}}, "48": {"start": {"line": 128, "column": 10}, "end": {"line": 130, "column": 12}}, "49": {"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 82}}, "50": {"start": {"line": 131, "column": 10}, "end": {"line": 131, "column": 126}}, "51": {"start": {"line": 132, "column": 10}, "end": {"line": 132, "column": 27}}, "52": {"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": 74}}, "53": {"start": {"line": 135, "column": 10}, "end": {"line": 135, "column": 23}}, "54": {"start": {"line": 139, "column": 27}, "end": {"line": 139, "column": 89}}, "55": {"start": {"line": 141, "column": 30}, "end": {"line": 141, "column": 66}}, "56": {"start": {"line": 143, "column": 8}, "end": {"line": 143, "column": 91}}, "57": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": 104}}, "58": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 25}}, "59": {"start": {"line": 150, "column": 6}, "end": {"line": 153, "column": 7}}, "60": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": 52}}, "61": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 21}}, "62": {"start": {"line": 155, "column": 6}, "end": {"line": 183, "column": 7}}, "63": {"start": {"line": 157, "column": 30}, "end": {"line": 157, "column": 56}}, "64": {"start": {"line": 160, "column": 28}, "end": {"line": 160, "column": 58}}, "65": {"start": {"line": 161, "column": 8}, "end": {"line": 167, "column": 9}}, "66": {"start": {"line": 162, "column": 10}, "end": {"line": 166, "column": 11}}, "67": {"start": {"line": 163, "column": 12}, "end": {"line": 163, "column": 73}}, "68": {"start": {"line": 165, "column": 12}, "end": {"line": 165, "column": 71}}, "69": {"start": {"line": 169, "column": 8}, "end": {"line": 172, "column": 9}}, "70": {"start": {"line": 170, "column": 10}, "end": {"line": 170, "column": 66}}, "71": {"start": {"line": 171, "column": 10}, "end": {"line": 171, "column": 23}}, "72": {"start": {"line": 174, "column": 31}, "end": {"line": 174, "column": 50}}, "73": {"start": {"line": 177, "column": 8}, "end": {"line": 177, "column": 85}}, "74": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": 120}}, "75": {"start": {"line": 179, "column": 8}, "end": {"line": 179, "column": 25}}, "76": {"start": {"line": 181, "column": 8}, "end": {"line": 181, "column": 63}}, "77": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": 21}}, "78": {"start": {"line": 187, "column": 6}, "end": {"line": 190, "column": 7}}, "79": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": 54}}, "80": {"start": {"line": 189, "column": 8}, "end": {"line": 189, "column": 21}}, "81": {"start": {"line": 192, "column": 6}, "end": {"line": 220, "column": 7}}, "82": {"start": {"line": 194, "column": 30}, "end": {"line": 194, "column": 56}}, "83": {"start": {"line": 197, "column": 28}, "end": {"line": 197, "column": 58}}, "84": {"start": {"line": 198, "column": 8}, "end": {"line": 204, "column": 9}}, "85": {"start": {"line": 199, "column": 10}, "end": {"line": 203, "column": 11}}, "86": {"start": {"line": 200, "column": 12}, "end": {"line": 200, "column": 73}}, "87": {"start": {"line": 202, "column": 12}, "end": {"line": 202, "column": 71}}, "88": {"start": {"line": 206, "column": 8}, "end": {"line": 209, "column": 9}}, "89": {"start": {"line": 207, "column": 10}, "end": {"line": 207, "column": 68}}, "90": {"start": {"line": 208, "column": 10}, "end": {"line": 208, "column": 23}}, "91": {"start": {"line": 211, "column": 31}, "end": {"line": 211, "column": 50}}, "92": {"start": {"line": 214, "column": 8}, "end": {"line": 214, "column": 85}}, "93": {"start": {"line": 215, "column": 8}, "end": {"line": 215, "column": 122}}, "94": {"start": {"line": 216, "column": 8}, "end": {"line": 216, "column": 25}}, "95": {"start": {"line": 218, "column": 8}, "end": {"line": 218, "column": 65}}, "96": {"start": {"line": 219, "column": 8}, "end": {"line": 219, "column": 21}}, "97": {"start": {"line": 226, "column": 6}, "end": {"line": 226, "column": 69}}, "98": {"start": {"line": 227, "column": 6}, "end": {"line": 227, "column": 19}}, "99": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 57}}, "100": {"start": {"line": 232, "column": 6}, "end": {"line": 232, "column": 19}}}, "fnMap": {"0": {"name": "grade", "decl": {"start": {"line": 9, "column": 16}, "end": {"line": 9, "column": 21}}, "loc": {"start": {"line": 9, "column": 66}, "end": {"line": 235, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 128, "column": 45}, "end": {"line": 128, "column": 46}}, "loc": {"start": {"line": 129, "column": 12}, "end": {"line": 129, "column": 82}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 35}}, {"start": {"line": 13, "column": 39}, "end": {"line": 13, "column": 54}}]}, "1": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 36, "column": 3}}, "type": "if", "locations": [{"start": {"line": 33, "column": 2}, "end": {"line": 36, "column": 3}}]}, "2": {"loc": {"start": {"line": 53, "column": 2}, "end": {"line": 234, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 54, "column": 4}, "end": {"line": 60, "column": 5}}, {"start": {"line": 62, "column": 4}, "end": {"line": 96, "column": 5}}, {"start": {"line": 98, "column": 4}, "end": {"line": 108, "column": 5}}, {"start": {"line": 110, "column": 4}, "end": {"line": 116, "column": 5}}, {"start": {"line": 118, "column": 4}, "end": {"line": 147, "column": 5}}, {"start": {"line": 149, "column": 4}, "end": {"line": 184, "column": 5}}, {"start": {"line": 186, "column": 4}, "end": {"line": 221, "column": 5}}, {"start": {"line": 223, "column": 4}, "end": {"line": 228, "column": 5}}, {"start": {"line": 230, "column": 4}, "end": {"line": 233, "column": 5}}]}, "3": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 66, "column": 7}}, "type": "if", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 66, "column": 7}}]}, "4": {"loc": {"start": {"line": 73, "column": 8}, "end": {"line": 79, "column": 9}}, "type": "if", "locations": [{"start": {"line": 73, "column": 8}, "end": {"line": 79, "column": 9}}]}, "5": {"loc": {"start": {"line": 73, "column": 12}, "end": {"line": 73, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 73, "column": 12}, "end": {"line": 73, "column": 26}}, {"start": {"line": 73, "column": 30}, "end": {"line": 73, "column": 62}}]}, "6": {"loc": {"start": {"line": 81, "column": 8}, "end": {"line": 84, "column": 9}}, "type": "if", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 84, "column": 9}}]}, "7": {"loc": {"start": {"line": 81, "column": 12}, "end": {"line": 81, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 12}, "end": {"line": 81, "column": 26}}, {"start": {"line": 81, "column": 30}, "end": {"line": 81, "column": 49}}]}, "8": {"loc": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 87}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 41}}, {"start": {"line": 100, "column": 45}, "end": {"line": 100, "column": 61}}, {"start": {"line": 100, "column": 65}, "end": {"line": 100, "column": 81}}, {"start": {"line": 100, "column": 85}, "end": {"line": 100, "column": 87}}]}, "9": {"loc": {"start": {"line": 102, "column": 28}, "end": {"line": 102, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 102, "column": 28}, "end": {"line": 102, "column": 58}}, {"start": {"line": 102, "column": 62}, "end": {"line": 102, "column": 64}}]}, "10": {"loc": {"start": {"line": 120, "column": 6}, "end": {"line": 146, "column": 7}}, "type": "if", "locations": [{"start": {"line": 120, "column": 6}, "end": {"line": 146, "column": 7}}, {"start": {"line": 137, "column": 13}, "end": {"line": 146, "column": 7}}]}, "11": {"loc": {"start": {"line": 123, "column": 36}, "end": {"line": 123, "column": 80}}, "type": "binary-expr", "locations": [{"start": {"line": 123, "column": 36}, "end": {"line": 123, "column": 74}}, {"start": {"line": 123, "column": 78}, "end": {"line": 123, "column": 80}}]}, "12": {"loc": {"start": {"line": 125, "column": 32}, "end": {"line": 125, "column": 68}}, "type": "binary-expr", "locations": [{"start": {"line": 125, "column": 32}, "end": {"line": 125, "column": 62}}, {"start": {"line": 125, "column": 66}, "end": {"line": 125, "column": 68}}]}, "13": {"loc": {"start": {"line": 139, "column": 27}, "end": {"line": 139, "column": 89}}, "type": "binary-expr", "locations": [{"start": {"line": 139, "column": 27}, "end": {"line": 139, "column": 43}}, {"start": {"line": 139, "column": 47}, "end": {"line": 139, "column": 63}}, {"start": {"line": 139, "column": 67}, "end": {"line": 139, "column": 83}}, {"start": {"line": 139, "column": 87}, "end": {"line": 139, "column": 89}}]}, "14": {"loc": {"start": {"line": 141, "column": 30}, "end": {"line": 141, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 141, "column": 30}, "end": {"line": 141, "column": 60}}, {"start": {"line": 141, "column": 64}, "end": {"line": 141, "column": 66}}]}, "15": {"loc": {"start": {"line": 150, "column": 6}, "end": {"line": 153, "column": 7}}, "type": "if", "locations": [{"start": {"line": 150, "column": 6}, "end": {"line": 153, "column": 7}}]}, "16": {"loc": {"start": {"line": 161, "column": 8}, "end": {"line": 167, "column": 9}}, "type": "if", "locations": [{"start": {"line": 161, "column": 8}, "end": {"line": 167, "column": 9}}]}, "17": {"loc": {"start": {"line": 161, "column": 12}, "end": {"line": 161, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 161, "column": 12}, "end": {"line": 161, "column": 26}}, {"start": {"line": 161, "column": 30}, "end": {"line": 161, "column": 62}}]}, "18": {"loc": {"start": {"line": 169, "column": 8}, "end": {"line": 172, "column": 9}}, "type": "if", "locations": [{"start": {"line": 169, "column": 8}, "end": {"line": 172, "column": 9}}]}, "19": {"loc": {"start": {"line": 169, "column": 12}, "end": {"line": 169, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 169, "column": 12}, "end": {"line": 169, "column": 26}}, {"start": {"line": 169, "column": 30}, "end": {"line": 169, "column": 50}}]}, "20": {"loc": {"start": {"line": 187, "column": 6}, "end": {"line": 190, "column": 7}}, "type": "if", "locations": [{"start": {"line": 187, "column": 6}, "end": {"line": 190, "column": 7}}]}, "21": {"loc": {"start": {"line": 198, "column": 8}, "end": {"line": 204, "column": 9}}, "type": "if", "locations": [{"start": {"line": 198, "column": 8}, "end": {"line": 204, "column": 9}}]}, "22": {"loc": {"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 198, "column": 12}, "end": {"line": 198, "column": 26}}, {"start": {"line": 198, "column": 30}, "end": {"line": 198, "column": 62}}]}, "23": {"loc": {"start": {"line": 206, "column": 8}, "end": {"line": 209, "column": 9}}, "type": "if", "locations": [{"start": {"line": 206, "column": 8}, "end": {"line": 209, "column": 9}}]}, "24": {"loc": {"start": {"line": 206, "column": 12}, "end": {"line": 206, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 206, "column": 12}, "end": {"line": 206, "column": 26}}, {"start": {"line": 206, "column": 30}, "end": {"line": 206, "column": 50}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0, 0, 0, 0, 0, 0, 0, 0], "3": [0], "4": [0], "5": [0, 0], "6": [0], "7": [0, 0], "8": [0, 0, 0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0, 0, 0], "14": [0, 0], "15": [0], "16": [0], "17": [0, 0], "18": [0], "19": [0, 0], "20": [0], "21": [0], "22": [0, 0], "23": [0], "24": [0, 0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\highlightKeywords.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\highlightKeywords.ts", "statementMap": {"0": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 16}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 42}}, "2": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 38}}, "3": {"start": {"line": 16, "column": 26}, "end": {"line": 16, "column": 38}}, "4": {"start": {"line": 19, "column": 38}, "end": {"line": 19, "column": 40}}, "5": {"start": {"line": 21, "column": 2}, "end": {"line": 33, "column": 3}}, "6": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 46}}, "7": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 12}}, "8": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 46}}, "9": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 12}}, "10": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 46}}, "11": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": 12}}, "12": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 18}}, "13": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 52}}, "14": {"start": {"line": 35, "column": 40}, "end": {"line": 35, "column": 52}}, "15": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 58}}, "16": {"start": {"line": 38, "column": 37}, "end": {"line": 38, "column": 56}}, "17": {"start": {"line": 42, "column": 19}, "end": {"line": 50, "column": 4}}, "18": {"start": {"line": 43, "column": 4}, "end": {"line": 49, "column": 5}}, "19": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 46}}, "20": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 35}}, "21": {"start": {"line": 52, "column": 18}, "end": {"line": 52, "column": 36}}, "22": {"start": {"line": 53, "column": 16}, "end": {"line": 53, "column": 41}}, "23": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 89}}, "24": {"start": {"line": 56, "column": 38}, "end": {"line": 56, "column": 87}}, "25": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 55}}}, "fnMap": {"0": {"name": "highlightKeywords", "decl": {"start": {"line": 11, "column": 16}, "end": {"line": 11, "column": 33}}, "loc": {"start": {"line": 14, "column": 20}, "end": {"line": 57, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 38, "column": 27}, "end": {"line": 38, "column": 28}}, "loc": {"start": {"line": 38, "column": 37}, "end": {"line": 38, "column": 56}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 43}, "end": {"line": 42, "column": 50}}, "loc": {"start": {"line": 42, "column": 53}, "end": {"line": 50, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 56, "column": 29}, "end": {"line": 56, "column": 34}}, "loc": {"start": {"line": 56, "column": 38}, "end": {"line": 56, "column": 87}}}, "4": {"name": "escapeRegExp", "decl": {"start": {"line": 64, "column": 9}, "end": {"line": 64, "column": 21}}, "loc": {"start": {"line": 64, "column": 36}, "end": {"line": 66, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 38}}, "type": "if", "locations": [{"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 38}}]}, "1": {"loc": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 24}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 11}}, {"start": {"line": 16, "column": 15}, "end": {"line": 16, "column": 24}}]}, "2": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 33, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 12}}, {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 12}}, {"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": 12}}, {"start": {"line": 31, "column": 4}, "end": {"line": 32, "column": 18}}]}, "3": {"loc": {"start": {"line": 23, "column": 28}, "end": {"line": 23, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 23, "column": 28}, "end": {"line": 23, "column": 39}}, {"start": {"line": 23, "column": 43}, "end": {"line": 23, "column": 45}}]}, "4": {"loc": {"start": {"line": 26, "column": 28}, "end": {"line": 26, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 28}, "end": {"line": 26, "column": 39}}, {"start": {"line": 26, "column": 43}, "end": {"line": 26, "column": 45}}]}, "5": {"loc": {"start": {"line": 29, "column": 28}, "end": {"line": 29, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 28}, "end": {"line": 29, "column": 39}}, {"start": {"line": 29, "column": 43}, "end": {"line": 29, "column": 45}}]}, "6": {"loc": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 52}}, "type": "if", "locations": [{"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": 52}}]}, "7": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 49, "column": 5}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 49, "column": 5}}, {"start": {"line": 46, "column": 11}, "end": {"line": 49, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0, 0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0], "7": [0, 0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\i18n.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\i18n.ts", "statementMap": {"0": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 16}}, "1": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 16}}, "2": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 16}}, "3": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 16}}, "4": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 16}}, "5": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 16}}, "6": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "7": {"start": {"line": 9, "column": 2}, "end": {"line": 16, "column": 3}}, "8": {"start": {"line": 11, "column": 6}, "end": {"line": 11, "column": 30}}, "9": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 44}}, "10": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 30}}, "11": {"start": {"line": 29, "column": 2}, "end": {"line": 36, "column": 3}}, "12": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 31}}, "13": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 46}}, "14": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 31}}, "15": {"start": {"line": 49, "column": 2}, "end": {"line": 56, "column": 3}}, "16": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 33}}, "17": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 50}}, "18": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 33}}, "19": {"start": {"line": 69, "column": 2}, "end": {"line": 76, "column": 3}}, "20": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": 39}}, "21": {"start": {"line": 73, "column": 6}, "end": {"line": 73, "column": 62}}, "22": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 39}}, "23": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 30}}, "24": {"start": {"line": 89, "column": 20}, "end": {"line": 89, "column": 30}}, "25": {"start": {"line": 91, "column": 2}, "end": {"line": 98, "column": 3}}, "26": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 38}}, "27": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 60}}, "28": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 38}}, "29": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 24}}, "30": {"start": {"line": 111, "column": 14}, "end": {"line": 111, "column": 24}}, "31": {"start": {"line": 113, "column": 2}, "end": {"line": 120, "column": 3}}, "32": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 31}}, "33": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 46}}, "34": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 31}}}, "fnMap": {"0": {"name": "pickPrompt", "decl": {"start": {"line": 8, "column": 16}, "end": {"line": 8, "column": 26}}, "loc": {"start": {"line": 8, "column": 38}, "end": {"line": 17, "column": 1}}}, "1": {"name": "pickAnswerText", "decl": {"start": {"line": 25, "column": 16}, "end": {"line": 25, "column": 30}}, "loc": {"start": {"line": 27, "column": 16}, "end": {"line": 37, "column": 1}}}, "2": {"name": "pickTopicText", "decl": {"start": {"line": 45, "column": 16}, "end": {"line": 45, "column": 29}}, "loc": {"start": {"line": 47, "column": 16}, "end": {"line": 57, "column": 1}}}, "3": {"name": "pickSubTopicText", "decl": {"start": {"line": 65, "column": 16}, "end": {"line": 65, "column": 32}}, "loc": {"start": {"line": 67, "column": 16}, "end": {"line": 77, "column": 1}}}, "4": {"name": "pickExplanationText", "decl": {"start": {"line": 85, "column": 16}, "end": {"line": 85, "column": 35}}, "loc": {"start": {"line": 87, "column": 16}, "end": {"line": 99, "column": 1}}}, "5": {"name": "pickMediaAltText", "decl": {"start": {"line": 107, "column": 16}, "end": {"line": 107, "column": 32}}, "loc": {"start": {"line": 109, "column": 16}, "end": {"line": 121, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 2}, "end": {"line": 16, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 10, "column": 4}, "end": {"line": 11, "column": 30}}, {"start": {"line": 12, "column": 4}, "end": {"line": 13, "column": 44}}, {"start": {"line": 14, "column": 4}, "end": {"line": 15, "column": 30}}]}, "1": {"loc": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 23}}, {"start": {"line": 11, "column": 27}, "end": {"line": 11, "column": 29}}]}, "2": {"loc": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 23}}, {"start": {"line": 13, "column": 27}, "end": {"line": 13, "column": 37}}, {"start": {"line": 13, "column": 41}, "end": {"line": 13, "column": 43}}]}, "3": {"loc": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 23}}, {"start": {"line": 15, "column": 27}, "end": {"line": 15, "column": 29}}]}, "4": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 36, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 31, "column": 31}}, {"start": {"line": 32, "column": 4}, "end": {"line": 33, "column": 46}}, {"start": {"line": 34, "column": 4}, "end": {"line": 35, "column": 31}}]}, "5": {"loc": {"start": {"line": 31, "column": 13}, "end": {"line": 31, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 31, "column": 13}, "end": {"line": 31, "column": 24}}, {"start": {"line": 31, "column": 28}, "end": {"line": 31, "column": 30}}]}, "6": {"loc": {"start": {"line": 33, "column": 13}, "end": {"line": 33, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 13}, "end": {"line": 33, "column": 24}}, {"start": {"line": 33, "column": 28}, "end": {"line": 33, "column": 39}}, {"start": {"line": 33, "column": 43}, "end": {"line": 33, "column": 45}}]}, "7": {"loc": {"start": {"line": 35, "column": 13}, "end": {"line": 35, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 13}, "end": {"line": 35, "column": 24}}, {"start": {"line": 35, "column": 28}, "end": {"line": 35, "column": 30}}]}, "8": {"loc": {"start": {"line": 49, "column": 2}, "end": {"line": 56, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 51, "column": 33}}, {"start": {"line": 52, "column": 4}, "end": {"line": 53, "column": 50}}, {"start": {"line": 54, "column": 4}, "end": {"line": 55, "column": 33}}]}, "9": {"loc": {"start": {"line": 51, "column": 13}, "end": {"line": 51, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 51, "column": 13}, "end": {"line": 51, "column": 26}}, {"start": {"line": 51, "column": 30}, "end": {"line": 51, "column": 32}}]}, "10": {"loc": {"start": {"line": 53, "column": 13}, "end": {"line": 53, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 13}, "end": {"line": 53, "column": 26}}, {"start": {"line": 53, "column": 30}, "end": {"line": 53, "column": 43}}, {"start": {"line": 53, "column": 47}, "end": {"line": 53, "column": 49}}]}, "11": {"loc": {"start": {"line": 55, "column": 13}, "end": {"line": 55, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 13}, "end": {"line": 55, "column": 26}}, {"start": {"line": 55, "column": 30}, "end": {"line": 55, "column": 32}}]}, "12": {"loc": {"start": {"line": 69, "column": 2}, "end": {"line": 76, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 70, "column": 4}, "end": {"line": 71, "column": 39}}, {"start": {"line": 72, "column": 4}, "end": {"line": 73, "column": 62}}, {"start": {"line": 74, "column": 4}, "end": {"line": 75, "column": 39}}]}, "13": {"loc": {"start": {"line": 71, "column": 13}, "end": {"line": 71, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 71, "column": 13}, "end": {"line": 71, "column": 32}}, {"start": {"line": 71, "column": 36}, "end": {"line": 71, "column": 38}}]}, "14": {"loc": {"start": {"line": 73, "column": 13}, "end": {"line": 73, "column": 61}}, "type": "binary-expr", "locations": [{"start": {"line": 73, "column": 13}, "end": {"line": 73, "column": 32}}, {"start": {"line": 73, "column": 36}, "end": {"line": 73, "column": 55}}, {"start": {"line": 73, "column": 59}, "end": {"line": 73, "column": 61}}]}, "15": {"loc": {"start": {"line": 75, "column": 13}, "end": {"line": 75, "column": 38}}, "type": "binary-expr", "locations": [{"start": {"line": 75, "column": 13}, "end": {"line": 75, "column": 32}}, {"start": {"line": 75, "column": 36}, "end": {"line": 75, "column": 38}}]}, "16": {"loc": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 30}}, "type": "if", "locations": [{"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 30}}]}, "17": {"loc": {"start": {"line": 91, "column": 2}, "end": {"line": 98, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 92, "column": 4}, "end": {"line": 93, "column": 38}}, {"start": {"line": 94, "column": 4}, "end": {"line": 95, "column": 60}}, {"start": {"line": 96, "column": 4}, "end": {"line": 97, "column": 38}}]}, "18": {"loc": {"start": {"line": 93, "column": 13}, "end": {"line": 93, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 93, "column": 13}, "end": {"line": 93, "column": 31}}, {"start": {"line": 93, "column": 35}, "end": {"line": 93, "column": 37}}]}, "19": {"loc": {"start": {"line": 95, "column": 13}, "end": {"line": 95, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 95, "column": 13}, "end": {"line": 95, "column": 31}}, {"start": {"line": 95, "column": 35}, "end": {"line": 95, "column": 53}}, {"start": {"line": 95, "column": 57}, "end": {"line": 95, "column": 59}}]}, "20": {"loc": {"start": {"line": 97, "column": 13}, "end": {"line": 97, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 13}, "end": {"line": 97, "column": 31}}, {"start": {"line": 97, "column": 35}, "end": {"line": 97, "column": 37}}]}, "21": {"loc": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 24}}, "type": "if", "locations": [{"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 24}}]}, "22": {"loc": {"start": {"line": 113, "column": 2}, "end": {"line": 120, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 114, "column": 4}, "end": {"line": 115, "column": 31}}, {"start": {"line": 116, "column": 4}, "end": {"line": 117, "column": 46}}, {"start": {"line": 118, "column": 4}, "end": {"line": 119, "column": 31}}]}, "23": {"loc": {"start": {"line": 115, "column": 13}, "end": {"line": 115, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 13}, "end": {"line": 115, "column": 24}}, {"start": {"line": 115, "column": 28}, "end": {"line": 115, "column": 30}}]}, "24": {"loc": {"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 117, "column": 13}, "end": {"line": 117, "column": 24}}, {"start": {"line": 117, "column": 28}, "end": {"line": 117, "column": 39}}, {"start": {"line": 117, "column": 43}, "end": {"line": 117, "column": 45}}]}, "25": {"loc": {"start": {"line": 119, "column": 13}, "end": {"line": 119, "column": 30}}, "type": "binary-expr", "locations": [{"start": {"line": 119, "column": 13}, "end": {"line": 119, "column": 24}}, {"start": {"line": 119, "column": 28}, "end": {"line": 119, "column": 30}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 0, 0], "1": [0, 0], "2": [0, 0, 0], "3": [0, 0], "4": [0, 0, 0], "5": [0, 0], "6": [0, 0, 0], "7": [0, 0], "8": [0, 0, 0], "9": [0, 0], "10": [0, 0, 0], "11": [0, 0], "12": [0, 0, 0], "13": [0, 0], "14": [0, 0, 0], "15": [0, 0], "16": [0], "17": [0, 0, 0], "18": [0, 0], "19": [0, 0, 0], "20": [0, 0], "21": [0], "22": [0, 0, 0], "23": [0, 0], "24": [0, 0, 0], "25": [0, 0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\prisma.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\prisma.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 45}}, "1": {"start": {"line": 7, "column": 15}, "end": {"line": 9, "column": 2}}, "2": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": null}}, "3": {"start": {"line": 11, "column": 43}, "end": {"line": 11, "column": null}}, "4": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": null}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 7, "column": 15}, "end": {"line": 9, "column": 2}}, "type": "binary-expr", "locations": [{"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 28}}, {"start": {"line": 7, "column": 32}, "end": {"line": 9, "column": 2}}]}, "1": {"loc": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": null}}, "type": "if", "locations": [{"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {}, "b": {"0": [0, 0], "1": [0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\rate-limit.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\rate-limit.ts", "statementMap": {"0": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 16}}, "1": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 16}}, "2": {"start": {"line": 8, "column": 23}, "end": {"line": 8, "column": 37}}, "3": {"start": {"line": 9, "column": 21}, "end": {"line": 9, "column": 23}}, "4": {"start": {"line": 11, "column": 18}, "end": {"line": 11, "column": 52}}, "5": {"start": {"line": 14, "column": 17}, "end": {"line": 14, "column": 34}}, "6": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 39}}, "7": {"start": {"line": 19, "column": 13}, "end": {"line": 19, "column": 81}}, "8": {"start": {"line": 20, "column": 14}, "end": {"line": 20, "column": 24}}, "9": {"start": {"line": 23, "column": 2}, "end": {"line": 27, "column": 3}}, "10": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, "11": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 28}}, "12": {"start": {"line": 29, "column": 15}, "end": {"line": 29, "column": 32}}, "13": {"start": {"line": 32, "column": 2}, "end": {"line": 38, "column": 3}}, "14": {"start": {"line": 33, "column": 4}, "end": {"line": 36, "column": 6}}, "15": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 30}}, "16": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 17}}, "17": {"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 3}}, "18": {"start": {"line": 44, "column": 26}, "end": {"line": 44, "column": 68}}, "19": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 53}}, "20": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 46}}, "21": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": 57}}, "22": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 7}}, "23": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 17}}, "24": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 51}}, "25": {"start": {"line": 56, "column": 2}, "end": {"line": 56, "column": 70}}, "26": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 55}}, "27": {"start": {"line": 59, "column": 2}, "end": {"line": 59, "column": 14}}}, "fnMap": {"0": {"name": "getRateLimitResetTime", "decl": {"start": {"line": 13, "column": 16}, "end": {"line": 13, "column": 37}}, "loc": {"start": {"line": 13, "column": 48}, "end": {"line": 16, "column": 1}}}, "1": {"name": "rateLimit", "decl": {"start": {"line": 18, "column": 16}, "end": {"line": 18, "column": 25}}, "loc": {"start": {"line": 18, "column": 67}, "end": {"line": 60, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 9}, "end": {"line": 15, "column": 38}}, "type": "cond-expr", "locations": [{"start": {"line": 15, "column": 18}, "end": {"line": 15, "column": 34}}, {"start": {"line": 15, "column": 37}, "end": {"line": 15, "column": 38}}]}, "1": {"loc": {"start": {"line": 19, "column": 13}, "end": {"line": 19, "column": 81}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 13}, "end": {"line": 19, "column": 53}}, {"start": {"line": 19, "column": 57}, "end": {"line": 19, "column": 81}}]}, "2": {"loc": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}, "type": "if", "locations": [{"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 5}}]}, "3": {"loc": {"start": {"line": 32, "column": 2}, "end": {"line": 38, "column": 3}}, "type": "if", "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 38, "column": 3}}]}, "4": {"loc": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 13}}, {"start": {"line": 32, "column": 17}, "end": {"line": 32, "column": 40}}]}, "5": {"loc": {"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 3}}, "type": "if", "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 3}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0], "3": [0], "4": [0, 0], "5": [0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\stats\\wilson.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\stats\\wilson.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 16}}, "1": {"start": {"line": 2, "column": 2}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 24}}, "3": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 26}}, "4": {"start": {"line": 4, "column": 16}, "end": {"line": 4, "column": 32}}, "5": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 42}}, "6": {"start": {"line": 6, "column": 17}, "end": {"line": 6, "column": 76}}, "7": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 35}}}, "fnMap": {"0": {"name": "wilsonLowerBound", "decl": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 32}}, "loc": {"start": {"line": 1, "column": 69}, "end": {"line": 8, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 1, "column": 61}, "end": {"line": 1, "column": 69}}, "type": "default-arg", "locations": [{"start": {"line": 1, "column": 65}, "end": {"line": 1, "column": 69}}]}, "1": {"loc": {"start": {"line": 2, "column": 2}, "end": {"line": 2, "column": 24}}, "type": "if", "locations": [{"start": {"line": 2, "column": 2}, "end": {"line": 2, "column": 24}}]}}, "s": {"0": 1, "1": 1, "2": 0, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1}, "f": {"0": 1}, "b": {"0": [1], "1": [0]}}, "D:\\projects\\GitHub\\my-quiz-app\\lib\\validation\\questionSpec.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\lib\\validation\\questionSpec.ts", "statementMap": {"0": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 16}}, "1": {"start": {"line": 126, "column": 0}, "end": {"line": 126, "column": 16}}, "2": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 24}}, "3": {"start": {"line": 6, "column": 13}, "end": {"line": 13, "column": 3}}, "4": {"start": {"line": 16, "column": 13}, "end": {"line": 28, "column": 3}}, "5": {"start": {"line": 31, "column": 13}, "end": {"line": 53, "column": 2}}, "6": {"start": {"line": 45, "column": 12}, "end": {"line": 45, "column": 58}}, "7": {"start": {"line": 49, "column": 19}, "end": {"line": 49, "column": 63}}, "8": {"start": {"line": 49, "column": 57}, "end": {"line": 49, "column": 62}}, "9": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 51}}, "10": {"start": {"line": 50, "column": 38}, "end": {"line": 50, "column": 49}}, "11": {"start": {"line": 56, "column": 13}, "end": {"line": 61, "column": 3}}, "12": {"start": {"line": 64, "column": 13}, "end": {"line": 67, "column": 3}}, "13": {"start": {"line": 70, "column": 13}, "end": {"line": 74, "column": 3}}, "14": {"start": {"line": 77, "column": 13}, "end": {"line": 80, "column": 3}}, "15": {"start": {"line": 83, "column": 13}, "end": {"line": 87, "column": 3}}, "16": {"start": {"line": 90, "column": 28}, "end": {"line": 100, "column": 11}}, "17": {"start": {"line": 112, "column": 2}, "end": {"line": 114, "column": 3}}, "18": {"start": {"line": 113, "column": 4}, "end": {"line": 113, "column": 54}}, "19": {"start": {"line": 116, "column": 17}, "end": {"line": 116, "column": 78}}, "20": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 50}}, "21": {"start": {"line": 130, "column": 2}, "end": {"line": 138, "column": 3}}, "22": {"start": {"line": 131, "column": 26}, "end": {"line": 131, "column": 50}}, "23": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": 50}}, "24": {"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, "25": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 39}}, "26": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 16}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 3}}, "loc": {"start": {"line": 45, "column": 12}, "end": {"line": 45, "column": 58}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 3}}, "loc": {"start": {"line": 48, "column": 11}, "end": {"line": 51, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 49, "column": 47}, "end": {"line": 49, "column": 48}}, "loc": {"start": {"line": 49, "column": 57}, "end": {"line": 49, "column": 62}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 50, "column": 24}, "end": {"line": 50, "column": 25}}, "loc": {"start": {"line": 50, "column": 38}, "end": {"line": 50, "column": 49}}}, "4": {"name": "validateSpec", "decl": {"start": {"line": 108, "column": 16}, "end": {"line": 108, "column": 28}}, "loc": {"start": {"line": 110, "column": 15}, "end": {"line": 118, "column": 1}}}, "5": {"name": "safeValidateSpec", "decl": {"start": {"line": 126, "column": 16}, "end": {"line": 126, "column": 32}}, "loc": {"start": {"line": 128, "column": 15}, "end": {"line": 139, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 112, "column": 2}, "end": {"line": 114, "column": 3}}, "type": "if", "locations": [{"start": {"line": 112, "column": 2}, "end": {"line": 114, "column": 3}}]}, "1": {"loc": {"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}, "type": "if", "locations": [{"start": {"line": 134, "column": 4}, "end": {"line": 136, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0], "1": [0]}}, "D:\\projects\\GitHub\\my-quiz-app\\services\\computeMastery.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\services\\computeMastery.ts", "statementMap": {"0": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 7}}, "1": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 35}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 26}}, "3": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 55}}, "4": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 24}}, "5": {"start": {"line": 6, "column": 22}, "end": {"line": 6, "column": 26}}, "6": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 23}}, "7": {"start": {"line": 21, "column": 16}, "end": {"line": 21, "column": 61}}, "8": {"start": {"line": 22, "column": 18}, "end": {"line": 31, "column": 4}}, "9": {"start": {"line": 34, "column": 18}, "end": {"line": 38, "column": 5}}, "10": {"start": {"line": 34, "column": 55}, "end": {"line": 38, "column": 4}}, "11": {"start": {"line": 40, "column": 2}, "end": {"line": 44, "column": 3}}, "12": {"start": {"line": 41, "column": 14}, "end": {"line": 41, "column": 45}}, "13": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 21}}, "14": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 36}}, "15": {"start": {"line": 43, "column": 21}, "end": {"line": 43, "column": 36}}, "16": {"start": {"line": 47, "column": 17}, "end": {"line": 50, "column": null}}, "17": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 53}}, "18": {"start": {"line": 54, "column": 18}, "end": {"line": 54, "column": 19}}, "19": {"start": {"line": 55, "column": 2}, "end": {"line": 59, "column": 3}}, "20": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": 16}}, "21": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "22": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 24}}, "23": {"start": {"line": 63, "column": 4}, "end": {"line": 67, "column": 20}}, "24": {"start": {"line": 70, "column": 2}, "end": {"line": 74, "column": 5}}, "25": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 35}}}, "fnMap": {"0": {"name": "computeAndUpsertMastery", "decl": {"start": {"line": 15, "column": 22}, "end": {"line": 15, "column": 45}}, "loc": {"start": {"line": 18, "column": 17}, "end": {"line": 77, "column": 1}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 34, "column": 44}, "end": {"line": 34, "column": 45}}, "loc": {"start": {"line": 34, "column": 55}, "end": {"line": 38, "column": 4}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 47, "column": 29}, "end": {"line": 47, "column": 30}}, "loc": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 53}}}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 16}, "end": {"line": 28, "column": 32}}, "type": "cond-expr", "locations": [{"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 29}}, {"start": {"line": 28, "column": 10}, "end": {"line": 28, "column": 32}}]}, "1": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 36}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 36}}]}, "2": {"loc": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 29}}, {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 36}}, {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 53}}]}, "3": {"loc": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "type": "if", "locations": [{"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}]}, "4": {"loc": {"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 56, "column": 8}, "end": {"line": 56, "column": 17}}, {"start": {"line": 56, "column": 21}, "end": {"line": 56, "column": 54}}]}, "5": {"loc": {"start": {"line": 63, "column": 4}, "end": {"line": 67, "column": 20}}, "type": "cond-expr", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 13}}, {"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 20}}]}, "6": {"loc": {"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": 20}}, "type": "cond-expr", "locations": [{"start": {"line": 66, "column": 10}, "end": {"line": 66, "column": 18}}, {"start": {"line": 67, "column": 10}, "end": {"line": 67, "column": 20}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 4, "8": 4, "9": 4, "10": 24, "11": 4, "12": 44, "13": 44, "14": 44, "15": 44, "16": 4, "17": 24, "18": 4, "19": 4, "20": 4, "21": 24, "22": 3, "23": 4, "24": 4, "25": 4}, "f": {"0": 4, "1": 24, "2": 24}, "b": {"0": [4, 0], "1": [44], "2": [24, 4, 4], "3": [3], "4": [24, 3], "5": [2, 2], "6": [2, 0]}}, "D:\\projects\\GitHub\\my-quiz-app\\services\\storage\\LocalDiskStorage.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\services\\storage\\LocalDiskStorage.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 29}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 55}}, "3": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": 54}}, "4": {"start": {"line": 29, "column": 17}, "end": {"line": 29, "column": 45}}, "5": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 35}}, "6": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 29}}, "7": {"start": {"line": 43, "column": 4}, "end": {"line": 49, "column": 5}}, "8": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 52}}, "9": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 13}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "loc": {"start": {"line": 12, "column": 2}, "end": {"line": 15, "column": 3}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 7}}, "loc": {"start": {"line": 24, "column": 52}, "end": {"line": 36, "column": 3}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 7}}, "loc": {"start": {"line": 42, "column": 26}, "end": {"line": 50, "column": 3}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "D:\\projects\\GitHub\\my-quiz-app\\services\\storage\\index.ts": {"path": "D:\\projects\\GitHub\\my-quiz-app\\services\\storage\\index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 54}}, "1": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 54}}, "2": {"start": {"line": 11, "column": 2}, "end": {"line": 16, "column": 3}}, "3": {"start": {"line": 14, "column": 6}, "end": {"line": 14, "column": 36}}, "4": {"start": {"line": 20, "column": 13}, "end": {"line": 20, "column": 42}}}, "fnMap": {"0": {"name": "getStorageDriver", "decl": {"start": {"line": 8, "column": 9}, "end": {"line": 8, "column": 25}}, "loc": {"start": {"line": 8, "column": 25}, "end": {"line": 17, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 17}, "end": {"line": 9, "column": 43}}, {"start": {"line": 9, "column": 47}, "end": {"line": 9, "column": 54}}]}, "1": {"loc": {"start": {"line": 11, "column": 2}, "end": {"line": 16, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": 17}}, {"start": {"line": 13, "column": 4}, "end": {"line": 14, "column": 36}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0]}}}