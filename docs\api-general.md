# General API Endpoints

This document provides documentation for the general API endpoints in the My Quiz App application.

## Years

### Get All Years

Retrieves a list of all available years.

- **URL**: `/api/years`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None

#### Response

```json
[
  {
    "id": 1,
    "yearNumber": "Year 1",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "yearNumber": "Year 2",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
]
```

## Subjects

### Get All Subjects

Retrieves a list of all available subjects.

- **URL**: `/api/subjects`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None

#### Response

```json
[
  {
    "id": 1,
    "name": "Mathematics",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "name": "Science",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
]
```

### Get Subjects by Year

Retrieves subjects available for a specific year.

- **URL**: `/api/subjects-by-year?yearNumber={yearNumber}`
- **Method**: `GET`
- **Authentication Required**: Yes
- **Role Required**: Any authenticated user

#### Request Parameters

| Parameter   | Type   | Required | Description                |
|-------------|--------|----------|----------------------------|
| yearNumber  | string | Yes      | The year number (e.g., "Year 5") |

#### Response

```json
[
  {
    "id": 1,
    "name": "Mathematics",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "name": "Science",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
]
```

## Topics/Units

### Get Topics by Subject

Retrieves topics/units for a specific subject.

- **URL**: `/api/topics?subjectId={subjectId}`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None

#### Request Parameters

| Parameter | Type   | Required | Description        |
|-----------|--------|----------|--------------------|
| subjectId | string | Yes      | The subject ID     |

#### Response

```json
[
  {
    "id": 1,
    "unitNumber": 1,
    "topicEn": "Addition",
    "topicZh": "加法",
    "topicMs": "Penambahan",
    "subjectId": 1,
    "yearId": 1,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "unitNumber": 2,
    "topicEn": "Subtraction",
    "topicZh": "减法",
    "topicMs": "Penolakan",
    "subjectId": 1,
    "yearId": 1,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
]
```

## Child Data

### Get Child Data

Retrieves data for the authenticated child.

- **URL**: `/api/child-data`
- **Method**: `GET`
- **Authentication Required**: Yes
- **Role Required**: Any authenticated user

#### Response

```json
{
  "id": 1,
  "name": "John Doe",
  "username": "johndoe",
  "year": "Year 5",
  "accountId": 1,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

## Syllabus

### Get Complete Syllabus

Retrieves the complete syllabus structure including years, subjects, and units.

- **URL**: `/api/syllabus`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None

#### Response

```json
{
  "years": [
    {
      "id": 1,
      "yearNumber": "Year 1",
      "subjects": [
        {
          "id": 1,
          "name": "Mathematics",
          "units": [
            {
              "id": 1,
              "unitNumber": 1,
              "topicEn": "Addition",
              "topicZh": "加法"
            }
          ]
        }
      ]
    }
  ]
}
```

## Translation

### Translate Text

Translates text using a dictionary.

- **URL**: `/api/translate`
- **Method**: `POST`
- **Authentication Required**: No
- **Role Required**: None

#### Request Body

```json
{
  "text": "你好"
}
```

#### Response

```json
{
  "translation": "Hello"
}
```

### AI Translator

Translates text using AI.

- **URL**: `/api/ai-translator`
- **Method**: `POST`
- **Authentication Required**: No
- **Role Required**: None

#### Request Body

```json
{
  "highlightedText": "你好",
  "question": "Complete question text for context",
  "targetLanguage": "English"
}
```

> Note: `targetLanguage` is optional and defaults to "English" if not provided.

#### Response

```json
{
  "translatedText": "Hello"
}
```

#### Implementation Details

- Uses OpenRouter API with the Qwen 3 32B model for translation
- Requires `OPENROUTER_TRANSLATOR_API_KEY` environment variable
- Provides context-aware translation by including the full question text
- Returns only the translated text without additional commentary
- Falls back to "Failed to get translation" if the API response is invalid
- Supports translation to any language by specifying the `targetLanguage` parameter

### Log Translation

Logs a translation event.

- **URL**: `/api/log-translation`
- **Method**: `POST`
- **Authentication Required**: No
- **Role Required**: None

#### Request Body

```json
{
  "childId": 1,
  "questionId": 123,
  "translatedText": "Hello"
}
```

#### Response

```json
{
  "id": 1,
  "childId": 1,
  "questionId": 123,
  "translatedText": "Hello",
  "timestamp": "2023-01-01T00:00:00.000Z"
}
```


