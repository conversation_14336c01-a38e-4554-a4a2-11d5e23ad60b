import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // This is a test endpoint to verify the hint API functionality

  // Check for OpenRouter API key
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  if (!openRouterApiKey) {
    return res.status(500).json({ message: 'OpenRouter API key not configured' });
  }

  // Get the question ID from the request query or body
  const questionId = req.method === 'GET'
    ? req.query.questionId
    : req.body?.questionId;

  // If no question ID is provided, use a default question for testing
  const useDefaultQuestion = !questionId;

  try {
    let question = null;
    let prompt = '';

    if (!useDefaultQuestion) {
      // Validate questionId
      const questionIdNum = parseInt(questionId as string, 10);
      if (isNaN(questionIdNum)) {
        return res.status(400).json({ message: 'Invalid question ID format' });
      }

      // Fetch the question from the database
      question = await prisma.question.findUnique({
        where: { id: questionIdNum },
        include: {
          choices: true,
          answer: true,
          explanation: true,
        },
      });

      if (!question) {
        return res.status(404).json({ message: 'Question not found' });
      }

      // Prepare prompt with the actual question
      prompt = `
      I need a hint for the following question:

      Question: ${question.promptEn || question.promptZh}

      Please provide a helpful hint that guides the student towards the answer without giving it away directly.
      The hint should be concise (1-2 sentences) and appropriate for elementary school students.
      `;
    } else {
      // Use default question for testing
      prompt = `
      I need a hint for the following question:

      Question: What is the capital of France?

      Please provide a helpful hint that guides the student towards the answer without giving it away directly.
      The hint should be concise (1-2 sentences) and appropriate for elementary school students.
      `;
    }

    // Query OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'google/gemini-2.0-flash-exp:free',
        messages: [
          { role: 'system', content: 'You are a helpful tutor providing hints for quiz questions.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 100
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      return res.status(500).json({
        message: 'LLM query failed',
        error: errorData.error?.message || 'Unknown error'
      });
    }

    const data = await response.json();
    const hint = data.choices?.[0]?.message?.content || 'No hint available';

    return res.status(200).json({
      question: useDefaultQuestion ? { promptEn: 'What is the capital of France?' } : question,
      hint,
      success: true,
      apiKeyPresent: !!openRouterApiKey,
      responseData: data
    });
  } catch (error) {
    console.error('Error generating hint:', error);
    return res.status(500).json({
      message: 'Error generating hint',
      error: error instanceof Error ? error.message : 'Unknown error',
      apiKeyPresent: !!openRouterApiKey
    });
  }
}
