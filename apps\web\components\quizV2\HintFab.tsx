import React, { useState } from 'react';
import { useQuizV2 } from './QuizV2Provider';

interface HintFabProps {
  className?: string;
  inline?: boolean;
}

export default function HintFab({ className = '', inline = false }: HintFabProps) {
  const {
    allowHints,
    explanation,
    questions,
    currentIndex,
    setExplanation,
    helpEnabled,
    attempt,
    setShowKeywords
  } = useQuizV2();

  // If hints are not allowed or we're in REVIEW phase, don't render the component
  if (!allowHints || !helpEnabled) return null;

  // Only disable if there's already an explanation showing
  const disabled = !!explanation;
  const [stage, setStage] = useState<0|1|2>(0);

  const handleHint = async () => {
    if (disabled) return;

    if (stage === 0) {
      // highlight keywords: call /api/ai-tutor/highlight
      try {
        const res = await fetch('/api/ai-tutor/highlight', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            questionId: questions[currentIndex].id
          })
        });

        if (!res.ok) {
          throw new Error(`Error fetching highlights: ${res.statusText}`);
        }

        const { highlight } = await res.json();
        setExplanation(highlight);
        setShowKeywords(true);
        setStage(1);

        // Log the hint usage
        try {
          await fetch('/api/log-hint', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              attemptId: attempt.quizAttempt.id,
              questionId: questions[currentIndex].id,
              stage: 1
            })
          });
        } catch (logError) {
          console.error('Error logging hint usage:', logError);
        }
      } catch (error) {
        console.error('Error fetching highlights:', error);
        setExplanation('Error fetching highlights. Please try again.');
      }
    } else {
      // full hint
      try {
        const res = await fetch('/api/ai-tutor/hint', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            questionId: questions[currentIndex].id
          })
        });

        if (!res.ok) {
          throw new Error(`Error fetching hint: ${res.statusText}`);
        }

        const { hint } = await res.json();
        setExplanation(hint);
        setStage(2);

        // Log the hint usage
        try {
          await fetch('/api/log-hint', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              attemptId: attempt.quizAttempt.id,
              questionId: questions[currentIndex].id,
              stage: 2
            })
          });
        } catch (logError) {
          console.error('Error logging hint usage:', logError);
        }
      } catch (error) {
        console.error('Error fetching hint:', error);
        setExplanation('Error fetching hint. Please try again.');
      }
    }
  };

  // For inline use in the header
  if (inline) {
    return (
      <div className={`flex items-center ${className}`}>
        <button
          onClick={handleHint}
          className={`flex items-center px-3 py-1 rounded-full text-sm font-medium mr-1
                    ${disabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'} text-white`}
          disabled={disabled}
          aria-label="Get hint"
        >
          <span className="mr-1">ℹ️</span>
          <span className="text-xs">∞</span>
        </button>
      </div>
    );
  }

  // For floating button use
  return (
    <div className="fixed top-4 right-4 md:right-10 z-50 flex flex-col items-center">
      <button
        onClick={handleHint}
        className={`h-12 w-12 rounded-full shadow-xl flex items-center justify-center
                    ${disabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
        disabled={disabled}
        aria-label="Get hint"
      >
        <span className="text-xl text-white">ℹ️</span>
      </button>
      <div className="mt-1 bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full flex items-center justify-center">
        <span className="text-sm">∞</span>
      </div>
    </div>
  );
}

