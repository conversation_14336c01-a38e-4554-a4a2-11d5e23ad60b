import { useState, useEffect } from 'react';
import useS<PERSON> from 'swr';

// Define types
interface ModelUsage {
  model: string;
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  batchCount: number;
  estimatedCost: number;
}

interface TokenSummary {
  totalPromptTokens: number;
  totalCompletionTokens: number;
  totalTokens: number;
  totalBatches: number;
  totalEstimatedCost: number;
  byModel: ModelUsage[];
}

// Approximate cost per 1000 tokens for different models
// These are rough estimates and should be updated with actual pricing
const MODEL_PRICING: Record<string, { input: number; output: number }> = {
  'gpt-4o': { input: 0.005, output: 0.015 },
  'gpt-4': { input: 0.03, output: 0.06 },
  'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 },
  'claude-3-opus': { input: 0.015, output: 0.075 },
  'claude-3-sonnet': { input: 0.003, output: 0.015 },
  'claude-3-haiku': { input: 0.00025, output: 0.00125 },
  'gemini-1.5-pro': { input: 0.0005, output: 0.0015 },
  'gemini-1.5-flash': { input: 0.00025, output: 0.00075 },
  'llama-3': { input: 0.0002, output: 0.0006 },
  'default': { input: 0.001, output: 0.002 } // Default fallback pricing
};

// Fetcher function for SWR
const fetcher = (url: string) => fetch(url).then(res => res.json());

export default function TokenUsageSummary() {
  // Fetch batches using SWR
  const { data: batches, error } = useSWR('/api/admin/batches', fetcher, {
    refreshInterval: 30000 // Refresh every 30 seconds
  });

  const [summary, setSummary] = useState<TokenSummary>({
    totalPromptTokens: 0,
    totalCompletionTokens: 0,
    totalTokens: 0,
    totalBatches: 0,
    totalEstimatedCost: 0,
    byModel: []
  });

  // Calculate token usage summary when batches data changes
  useEffect(() => {
    if (!batches) return;

    // Filter out batches with no token data
    const batchesWithTokens = batches.filter(
      (batch: any) => batch.totalTokens && batch.promptTokens && batch.completionTokens
    );

    // Group by model
    const modelGroups: Record<string, any[]> = {};
    batchesWithTokens.forEach((batch: any) => {
      const model = batch.modelUsed;
      if (!modelGroups[model]) {
        modelGroups[model] = [];
      }
      modelGroups[model].push(batch);
    });

    // Calculate usage by model
    const byModel: ModelUsage[] = Object.entries(modelGroups).map(([model, modelBatches]) => {
      const promptTokens = modelBatches.reduce((sum, batch) => sum + (batch.promptTokens || 0), 0);
      const completionTokens = modelBatches.reduce((sum, batch) => sum + (batch.completionTokens || 0), 0);
      const totalTokens = promptTokens + completionTokens;
      
      // Find the closest matching model for pricing
      const pricingKey = Object.keys(MODEL_PRICING).find(key => 
        model.toLowerCase().includes(key.toLowerCase())
      ) || 'default';
      
      const pricing = MODEL_PRICING[pricingKey];
      
      // Calculate estimated cost
      const estimatedCost = 
        (promptTokens / 1000) * pricing.input + 
        (completionTokens / 1000) * pricing.output;
      
      return {
        model,
        promptTokens,
        completionTokens,
        totalTokens,
        batchCount: modelBatches.length,
        estimatedCost
      };
    });

    // Sort by total tokens (descending)
    byModel.sort((a, b) => b.totalTokens - a.totalTokens);

    // Calculate totals
    const totalPromptTokens = byModel.reduce((sum, model) => sum + model.promptTokens, 0);
    const totalCompletionTokens = byModel.reduce((sum, model) => sum + model.completionTokens, 0);
    const totalTokens = totalPromptTokens + totalCompletionTokens;
    const totalBatches = batchesWithTokens.length;
    const totalEstimatedCost = byModel.reduce((sum, model) => sum + model.estimatedCost, 0);

    setSummary({
      totalPromptTokens,
      totalCompletionTokens,
      totalTokens,
      totalBatches,
      totalEstimatedCost,
      byModel
    });
  }, [batches]);

  // Format number with commas
  const formatNumber = (num: number) => num.toLocaleString();

  // Format currency
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  if (error) return <div className="text-red-500">Error loading token usage data</div>;
  if (!batches) return <div className="text-gray-500">Loading token usage data...</div>;

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h2 className="text-xl font-semibold mb-4">Token Usage Summary</h2>
      
      {/* Overall Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="text-sm text-gray-500">Total Batches</div>
          <div className="text-2xl font-bold">{summary.totalBatches}</div>
        </div>
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="text-sm text-gray-500">Total Tokens</div>
          <div className="text-2xl font-bold">{formatNumber(summary.totalTokens)}</div>
          <div className="text-xs text-gray-500">
            Input: {formatNumber(summary.totalPromptTokens)} | 
            Output: {formatNumber(summary.totalCompletionTokens)}
          </div>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg col-span-2">
          <div className="text-sm text-gray-500">Estimated Cost</div>
          <div className="text-2xl font-bold">{formatCurrency(summary.totalEstimatedCost)}</div>
          <div className="text-xs text-gray-500">
            Based on approximate pricing for each model
          </div>
        </div>
      </div>
      
      {/* By Model Breakdown */}
      <h3 className="text-lg font-medium mb-2">Usage by Model</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batches</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Input Tokens</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Output Tokens</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Tokens</th>
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Est. Cost</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {summary.byModel.map((modelUsage, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{modelUsage.model}</td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{modelUsage.batchCount}</td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatNumber(modelUsage.promptTokens)}</td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatNumber(modelUsage.completionTokens)}</td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatNumber(modelUsage.totalTokens)}</td>
                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatCurrency(modelUsage.estimatedCost)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="mt-4 text-xs text-gray-500">
        <p>Note: Cost estimates are approximate and based on published pricing for similar models.</p>
      </div>
    </div>
  );
}
