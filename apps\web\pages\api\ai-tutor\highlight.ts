import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  // For testing purposes, we'll allow unauthenticated requests
  // if (!session) {
  //   return res.status(401).json({ message: 'Unauthorized' });
  // }

  // Get the question ID from the request body
  const { questionId } = req.body;

  if (!questionId) {
    return res.status(400).json({ message: 'Question ID is required' });
  }

  try {
    // Fetch the question from the database
    const question = await prisma.question.findUnique({
      where: { id: questionId }
    });

    if (!question) {
      return res.status(404).json({ message: 'Question not found' });
    }

    // Check if the question has keywords
    if (!question.keywords || 
        (!question.keywords.en?.length && 
         !question.keywords.zh?.length && 
         !question.keywords.ms?.length)) {
      // If no keywords are available, generate a simple message
      return res.status(200).json({ 
        highlight: "No keywords available for this question. Try looking for important terms or concepts in the question." 
      });
    }

    // Determine which language's keywords to use based on the question's original language
    let keywordsToUse = [];
    const originalLanguage = question.originalLanguage || 'EN';
    
    if (originalLanguage === 'EN' && question.keywords.en?.length) {
      keywordsToUse = question.keywords.en;
    } else if (originalLanguage === 'ZH' && question.keywords.zh?.length) {
      keywordsToUse = question.keywords.zh;
    } else if (originalLanguage === 'MS' && question.keywords.ms?.length) {
      keywordsToUse = question.keywords.ms;
    } else {
      // Fallback to any available keywords
      keywordsToUse = question.keywords.en || question.keywords.zh || question.keywords.ms || [];
    }

    if (keywordsToUse.length === 0) {
      return res.status(200).json({ 
        highlight: "No keywords available for this question. Try looking for important terms or concepts in the question." 
      });
    }

    // Format the keywords into a helpful message
    const highlight = `Pay attention to these important words: ${keywordsToUse.join(', ')}`;

    // Log the highlight request
    try {
      await prisma.hintLog.create({
        data: {
          questionId,
          childId: session?.user?.id ? parseInt(session.user.id as string, 10) : 1, // Use default childId if not authenticated
          hint: highlight,
        },
      });
    } catch (logError) {
      console.error('Error logging highlight:', logError);
      // Continue even if logging fails
    }

    return res.status(200).json({ highlight });
  } catch (error) {
    console.error('Error generating highlight:', error);
    return res.status(500).json({ message: 'Error generating highlight', error });
  }
}
