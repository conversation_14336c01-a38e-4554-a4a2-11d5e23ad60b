// Script to apply the metadata migration to the database
const { execSync } = require('child_process');
const path = require('path');

console.log('Applying metadata migration to GenerationBatch model...');

try {
  // Generate Prisma client with the updated schema
  console.log('Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });

  // Apply the migration directly to the database
  console.log('Applying migration...');
  execSync('npx prisma db push', { stdio: 'inherit' });

  console.log('Migration applied successfully!');
  console.log('The GenerationBatch model now has a metadata field that can store tpDistribution.');
} catch (error) {
  console.error('Error applying migration:', error.message);
  process.exit(1);
}
