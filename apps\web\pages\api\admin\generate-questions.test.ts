import { createMocks } from 'node-mocks-http';
import handler from './generate-questions';
import prisma from '../../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import fs from 'fs/promises';
import path from 'path';
import { QuestionType, Language } from '@prisma/client';

// Mock the auth options
jest.mock('../auth/[...nextauth]', () => ({
  authOptions: {}
}));

// Mock the next-auth session
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock the Prisma client
jest.mock('../../../lib/prisma', () => ({
  note: {
    findMany: jest.fn(),
  },
  generationBatch: {
    create: jest.fn(),
  },
}));

// Mock fs/promises
jest.mock('fs/promises', () => ({
  writeFile: jest.fn(),
  mkdir: jest.fn().mockResolvedValue(undefined),
}));

// Mock path
jest.mock('path', () => ({
  join: jest.fn().mockImplementation((...args) => args.join('/')),
}));

describe('Admin Generate Questions API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock process.cwd() to return a consistent path
    jest.spyOn(process, 'cwd').mockReturnValue('/app');
  });

  // Mock admin session
  const mockAdminSession = {
    user: {
      id: '1',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
    },
  };

  // Mock non-admin session
  const mockNonAdminSession = {
    user: {
      id: '2',
      name: 'Regular User',
      email: '<EMAIL>',
      role: 'PARENT',
    },
  };

  // Valid request body
  const validRequestBody = {
    yearId: 1,
    subjectId: 2,
    unitId: 3,
    numQuestions: 5,
    questionTypes: [QuestionType.MULTIPLE_CHOICE, QuestionType.TRUE_FALSE],
    provider: 'openrouter',
    model: 'anthropic/claude-3-opus:beta',
    language: Language.ZH,
    tpDistribution: [1, 2, 3, 4, 5],
  };

  describe('Authentication and Authorization', () => {
    it('should return 401 if not authenticated', async () => {
      // Mock unauthenticated session
      (getServerSession as jest.Mock).mockResolvedValueOnce(null);

      const { req, res } = createMocks({
        method: 'POST',
        body: validRequestBody,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Unauthorized' });
    });

    it('should return 403 if not an admin', async () => {
      // Mock non-admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockNonAdminSession);

      const { req, res } = createMocks({
        method: 'POST',
        body: validRequestBody,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(403);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Forbidden - Admin access required' });
    });
  });

  describe('Input Validation', () => {
    it('should return 400 if required fields are missing', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      const { req, res } = createMocks({
        method: 'POST',
        body: {
          // Missing yearId
          subjectId: 2,
          questionTypes: [QuestionType.MULTIPLE_CHOICE],
          model: 'anthropic/claude-3-opus:beta',
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Missing required fields' });
    });

    it('should return 400 if numQuestions exceeds maximum', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      const { req, res } = createMocks({
        method: 'POST',
        body: {
          ...validRequestBody,
          numQuestions: 101, // Exceeds maximum of 100
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Maximum number of questions is 100' });
    });

    it('should return 400 if questionTypes is empty', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      const { req, res } = createMocks({
        method: 'POST',
        body: {
          ...validRequestBody,
          questionTypes: [], // Empty array
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Question types must be a non-empty array' });
    });

    it('should return 404 if no notes are found', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock empty notes response
      (prisma.note.findMany as jest.Mock).mockResolvedValueOnce([]);

      const { req, res } = createMocks({
        method: 'POST',
        body: validRequestBody,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData())).toEqual({ message: 'No notes found for the specified criteria' });
    });
  });

  describe('Successful Generation Request', () => {
    it('should create a generation batch and queue file', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock notes response
      const mockNotes = [{ id: 1 }, { id: 2 }];
      (prisma.note.findMany as jest.Mock).mockResolvedValueOnce(mockNotes);

      // Mock batch creation response
      const mockBatch = { id: 123, ...validRequestBody };
      (prisma.generationBatch.create as jest.Mock).mockResolvedValueOnce(mockBatch);

      const { req, res } = createMocks({
        method: 'POST',
        body: validRequestBody,
      });

      await handler(req, res);

      // Check that the batch was created with correct data
      expect(prisma.generationBatch.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          adminId: 1, // Converted from string '1'
          yearId: validRequestBody.yearId,
          subjectId: validRequestBody.subjectId,
          unitId: validRequestBody.unitId,
          questionTypes: validRequestBody.questionTypes,
          numQuestions: validRequestBody.numQuestions,
          provider: validRequestBody.provider,
          modelUsed: validRequestBody.model,
          language: validRequestBody.language,
          status: 'PENDING',
          notes: {
            connect: mockNotes.map(note => ({ id: note.id })),
          },
          metadata: { tpDistribution: validRequestBody.tpDistribution }
        })
      });

      // Check that the queue file was created
      expect(fs.writeFile).toHaveBeenCalledWith(
        '/app/queue/generation-123.json',
        JSON.stringify({
          batchId: 123,
          provider: validRequestBody.provider,
          language: validRequestBody.language,
          tpDistribution: validRequestBody.tpDistribution
        }),
        'utf-8'
      );

      // Check response
      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({ ok: true, batchId: 123 });
    });

    it('should use default values when optional fields are not provided', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock notes response
      const mockNotes = [{ id: 1 }];
      (prisma.note.findMany as jest.Mock).mockResolvedValueOnce(mockNotes);

      // Mock batch creation response
      const mockBatch = { id: 456 };
      (prisma.generationBatch.create as jest.Mock).mockResolvedValueOnce(mockBatch);

      // Request with minimal required fields
      const minimalRequest = {
        yearId: 1,
        subjectId: 2,
        questionTypes: [QuestionType.MULTIPLE_CHOICE],
        model: 'anthropic/claude-3-opus:beta',
      };

      const { req, res } = createMocks({
        method: 'POST',
        body: minimalRequest,
      });

      await handler(req, res);

      // Check that default values were used
      expect(prisma.generationBatch.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          numQuestions: 10, // Default
          provider: 'openrouter', // Default
          language: Language.ZH, // Default
        })
      });

      expect(res._getStatusCode()).toBe(200);
    });
  });

  describe('Error Handling', () => {
    it('should return 500 if an error occurs', async () => {
      // Mock admin session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockAdminSession);

      // Mock notes response
      (prisma.note.findMany as jest.Mock).mockResolvedValueOnce([{ id: 1 }]);

      // Mock error in batch creation
      (prisma.generationBatch.create as jest.Mock).mockRejectedValueOnce(new Error('Database error'));

      const { req, res } = createMocks({
        method: 'POST',
        body: validRequestBody,
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(500);
      expect(JSON.parse(res._getData())).toEqual({
        message: 'Internal server error',
        error: 'Error: Database error'
      });
    });
  });

  it('should return 405 for unsupported methods', async () => {
    const { req, res } = createMocks({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Method not allowed' });
  });
});
