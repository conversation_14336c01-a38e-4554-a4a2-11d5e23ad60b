import React, { useState } from 'react';
import Head from 'next/head';

interface Question {
  id: number;
  questionId?: string;
  promptEn?: string;
  promptZh?: string;
  choices?: Array<{
    id: number;
    key: string;
    textEn: string;
    textZh: string;
  }>;
  answer?: {
    key?: string;
    textEn?: string;
    textZh?: string;
  };
  explanation?: {
    textEn?: string;
    textZh?: string;
  };
}

export default function TestHint() {
  const [questionId, setQuestionId] = useState(1);
  const [hint, setHint] = useState<string | null>(null);
  const [question, setQuestion] = useState<Question | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setQuestion(null);
    setHint(null);

    try {
      // Use the improved test-hint API with the question ID
      const testRes = await fetch(`/api/test-hint?questionId=${questionId}`);

      if (!testRes.ok) {
        throw new Error(`Test API error: ${testRes.status} ${testRes.statusText}`);
      }

      const testData = await testRes.json();
      console.log('Test hint API response:', testData);

      // Set both the question and hint from the response
      setQuestion(testData.question || null);
      setHint(testData.hint || 'No hint provided');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      // If there's an error, try the fallback
      try {
        const fallbackData = await fetch('/api/test-hint').then(r => r.json());
        setHint('Using fallback: ' + fallbackData.hint);
        setQuestion(fallbackData.question || null);
      } catch (fallbackErr) {
        console.error('Fallback also failed:', fallbackErr);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <Head>
        <title>Test Hint</title>
      </Head>

      <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-6">Test Hint API</h1>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Question ID
            </label>
            <input
              type="number"
              value={questionId}
              onChange={(e) => setQuestionId(Number(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded"
              required
              min="1"
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full py-2 px-4 rounded font-medium text-white ${
              loading ? 'bg-gray-400' : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {loading ? 'Loading...' : 'Get Hint'}
          </button>
        </form>

        {error && (
          <div className="mt-4 p-3 bg-red-100 border border-red-300 text-red-700 rounded">
            <strong>Error:</strong> {error}
          </div>
        )}

        {question && (
          <div className="mt-6">
            <h2 className="text-lg font-semibold mb-2">Question:</h2>
            <div className="p-4 bg-blue-50 rounded border border-blue-200 mb-4">
              <p className="font-medium">ID: {question.id}</p>
              {question.questionId && <p className="text-sm text-gray-500">Question ID: {question.questionId}</p>}
              <div className="mt-2">
                <p className="font-medium">English:</p>
                <p>{question.promptEn || 'Not available'}</p>
              </div>
              {question.promptZh && (
                <div className="mt-2">
                  <p className="font-medium">Chinese:</p>
                  <p>{question.promptZh}</p>
                </div>
              )}

              {question.choices && question.choices.length > 0 && (
                <div className="mt-3">
                  <p className="font-medium">Choices:</p>
                  <ul className="list-disc pl-5 mt-1">
                    {question.choices.map((choice) => (
                      <li key={choice.id}>
                        {choice.key}: {choice.textEn || choice.textZh}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {question.answer && (
                <div className="mt-3">
                  <p className="font-medium">Answer:</p>
                  <p>
                    {question.answer.key
                      ? `${question.answer.key}: ${question.answer.textEn || question.answer.textZh || ''}`
                      : question.answer.textEn || question.answer.textZh || 'Not available'}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {hint && (
          <div className="mt-4">
            <h2 className="text-lg font-semibold mb-2">Generated Hint:</h2>
            <div className="p-4 bg-green-50 rounded border border-green-200">
              {hint}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
