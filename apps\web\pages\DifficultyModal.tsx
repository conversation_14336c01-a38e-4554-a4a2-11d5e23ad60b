import React from 'react';
import { XIcon } from 'lucide-react';

export interface DifficultyOption {
  label: string;
  value: string;
  tpLevels: number[];
  description: string;
}

interface DifficultyModalProps {
  onSelect: (difficulty: DifficultyOption) => void;
  onClose: () => void;
  unit: {
    name: string;
    subject: string;
  };
}

export const DifficultyModal: React.FC<DifficultyModalProps> = ({
  onSelect,
  onClose,
  unit
}) => {
  const difficulties: DifficultyOption[] = [
    {
      label: 'Very Easy',
      value: 'very-easy',
      tpLevels: [1],
      description: 'TP1 level questions'
    },
    {
      label: 'Easy',
      value: 'easy',
      tpLevels: [2],
      description: 'TP2 level questions'
    },
    {
      label: 'Medium',
      value: 'medium',
      tpLevels: [3, 4],
      description: 'TP3-TP4 level questions'
    },
    {
      label: 'Expert',
      value: 'expert',
      tpLevels: [5, 6],
      description: 'TP5-TP6 level questions'
    },
    {
      label: 'Mix It',
      value: 'mix',
      tpLevels: [1, 2, 3, 4, 5, 6],
      description: 'Questions from all TP levels'
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full p-6 shadow-xl">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-[#0D0D0D]">
              Select Difficulty
            </h2>
            <p className="text-sm text-gray-600">
              {unit.subject} - {unit.name}
            </p>
          </div>
          <button onClick={onClose} className="p-1">
            <XIcon size={24} />
          </button>
        </div>
        <div className="grid grid-cols-1 gap-4 mb-6">
          {difficulties.map(difficulty => (
            <button
              key={difficulty.value}
              onClick={() => onSelect(difficulty)}
              className="p-4 rounded-lg font-bold text-left transition-all
                hover:bg-[#04B2D9] hover:text-white
                bg-white border-2 border-[#04B2D9] text-[#04B2D9]"
            >
              <div className="flex flex-col">
                <span className="text-lg">{difficulty.label}</span>
                <span className="text-xs font-normal mt-1">{difficulty.description}</span>
              </div>
            </button>
          ))}
        </div>
        <p className="text-center text-sm text-gray-600">
          Choose a difficulty level to begin practice
        </p>
      </div>
    </div>
  );
};