import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';

/**
 * API endpoint for fetching child data
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    // Get the child ID from the session
    const childId = session.user?.id;

    if (!childId) {
      return res.status(400).json({ message: 'Child ID not found in session' });
    }

    // Fetch the child data
    const child = await prisma.child.findUnique({
      where: {
        id: Number(childId),
      },
    });

    if (!child) {
      return res.status(404).json({ message: 'Child not found' });
    }

    // Get the year ID by extracting the year number from the year string
    let yearId = null;

    if (child.year) {
      // Extract year number from the year string (e.g., "Year 5" -> 5)
      const yearMatch = child.year.match(/\d+/);
      const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;

      if (yearNumber) {
        // Try to find the year by yearNumber
        const year = await prisma.year.findFirst({
          where: {
            yearNumber: yearNumber,
          },
        });

        if (year) {
          yearId = year.id;
        }
      }
    }

    // Return the child data with yearId
    res.status(200).json({
      id: child.id,
      name: child.name,
      year: child.year,
      yearId: yearId || null,
      username: child.username,
    });
  } catch (error) {
    console.error('Error fetching child data:', error);
    res.status(500).json({ message: 'Failed to fetch child data' });
  }
}
