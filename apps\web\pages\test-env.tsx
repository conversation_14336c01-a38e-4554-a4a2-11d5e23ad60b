import { GetServerSideProps, NextPage } from 'next';
import { useEffect } from 'react';

interface Props {
  serverEnv: {
    FEATURE_ADAPTIVE_V2: string;
  };
}

const TestEnvPage: NextPage<Props> = ({ serverEnv }) => {
  useEffect(() => {
    console.log('Client-side env:', {
      NEXT_PUBLIC_FEATURE_ADAPTIVE_V2: process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2,
    });
  }, []);

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Environment Variables Test</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Server-side Environment Variables</h2>
        <div className="bg-gray-100 p-4 rounded">
          <p><strong>FEATURE_ADAPTIVE_V2:</strong> {serverEnv.FEATURE_ADAPTIVE_V2}</p>
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-semibold mb-2">Client-side Environment Variables</h2>
        <div className="bg-gray-100 p-4 rounded">
          <p><strong>NEXT_PUBLIC_FEATURE_ADAPTIVE_V2:</strong> {process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2}</p>
        </div>
        <p className="mt-2 text-sm text-gray-600">Check the browser console for client-side environment variables.</p>
      </div>
    </div>
  );
};

export const getServerSideProps: GetServerSideProps = async () => {
  console.log('Server-side env:', {
    FEATURE_ADAPTIVE_V2: process.env.FEATURE_ADAPTIVE_V2,
  });

  return {
    props: {
      serverEnv: {
        FEATURE_ADAPTIVE_V2: process.env.FEATURE_ADAPTIVE_V2 || 'not set',
      },
    },
  };
};

export default TestEnvPage;
