# Authentication API Endpoints

This document provides documentation for the authentication-related API endpoints in the My Quiz App application.

## NextAuth.js

The application uses NextAuth.js for authentication, which provides a set of API endpoints for handling authentication.

### Main Authentication Endpoint

- **URL**: `/api/auth/[...nextauth]`
- **Methods**: Various (handled by NextAuth.js)
- **Authentication Required**: Varies by operation
- **Role Required**: None

This is the main NextAuth.js endpoint that handles various authentication operations, including:

- Sign in
- Sign out
- Session management
- JWT token handling

## Sign In

### Sign In with Credentials

Signs in a user with credentials (email/password for parents/admins or username/pin for children).

- **URL**: `/api/auth/signin`
- **Method**: `POST`
- **Authentication Required**: No
- **Role Required**: None

#### Request Body for Parent/Admin Login

```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "csrfToken": "csrf_token_value"
}
```

#### Request Body for Child Login

```json
{
  "username": "childusername",
  "pin": "1234",
  "csrfToken": "csrf_token_value"
}
```

#### Response

On successful authentication, the server will:

1. Set HTTP cookies for the session
2. Redirect to the callback URL or the default URL

## Sign Out

### Sign Out

Signs out the currently authenticated user.

- **URL**: `/api/auth/signout`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: None

#### Request Body

```json
{
  "csrfToken": "csrf_token_value"
}
```

#### Response

On successful sign out, the server will:

1. Clear HTTP cookies for the session
2. Redirect to the sign-in page or the default URL

## Session

### Get Session

Retrieves the current session information.

- **URL**: `/api/auth/session`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None

#### Response for Authenticated User

```json
{
  "user": {
    "id": "1",
    "name": "User Name",
    "email": "<EMAIL>",
    "role": "PARENT",
    "image": null
  },
  "expires": "2023-01-31T00:00:00.000Z"
}
```

#### Response for Unauthenticated User

```json
{
  "user": null,
  "expires": "2023-01-01T00:00:00.000Z"
}
```

## CSRF Token

### Get CSRF Token

Retrieves a CSRF token for form submissions.

- **URL**: `/api/auth/csrf`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None

#### Response

```json
{
  "csrfToken": "csrf_token_value"
}
```

## Providers

### Get Providers

Retrieves the available authentication providers.

- **URL**: `/api/auth/providers`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None

#### Response

```json
{
  "credentials": {
    "id": "credentials",
    "name": "Credentials",
    "type": "credentials",
    "signinUrl": "/api/auth/signin/credentials",
    "callbackUrl": "/api/auth/callback/credentials"
  }
}
```

## Implementation Details

The authentication system is implemented in `/api/auth/[...nextauth].ts` with the following key features:

- **Providers**: Credentials provider for email/password and username/pin authentication
- **Session Strategy**: JWT-based session management
- **Session Duration**: 30 days
- **Custom Pages**: Custom sign-in page at `/login`
- **Secret**: Uses `NEXTAUTH_SECRET` environment variable for signing and encrypting tokens

### Authentication Logic

- **Parent/Admin Login**: Verifies email and password against the `Account` table
- **Child Login**: Verifies username and pin against the `Child` table
- **Account Status**: Checks that the account status is `ACTIVE` before allowing authentication
- **Password/PIN Verification**: Uses secure password hashing and verification

### Session Data

The session includes the following user data:

- **id**: User ID (string)
- **name**: User's name
- **email**: User's email (for parents/admins)
- **username**: User's username (for children)
- **role**: User's role (ADMIN, PARENT, or CHILD)
- **parentId**: Parent's ID (for children, if applicable)
