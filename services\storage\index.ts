import { LocalDiskStorage } from './LocalDiskStorage';
import { StorageDriver } from './StorageDriver';

/**
 * Factory function to get the appropriate storage driver based on environment configuration
 * Currently only supports local disk storage, but can be extended to support other providers
 */
function getStorageDriver(): StorageDriver {
  const driver = process.env.STORAGE_DRIVER || 'local';
  
  switch (driver.toLowerCase()) {
    case 'local':
    default:
      return new LocalDiskStorage();
    // Add more cases here for other storage providers (S3, Azure, etc.)
  }
}

// Export a singleton instance of the storage driver
export const storage = getStorageDriver();
