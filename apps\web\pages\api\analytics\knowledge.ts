import { NextApiRequest, NextApiResponse } from 'next';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { childId, timePeriod, subjectId, unitId } = req.query; // Optional query parameters

  try {
    let whereClause: any = {};
    if (childId) {
      whereClause.childId = parseInt(childId as string, 10);
    }
    if (subjectId) {
      whereClause.subjectId = parseInt(subjectId as string, 10);
    }
    if (unitId) {
      whereClause.unitId = parseInt(unitId as string, 10);
    }

    // Basic filtering by time period (can be expanded)
    if (timePeriod) {
      const now = new Date();
      let startDate: Date | undefined;
      switch (timePeriod) {
        case 'day':
          startDate = new Date(now.setDate(now.getDate() - 1));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          break;
      }
      if (startDate) {
        whereClause.startTime = {
          gte: startDate,
        };
      }
    }

    // Wrap the database query in a try-catch to handle specific database errors
    let quizAttempts: any[] = [];
    try {
      quizAttempts = await prisma.quizAttempt.findMany({
        where: whereClause,
        include: {
          child: {
            select: {
              name: true,
            },
          },
          subject: {
            select: {
              name: true,
            },
          },
          unit: {
            select: {
              unitNumber: true,
              topicEn: true,
            },
          },
          studentAnswers: {
            select: {
              isCorrect: true,
              question: {
                select: {
                  id: true,
                  subTopicEn: true,
                  tpLevel: true,
                },
              },
            },
          },
        },
      });
    } catch (dbError) {
      console.error('Database error fetching quiz attempts:', dbError);
      return res.status(500).json({
        message: 'Database error fetching quiz attempts',
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      });
    }

    // Initialize aggregation data structures
    const attemptCount = quizAttempts.length;
    const subjectAttemptCounts: { [key: string]: number } = {};
    const unitAttemptCounts: { [key: string]: number } = {};
    const subjectSuccessRates: { [key: string]: { correct: number; total: number } } = {};
    const unitSuccessRates: { [key: string]: { correct: number; total: number } } = {};
    const tpLevelSuccessRates: { [key: number]: { correct: number; total: number } } = {};

    // Process each quiz attempt with null checks
    quizAttempts.forEach((attempt: any) => {
      // Skip attempts with missing required data
      if (!attempt.subject || !attempt.subject.name) {
        console.warn('Quiz attempt missing subject data:', attempt.id);
        return;
      }

      const subjectName = attempt.subject.name;

      // Handle case where unit might be null (optional relation)
      let unitKey = 'Unknown Unit';
      if (attempt.unit) {
        unitKey = `${attempt.unit.unitNumber} - ${attempt.unit.topicEn}`;
      }

      // Update attempt counts
      subjectAttemptCounts[subjectName] = (subjectAttemptCounts[subjectName] || 0) + 1;
      unitAttemptCounts[unitKey] = (unitAttemptCounts[unitKey] || 0) + 1;

      // Process student answers if they exist
      if (attempt.studentAnswers && attempt.studentAnswers.length > 0) {
        attempt.studentAnswers.forEach((answer: any) => {
          // Initialize success rate objects if needed
          if (!subjectSuccessRates[subjectName]) {
            subjectSuccessRates[subjectName] = { correct: 0, total: 0 };
          }
          if (!unitSuccessRates[unitKey]) {
            unitSuccessRates[unitKey] = { correct: 0, total: 0 };
          }

          // Get TP level if available
          const tpLevel = answer.question?.tpLevel || 0;
          if (tpLevel > 0) {
            if (!tpLevelSuccessRates[tpLevel]) {
              tpLevelSuccessRates[tpLevel] = { correct: 0, total: 0 };
            }

            // Update TP level totals
            tpLevelSuccessRates[tpLevel].total++;

            // Update TP level correct counts if answer is correct
            if (answer.isCorrect) {
              tpLevelSuccessRates[tpLevel].correct++;
            }
          }

          // Update totals
          subjectSuccessRates[subjectName].total++;
          unitSuccessRates[unitKey].total++;

          // Update correct counts if answer is correct
          if (answer.isCorrect) {
            subjectSuccessRates[subjectName].correct++;
            unitSuccessRates[unitKey].correct++;
          }
        });
      }
    });

    // Format success rates for response
    const formattedSubjectSuccessRates = Object.entries(subjectSuccessRates).map(([subject, data]) => ({
      subject,
      successRate: data.total > 0 ? (data.correct / data.total) * 100 : 0,
    }));

    const formattedUnitSuccessRates = Object.entries(unitSuccessRates).map(([unit, data]) => ({
      unit,
      successRate: data.total > 0 ? (data.correct / data.total) * 100 : 0,
    }));

    // Format TP level success rates
    const formattedTpLevelSuccessRates = Object.entries(tpLevelSuccessRates).map(([tpLevel, data]) => ({
      tpLevel: parseInt(tpLevel),
      successRate: data.total > 0 ? (data.correct / data.total) * 100 : 0,
      totalQuestions: data.total,
      correctAnswers: data.correct
    }));

    // Determine student's TP level based on performance
    let studentTpLevel = 0;
    if (formattedTpLevelSuccessRates.length > 0) {
      // Find the highest TP level where success rate is at least 70%
      const qualifyingLevels = formattedTpLevelSuccessRates
        .filter(level => level.successRate >= 70 && level.totalQuestions >= 5)
        .map(level => level.tpLevel);

      if (qualifyingLevels.length > 0) {
        studentTpLevel = Math.max(...qualifyingLevels);
      } else {
        // If no qualifying levels, use the highest level with any success
        const anySuccessLevels = formattedTpLevelSuccessRates
          .filter(level => level.correctAnswers > 0)
          .map(level => level.tpLevel);

        if (anySuccessLevels.length > 0) {
          studentTpLevel = Math.min(...anySuccessLevels);
        } else {
          studentTpLevel = 1; // Default to level 1 if no success
        }
      }
    }

    // Return the aggregated data
    res.status(200).json({
      attemptCount,
      subjectAttemptCounts,
      unitAttemptCounts,
      subjectSuccessRates: formattedSubjectSuccessRates,
      unitSuccessRates: formattedUnitSuccessRates,
      tpLevelSuccessRates: formattedTpLevelSuccessRates,
      studentTpLevel,
      // Exclude raw quiz attempts data to reduce payload size
      // quizAttempts,
    });

  } catch (error) {
    console.error('Error fetching knowledge analytics:', error);
    res.status(500).json({
      message: 'Error fetching knowledge analytics',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('Error disconnecting from database:', disconnectError);
    }
  }
}
