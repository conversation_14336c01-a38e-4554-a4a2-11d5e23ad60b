# Augment Code Rules for My Quiz App

## Core Principles

### Never place code in repo root
- All application code belongs in `apps/` or `packages/` directories
- Scripts go in `scripts/` directory
- Documentation goes in `docs/` directory
- Configuration files (package.json, tsconfig.json, etc.) are allowed in root

### Use "@quiz/db" for Prisma access
```typescript
// ✅ Correct
import { prisma } from '@quiz/db';
import { QuestionType, Language } from '@quiz/db';

// ❌ Wrong
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
```

### Environment variables must be read via process.env or NEXT_PUBLIC_ prefix
```typescript
// ✅ Correct - Server-side
const apiKey = process.env.OPENROUTER_API_KEY;
const dbUrl = process.env.DATABASE_URL;

// ✅ Correct - Client-side (Next.js)
const featureFlag = process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2;

// ❌ Wrong - Direct access without process.env
const apiKey = OPENROUTER_API_KEY;
```

### All new migrations in packages/db/prisma/migrations
- Never modify existing migration files
- Use `npm run migrate` to create new migrations
- Always test migrations on development database first

### Use logger from '@quiz/core/logger'
```typescript
// ✅ Correct
import { logger } from '@quiz/core/logger';
logger.info('Quiz attempt started', { quizId, userId });
logger.error('Database error', { error: error.message });

// ❌ Wrong
console.log('Quiz attempt started');
console.error('Database error', error);
```

## Project Structure Rules

### Apps Directory (`apps/`)
- `apps/web/` - Next.js web application (port 3000)
- `apps/api/` - Fastify API server (port 4000)
- Each app has its own package.json and dependencies

### Packages Directory (`packages/`)
- `packages/db/` - Shared Prisma database package
- `packages/core/` - Shared utilities, logger, API client
- Packages are shared across apps using workspace references

### API Patterns

#### Next.js API Routes (`apps/web/pages/api/`)
```typescript
// ✅ Correct pattern
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';
import { prisma } from '@quiz/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    // Your logic here
    res.status(200).json({ success: true });
  } catch (error) {
    res.status(500).json({ message: 'Internal server error' });
  }
}
```

#### Fastify API Routes (`apps/api/src/`)
```typescript
// ✅ Correct pattern
import { FastifyInstance } from 'fastify';
import { prisma } from '@quiz/db';
import { logger } from '@quiz/core/logger';

export async function quizRoutes(fastify: FastifyInstance) {
  fastify.get('/quiz/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const quiz = await prisma.quiz.findUnique({ where: { id } });
      return quiz;
    } catch (error) {
      fastify.log.error(error);
      reply.code(500).send({ message: 'Internal server error' });
    }
  });
}
```

## Database Rules

### Prisma Schema (`packages/db/prisma/schema.prisma`)
- All models use `@@map("TG_TableName")` for table names
- Use proper TypeScript types for enums
- Include proper indexes for performance

### Database Queries
```typescript
// ✅ Correct - Use shared prisma instance
import { prisma } from '@quiz/db';

const questions = await prisma.question.findMany({
  where: { unitId },
  include: { choices: true }
});

// ❌ Wrong - Creating new Prisma instance
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();
```

## Component Rules

### React Components (`apps/web/components/`)
```typescript
// ✅ Correct component structure
import React from 'react';
import { logger } from '@quiz/core/logger';

interface QuizComponentProps {
  quizId: string;
  onComplete: (score: number) => void;
}

export const QuizComponent: React.FC<QuizComponentProps> = ({ quizId, onComplete }) => {
  // Component logic
  return <div>Quiz content</div>;
};
```

### Custom Hooks (`apps/web/hooks/`)
```typescript
// ✅ Correct hook pattern
import { useState, useEffect } from 'react';
import useSWR from 'swr';

export const useQuizData = (quizId: string) => {
  const { data, error, mutate } = useSWR(
    quizId ? `/api/quiz/${quizId}` : null,
    fetcher
  );

  return {
    quiz: data,
    isLoading: !error && !data,
    isError: error,
    refresh: mutate
  };
};
```

## Testing Rules

### Test Files
- Place tests in `__tests__/` directories adjacent to tested code
- Use `.test.ts` or `.test.tsx` extensions
- Import from proper packages

```typescript
// ✅ Correct test structure
import { render, screen } from '@testing-library/react';
import { QuizComponent } from '../QuizComponent';

describe('QuizComponent', () => {
  it('renders quiz questions', () => {
    render(<QuizComponent quizId="123" onComplete={jest.fn()} />);
    expect(screen.getByText('Question 1')).toBeInTheDocument();
  });
});
```

## Error Handling

### API Error Responses
```typescript
// ✅ Correct error handling
try {
  const result = await someOperation();
  res.status(200).json({ data: result });
} catch (error) {
  logger.error('Operation failed', { error: error.message, userId });
  res.status(500).json({ 
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
}
```

### Client Error Handling
```typescript
// ✅ Correct client error handling
const { data, error } = useSWR('/api/quiz', fetcher);

if (error) {
  return <div>Error loading quiz: {error.message}</div>;
}

if (!data) {
  return <div>Loading...</div>;
}
```

## Security Rules

### Authentication Checks
```typescript
// ✅ Always check authentication for protected routes
const session = await getServerSession(req, res, authOptions);
if (!session) {
  return res.status(401).json({ message: 'Unauthorized' });
}
```

### Input Validation
```typescript
// ✅ Validate all inputs
const { questionId, answer } = req.body;

if (!questionId || !answer) {
  return res.status(400).json({ message: 'Missing required fields' });
}
```

## Performance Rules

### Database Queries
```typescript
// ✅ Use proper includes and selects
const quiz = await prisma.quiz.findUnique({
  where: { id },
  include: {
    questions: {
      include: { choices: true }
    }
  }
});

// ❌ Avoid N+1 queries
const questions = await prisma.question.findMany({ where: { quizId } });
for (const question of questions) {
  const choices = await prisma.choice.findMany({ where: { questionId: question.id } });
}
```

### Client-Side Data Fetching
```typescript
// ✅ Use SWR for caching
const { data } = useSWR('/api/quiz', fetcher);

// ✅ Implement proper loading states
if (!data) return <LoadingSpinner />;
```

## Deployment Rules

### Environment Variables
- Never commit `.env` files
- Use `.env.example` for documentation
- Validate required environment variables on startup

### Build Process
```bash
# ✅ Use turbo for builds
npm run build  # Uses turbo to build all packages

# ✅ Run tests before deployment
npm test
```

## Common Patterns to Follow

### Feature Flags
```typescript
// ✅ Check feature flags properly
const adaptiveV2Enabled = process.env.NEXT_PUBLIC_FEATURE_ADAPTIVE_V2 === 'on';

if (adaptiveV2Enabled) {
  // New adaptive logic
} else {
  // Legacy logic
}
```

### Logging
```typescript
// ✅ Structured logging
logger.info('Quiz completed', {
  quizId,
  userId,
  score,
  duration: Date.now() - startTime
});
```

### Type Safety
```typescript
// ✅ Use proper TypeScript types
interface QuizAttempt {
  id: string;
  quizId: string;
  userId: string;
  score: number;
  completedAt: Date;
}

// ✅ Use Prisma generated types
import { Question, QuestionType } from '@quiz/db';
```

Remember: Always prioritize code quality, type safety, and maintainability. When in doubt, follow existing patterns in the codebase.
