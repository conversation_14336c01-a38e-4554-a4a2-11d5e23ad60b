import React from 'react';
import { render, screen, fireEvent, act, waitFor } from '@testing-library/react';
import { useTranslateBubble } from '../../../hooks/useTranslateBubble';
import { QuizProvider, useQuiz } from '../../../components/quiz/QuizContext';
import { useSession } from 'next-auth/react';

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn().mockImplementation(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ translatedText: 'Translated text' }),
  })
);

// Create a test component that uses the hook
const TestComponent = ({ enabled = true }) => {
  const { isVisible, handleTranslate } = useTranslateBubble(enabled);
  
  return (
    <div>
      <div data-testid="test-text">This is a test text that can be translated</div>
      {isVisible && (
        <button data-testid="translate-button" onClick={handleTranslate}>
          Translate
        </button>
      )}
    </div>
  );
};

// Create a component that uses both the hook and the QuizContext
const TestWithQuizContext = ({ enabled = true }) => {
  const { aiTutorMessages } = useQuiz();
  const { isVisible, handleTranslate } = useTranslateBubble(enabled);
  
  return (
    <div>
      <div data-testid="test-text">This is a test text that can be translated</div>
      {isVisible && (
        <button data-testid="translate-button" onClick={handleTranslate}>
          Translate
        </button>
      )}
      <div data-testid="ai-tutor-messages">
        {aiTutorMessages.map((msg, index) => (
          <div key={index} data-testid={`message-${index}`}>
            {msg.content}
          </div>
        ))}
      </div>
    </div>
  );
};

describe('useTranslateBubble', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock authenticated session
    (useSession as jest.Mock).mockReturnValue({
      data: { user: { id: '1' } },
      status: 'authenticated',
    });
    
    // Mock window.getSelection
    const mockGetSelection = jest.fn().mockImplementation(() => ({
      toString: () => 'Selected text',
      getRangeAt: () => ({
        getBoundingClientRect: () => ({
          left: 100,
          top: 100,
          width: 100,
        }),
      }),
    }));
    
    window.getSelection = mockGetSelection;
    
    // Mock CustomEvent
    global.CustomEvent = class CustomEvent extends Event {
      detail: any;
      constructor(name: string, options?: any) {
        super(name);
        this.detail = options?.detail;
      }
    };
  });
  
  it('should show translation bubble when text is selected', () => {
    render(<TestComponent />);
    
    // Simulate text selection
    fireEvent.mouseUp(screen.getByTestId('test-text'));
    
    // Check if the translation button appears
    expect(screen.getByTestId('translate-button')).toBeInTheDocument();
  });
  
  it('should call fetch when translate button is clicked', async () => {
    render(<TestComponent />);
    
    // Simulate text selection
    fireEvent.mouseUp(screen.getByTestId('test-text'));
    
    // Click the translate button
    fireEvent.click(screen.getByTestId('translate-button'));
    
    // Check if fetch was called
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/ai-translator',
        expect.objectContaining({
          method: 'POST',
          headers: {'Content-Type': 'application/json'},
        })
      );
    });
  });
  
  it('should dispatch translationComplete event when translation is successful', async () => {
    // Spy on window.dispatchEvent
    const dispatchEventSpy = jest.spyOn(window, 'dispatchEvent');
    
    render(<TestComponent />);
    
    // Simulate text selection
    fireEvent.mouseUp(screen.getByTestId('test-text'));
    
    // Click the translate button
    fireEvent.click(screen.getByTestId('translate-button'));
    
    // Check if the event was dispatched
    await waitFor(() => {
      expect(dispatchEventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'translationComplete',
          detail: expect.objectContaining({
            translatedText: 'Translated text',
            originalText: 'Selected text',
          }),
        })
      );
    });
  });
  
  it('should add translation to AI tutor messages', async () => {
    // Create a mock implementation of QuizProvider that tracks messages
    const mockAddAiTutorMessage = jest.fn();
    const mockMessages: any[] = [];
    
    jest.mock('../../../components/quiz/QuizContext', () => ({
      useQuiz: () => ({
        aiTutorMessages: mockMessages,
        addAiTutorMessage: mockAddAiTutorMessage,
        displayLanguage: 'en',
        setIsAiTutorCollapsed: jest.fn(),
      }),
      QuizProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    }));
    
    render(
      <QuizProvider>
        <TestWithQuizContext />
      </QuizProvider>
    );
    
    // Simulate text selection
    fireEvent.mouseUp(screen.getByTestId('test-text'));
    
    // Click the translate button
    fireEvent.click(screen.getByTestId('translate-button'));
    
    // Wait for the translation to complete and event to be dispatched
    await act(async () => {
      // Manually dispatch the event that would normally be dispatched by the hook
      const event = new CustomEvent('translationComplete', {
        detail: {
          translatedText: 'Translated text',
          originalText: 'Selected text',
        },
      });
      window.dispatchEvent(event);
    });
    
    // Check if the translation was added to the AI tutor messages
    // This is a bit tricky to test since we're mocking the context
    // In a real app, we'd need to check if the addAiTutorMessage was called
    expect(mockAddAiTutorMessage).toHaveBeenCalled();
  });
});
