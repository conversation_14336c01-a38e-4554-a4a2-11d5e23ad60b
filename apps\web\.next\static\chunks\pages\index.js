/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/index"],{

/***/ "(pages-dir-browser)/../../node_modules/.prisma/client/index-browser.js":
/*!**********************************************************!*\
  !*** ../../node_modules/.prisma/client/index-browser.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nconst {\n  Decimal,\n  objectEnumValues,\n  makeStrictEnum,\n  Public,\n  getRuntime,\n  skip\n} = __webpack_require__(/*! ./runtime/index-browser.js */ \"(pages-dir-browser)/../../node_modules/.prisma/client/runtime/index-browser.js\")\n\n\nconst Prisma = {}\n\nexports.Prisma = Prisma\nexports.$Enums = {}\n\n/**\n * Prisma Client JS version: 6.6.0\n * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a\n */\nPrisma.prismaVersion = {\n  client: \"6.6.0\",\n  engine: \"f676762280b54cd07c770017ed3711ddde35f37a\"\n}\n\nPrisma.PrismaClientKnownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)};\nPrisma.PrismaClientUnknownRequestError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientRustPanicError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientInitializationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.PrismaClientValidationError = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.Decimal = Decimal\n\n/**\n * Re-export of sql-template-tag\n */\nPrisma.sql = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.empty = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.join = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.raw = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.validator = Public.validator\n\n/**\n* Extensions\n*/\nPrisma.getExtensionContext = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\nPrisma.defineExtension = () => {\n  const runtimeName = getRuntime().prettyName;\n  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).\nIn case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,\n)}\n\n/**\n * Shorthand utilities for JSON filtering\n */\nPrisma.DbNull = objectEnumValues.instances.DbNull\nPrisma.JsonNull = objectEnumValues.instances.JsonNull\nPrisma.AnyNull = objectEnumValues.instances.AnyNull\n\nPrisma.NullTypes = {\n  DbNull: objectEnumValues.classes.DbNull,\n  JsonNull: objectEnumValues.classes.JsonNull,\n  AnyNull: objectEnumValues.classes.AnyNull\n}\n\n\n\n/**\n * Enums\n */\n\nexports.Prisma.TransactionIsolationLevel = makeStrictEnum({\n  ReadUncommitted: 'ReadUncommitted',\n  ReadCommitted: 'ReadCommitted',\n  RepeatableRead: 'RepeatableRead',\n  Serializable: 'Serializable'\n});\n\nexports.Prisma.SubjectScalarFieldEnum = {\n  id: 'id',\n  name: 'name'\n};\n\nexports.Prisma.YearScalarFieldEnum = {\n  id: 'id',\n  yearNumber: 'yearNumber'\n};\n\nexports.Prisma.UnitScalarFieldEnum = {\n  id: 'id',\n  unitNumber: 'unitNumber',\n  topicEn: 'topicEn',\n  topicZh: 'topicZh',\n  topicMs: 'topicMs',\n  subjectId: 'subjectId',\n  yearId: 'yearId'\n};\n\nexports.Prisma.QuestionScalarFieldEnum = {\n  id: 'id',\n  questionId: 'questionId',\n  type: 'type',\n  promptEn: 'promptEn',\n  promptZh: 'promptZh',\n  promptMs: 'promptMs',\n  promptMediaId: 'promptMediaId',\n  spec: 'spec',\n  keywords: 'keywords',\n  tpLevel: 'tpLevel',\n  originalLanguage: 'originalLanguage',\n  translationState: 'translationState',\n  status: 'status',\n  subjectId: 'subjectId',\n  yearId: 'yearId',\n  unitId: 'unitId',\n  subTopicEn: 'subTopicEn',\n  subTopicZh: 'subTopicZh',\n  subTopicMs: 'subTopicMs',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt',\n  generationBatchId: 'generationBatchId'\n};\n\nexports.Prisma.MediaScalarFieldEnum = {\n  id: 'id',\n  url: 'url',\n  altEn: 'altEn',\n  altZh: 'altZh',\n  altMs: 'altMs',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.ChoiceScalarFieldEnum = {\n  id: 'id',\n  key: 'key',\n  textEn: 'textEn',\n  textZh: 'textZh',\n  textMs: 'textMs',\n  mediaId: 'mediaId',\n  questionId: 'questionId'\n};\n\nexports.Prisma.AnswerScalarFieldEnum = {\n  id: 'id',\n  questionId: 'questionId',\n  type: 'type',\n  key: 'key',\n  textEn: 'textEn',\n  textZh: 'textZh',\n  textMs: 'textMs',\n  answerSpec: 'answerSpec',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.ExplanationTextScalarFieldEnum = {\n  id: 'id',\n  questionId: 'questionId',\n  textEn: 'textEn',\n  textZh: 'textZh',\n  textMs: 'textMs'\n};\n\nexports.Prisma.AccountScalarFieldEnum = {\n  id: 'id',\n  email: 'email',\n  password: 'password',\n  password_hash: 'password_hash',\n  salt: 'salt',\n  role: 'role',\n  name: 'name',\n  status: 'status',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.ChildScalarFieldEnum = {\n  id: 'id',\n  name: 'name',\n  year: 'year',\n  username: 'username',\n  pin: 'pin',\n  pin_hash: 'pin_hash',\n  salt: 'salt',\n  quizLanguage: 'quizLanguage',\n  menuLanguage: 'menuLanguage',\n  showDualLanguage: 'showDualLanguage',\n  accountId: 'accountId'\n};\n\nexports.Prisma.LicenseScalarFieldEnum = {\n  id: 'id',\n  type: 'type',\n  duration: 'duration',\n  accountId: 'accountId'\n};\n\nexports.Prisma.TranslationLogScalarFieldEnum = {\n  id: 'id',\n  timestamp: 'timestamp',\n  translatedText: 'translatedText',\n  childId: 'childId',\n  questionId: 'questionId'\n};\n\nexports.Prisma.QuizAttemptScalarFieldEnum = {\n  id: 'id',\n  questionIds: 'questionIds',\n  startTime: 'startTime',\n  endTime: 'endTime',\n  score: 'score',\n  childId: 'childId',\n  subjectId: 'subjectId',\n  unitId: 'unitId',\n  currentQuestionIndex: 'currentQuestionIndex',\n  status: 'status',\n  quizType: 'quizType',\n  metadata: 'metadata'\n};\n\nexports.Prisma.StudentAnswerScalarFieldEnum = {\n  id: 'id',\n  submittedAt: 'submittedAt',\n  submittedKey: 'submittedKey',\n  submittedText: 'submittedText',\n  submittedJson: 'submittedJson',\n  isCorrect: 'isCorrect',\n  firstTryCorrect: 'firstTryCorrect',\n  quizType: 'quizType',\n  childId: 'childId',\n  questionId: 'questionId',\n  quizAttemptId: 'quizAttemptId'\n};\n\nexports.Prisma.StudentMasteryScalarFieldEnum = {\n  scope: 'scope',\n  scopeId: 'scopeId',\n  studentId: 'studentId',\n  currentTp: 'currentTp',\n  confidence: 'confidence',\n  computedAt: 'computedAt'\n};\n\nexports.Prisma.SettingScalarFieldEnum = {\n  id: 'id',\n  key: 'key',\n  value: 'value',\n  createdAt: 'createdAt',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.NoteScalarFieldEnum = {\n  id: 'id',\n  filename: 'filename',\n  fileUrl: 'fileUrl',\n  mimeType: 'mimeType',\n  fileSize: 'fileSize',\n  contentType: 'contentType',\n  yearId: 'yearId',\n  subjectId: 'subjectId',\n  unitId: 'unitId',\n  createdAt: 'createdAt'\n};\n\nexports.Prisma.GenerationBatchScalarFieldEnum = {\n  id: 'id',\n  adminId: 'adminId',\n  yearId: 'yearId',\n  subjectId: 'subjectId',\n  unitId: 'unitId',\n  questionTypes: 'questionTypes',\n  numQuestions: 'numQuestions',\n  provider: 'provider',\n  modelUsed: 'modelUsed',\n  language: 'language',\n  status: 'status',\n  jobId: 'jobId',\n  createdAt: 'createdAt',\n  completedAt: 'completedAt',\n  promptTokens: 'promptTokens',\n  completionTokens: 'completionTokens',\n  totalTokens: 'totalTokens',\n  metadata: 'metadata'\n};\n\nexports.Prisma.QuizConfigScalarFieldEnum = {\n  id: 'id',\n  mode: 'mode',\n  numQuestions: 'numQuestions',\n  questionTypes: 'questionTypes',\n  allowTranslate: 'allowTranslate',\n  allowHints: 'allowHints',\n  allowAiTutor: 'allowAiTutor',\n  reviewMissedQuestions: 'reviewMissedQuestions',\n  updatedAt: 'updatedAt'\n};\n\nexports.Prisma.HintLogScalarFieldEnum = {\n  id: 'id',\n  questionId: 'questionId',\n  childId: 'childId',\n  hint: 'hint',\n  timestamp: 'timestamp'\n};\n\nexports.Prisma.AiTutorLogScalarFieldEnum = {\n  id: 'id',\n  questionId: 'questionId',\n  childId: 'childId',\n  userQuestion: 'userQuestion',\n  aiResponse: 'aiResponse',\n  language: 'language',\n  timestamp: 'timestamp'\n};\n\nexports.Prisma.QuestionFlagScalarFieldEnum = {\n  id: 'id',\n  questionId: 'questionId',\n  childId: 'childId',\n  quizAttemptId: 'quizAttemptId',\n  submittedKey: 'submittedKey',\n  submittedText: 'submittedText',\n  submittedJson: 'submittedJson',\n  status: 'status',\n  reviewNotes: 'reviewNotes',\n  createdAt: 'createdAt',\n  reviewedAt: 'reviewedAt'\n};\n\nexports.Prisma.SortOrder = {\n  asc: 'asc',\n  desc: 'desc'\n};\n\nexports.Prisma.NullableJsonNullValueInput = {\n  DbNull: Prisma.DbNull,\n  JsonNull: Prisma.JsonNull\n};\n\nexports.Prisma.QueryMode = {\n  default: 'default',\n  insensitive: 'insensitive'\n};\n\nexports.Prisma.NullsOrder = {\n  first: 'first',\n  last: 'last'\n};\n\nexports.Prisma.JsonNullValueFilter = {\n  DbNull: Prisma.DbNull,\n  JsonNull: Prisma.JsonNull,\n  AnyNull: Prisma.AnyNull\n};\nexports.QuestionType = exports.$Enums.QuestionType = {\n  MULTIPLE_CHOICE: 'MULTIPLE_CHOICE',\n  MULTIPLE_CHOICE_IMAGE: 'MULTIPLE_CHOICE_IMAGE',\n  PICTURE_PROMPT: 'PICTURE_PROMPT',\n  FILL_IN_THE_BLANK: 'FILL_IN_THE_BLANK',\n  TRUE_FALSE: 'TRUE_FALSE',\n  SHORT_ANSWER: 'SHORT_ANSWER',\n  LONG_ANSWER: 'LONG_ANSWER',\n  MATCHING: 'MATCHING',\n  SEQUENCING: 'SEQUENCING'\n};\n\nexports.Language = exports.$Enums.Language = {\n  EN: 'EN',\n  ZH: 'ZH',\n  MS: 'MS'\n};\n\nexports.TranslationStatus = exports.$Enums.TranslationStatus = {\n  NONE: 'NONE',\n  PARTIAL: 'PARTIAL',\n  COMPLETE: 'COMPLETE'\n};\n\nexports.QuestionStatus = exports.$Enums.QuestionStatus = {\n  DRAFT: 'DRAFT',\n  LIVE: 'LIVE',\n  ARCHIVED: 'ARCHIVED'\n};\n\nexports.AnswerType = exports.$Enums.AnswerType = {\n  SINGLE_CHOICE: 'SINGLE_CHOICE',\n  MULTI_CHOICE: 'MULTI_CHOICE',\n  SHORT_TEXT: 'SHORT_TEXT',\n  TRUE_FALSE: 'TRUE_FALSE',\n  FILL_IN_THE_BLANK: 'FILL_IN_THE_BLANK',\n  MATCHING: 'MATCHING',\n  SEQUENCING: 'SEQUENCING',\n  LONG_TEXT_RUBRIC: 'LONG_TEXT_RUBRIC'\n};\n\nexports.Role = exports.$Enums.Role = {\n  PARENT: 'PARENT',\n  CHILD: 'CHILD',\n  TEACHER: 'TEACHER',\n  ADMIN: 'ADMIN'\n};\n\nexports.Status = exports.$Enums.Status = {\n  ACTIVE: 'ACTIVE',\n  PENDING: 'PENDING',\n  INACTIVE: 'INACTIVE'\n};\n\nexports.LicenseType = exports.$Enums.LicenseType = {\n  FREE_TRIAL: 'FREE_TRIAL',\n  STANDARD_PLAN: 'STANDARD_PLAN'\n};\n\nexports.QuizStatus = exports.$Enums.QuizStatus = {\n  ACTIVE: 'ACTIVE',\n  COMPLETED: 'COMPLETED',\n  CANCELED: 'CANCELED'\n};\n\nexports.QuizType = exports.$Enums.QuizType = {\n  MASTERY: 'MASTERY',\n  TEST: 'TEST',\n  QUICK: 'QUICK'\n};\n\nexports.NoteType = exports.$Enums.NoteType = {\n  PDF: 'PDF',\n  IMAGE: 'IMAGE',\n  TEXT: 'TEXT',\n  MARKDOWN: 'MARKDOWN',\n  SAMPLE: 'SAMPLE',\n  OTHER: 'OTHER'\n};\n\nexports.BatchStatus = exports.$Enums.BatchStatus = {\n  PENDING: 'PENDING',\n  IN_PROGRESS: 'IN_PROGRESS',\n  COMPLETED: 'COMPLETED',\n  FAILED: 'FAILED'\n};\n\nexports.QuizMode = exports.$Enums.QuizMode = {\n  MASTERY: 'MASTERY',\n  TEST: 'TEST'\n};\n\nexports.FlagStatus = exports.$Enums.FlagStatus = {\n  PENDING: 'PENDING',\n  REVIEWED: 'REVIEWED',\n  APPROVED: 'APPROVED',\n  REJECTED: 'REJECTED'\n};\n\nexports.Prisma.ModelName = {\n  Subject: 'Subject',\n  Year: 'Year',\n  Unit: 'Unit',\n  Question: 'Question',\n  Media: 'Media',\n  Choice: 'Choice',\n  Answer: 'Answer',\n  ExplanationText: 'ExplanationText',\n  Account: 'Account',\n  Child: 'Child',\n  License: 'License',\n  TranslationLog: 'TranslationLog',\n  QuizAttempt: 'QuizAttempt',\n  StudentAnswer: 'StudentAnswer',\n  StudentMastery: 'StudentMastery',\n  Setting: 'Setting',\n  Note: 'Note',\n  GenerationBatch: 'GenerationBatch',\n  QuizConfig: 'QuizConfig',\n  HintLog: 'HintLog',\n  AiTutorLog: 'AiTutorLog',\n  QuestionFlag: 'QuestionFlag'\n};\n\n/**\n * This is a stub Prisma Client that will error at runtime if called.\n */\nclass PrismaClient {\n  constructor() {\n    return new Proxy(this, {\n      get(target, prop) {\n        let message\n        const runtime = getRuntime()\n        if (runtime.isEdge) {\n          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:\n- Use Prisma Accelerate: https://pris.ly/d/accelerate\n- Use Driver Adapters: https://pris.ly/d/driver-adapters\n`;\n        } else {\n          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'\n        }\n\n        message += `\nIf this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`\n\n        throw new Error(message)\n      }\n    })\n  }\n}\n\nexports.PrismaClient = PrismaClient\n\nObject.assign(exports, Prisma)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.prisma/client/index-browser.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/.prisma/client/runtime/index-browser.js":
/*!******************************************************************!*\
  !*** ../../node_modules/.prisma/client/runtime/index-browser.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("var ne=Object.defineProperty;var We=Object.getOwnPropertyDescriptor;var Ge=Object.getOwnPropertyNames;var Je=Object.prototype.hasOwnProperty;var Xe=(e,n,i)=>n in e?ne(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i;var Ce=(e,n)=>{for(var i in n)ne(e,i,{get:n[i],enumerable:!0})},Ke=(e,n,i,t)=>{if(n&&typeof n==\"object\"||typeof n==\"function\")for(let r of Ge(n))!Je.call(e,r)&&r!==i&&ne(e,r,{get:()=>n[r],enumerable:!(t=We(n,r))||t.enumerable});return e};var Qe=e=>Ke(ne({},\"__esModule\",{value:!0}),e);var ie=(e,n,i)=>Xe(e,typeof n!=\"symbol\"?n+\"\":n,i);var yn={};Ce(yn,{Decimal:()=>je,Public:()=>ge,getRuntime:()=>Re,makeStrictEnum:()=>Oe,objectEnumValues:()=>Pe});module.exports=Qe(yn);var ge={};Ce(ge,{validator:()=>be});function be(...e){return n=>n}var te=Symbol(),me=new WeakMap,we=class{constructor(n){n===te?me.set(this,\"Prisma.\".concat(this._getName())):me.set(this,\"new Prisma.\".concat(this._getNamespace(),\".\").concat(this._getName(),\"()\"))}_getName(){return this.constructor.name}toString(){return me.get(this)}},G=class extends we{_getNamespace(){return\"NullTypes\"}},J=class extends G{constructor(){super(...arguments);ie(this,\"_brand_DbNull\")}};Ne(J,\"DbNull\");var X=class extends G{constructor(){super(...arguments);ie(this,\"_brand_JsonNull\")}};Ne(X,\"JsonNull\");var K=class extends G{constructor(){super(...arguments);ie(this,\"_brand_AnyNull\")}};Ne(K,\"AnyNull\");var Pe={classes:{DbNull:J,JsonNull:X,AnyNull:K},instances:{DbNull:new J(te),JsonNull:new X(te),AnyNull:new K(te)}};function Ne(e,n){Object.defineProperty(e,\"name\",{value:n,configurable:!0})}var Ye=new Set([\"toJSON\",\"$$typeof\",\"asymmetricMatch\",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Oe(e){return new Proxy(e,{get(n,i){if(i in n)return n[i];if(!Ye.has(i))throw new TypeError(\"Invalid enum value: \".concat(String(i)))}})}var xe=()=>{var e,n;return((n=(e=globalThis.process)==null?void 0:e.release)==null?void 0:n.name)===\"node\"},ze=()=>{var e,n;return!!globalThis.Bun||!!((n=(e=globalThis.process)==null?void 0:e.versions)!=null&&n.bun)},ye=()=>!!globalThis.Deno,en=()=>typeof globalThis.Netlify==\"object\",nn=()=>typeof globalThis.EdgeRuntime==\"object\",tn=()=>{var e;return((e=globalThis.navigator)==null?void 0:e.userAgent)===\"Cloudflare-Workers\"};function rn(){var i;return(i=[[en,\"netlify\"],[nn,\"edge-light\"],[tn,\"workerd\"],[ye,\"deno\"],[ze,\"bun\"],[xe,\"node\"]].flatMap(t=>t[0]()?[t[1]]:[]).at(0))!=null?i:\"\"}var sn={node:\"Node.js\",workerd:\"Cloudflare Workers\",deno:\"Deno and Deno Deploy\",netlify:\"Netlify Edge Functions\",\"edge-light\":\"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)\"};function Re(){let e=rn();return{id:e,prettyName:sn[e]||e,isEdge:[\"workerd\",\"deno\",\"netlify\",\"edge-light\"].includes(e)}}var V=9e15,H=1e9,ve=\"0123456789abcdef\",oe=\"2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058\",ue=\"3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789\",Ee={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-V,maxE:V,crypto:!1},Te,Z,w=!0,ce=\"[DecimalError] \",$=ce+\"Invalid argument: \",De=ce+\"Precision limit exceeded\",Fe=ce+\"crypto unavailable\",Le=\"[object Decimal]\",R=Math.floor,C=Math.pow,on=/^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,un=/^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,fn=/^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,Ie=/^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,D=1e7,m=7,cn=9007199254740991,ln=oe.length-1,ke=ue.length-1,h={toStringTag:Le};h.absoluteValue=h.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),p(e)};h.ceil=function(){return p(new this.constructor(this),this.e+1,2)};h.clampedTo=h.clamp=function(e,n){var i,t=this,r=t.constructor;if(e=new r(e),n=new r(n),!e.s||!n.s)return new r(NaN);if(e.gt(n))throw Error($+n);return i=t.cmp(e),i<0?e:t.cmp(n)>0?n:new r(t)};h.comparedTo=h.cmp=function(e){var n,i,t,r,s=this,o=s.d,u=(e=new s.constructor(e)).d,c=s.s,f=e.s;if(!o||!u)return!c||!f?NaN:c!==f?c:o===u?0:!o^c<0?1:-1;if(!o[0]||!u[0])return o[0]?c:u[0]?-f:0;if(c!==f)return c;if(s.e!==e.e)return s.e>e.e^c<0?1:-1;for(t=o.length,r=u.length,n=0,i=t<r?t:r;n<i;++n)if(o[n]!==u[n])return o[n]>u[n]^c<0?1:-1;return t===r?0:t>r^c<0?1:-1};h.cosine=h.cos=function(){var e,n,i=this,t=i.constructor;return i.d?i.d[0]?(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=an(t,He(t,i)),t.precision=e,t.rounding=n,p(Z==2||Z==3?i.neg():i,e,n,!0)):new t(1):new t(NaN)};h.cubeRoot=h.cbrt=function(){var e,n,i,t,r,s,o,u,c,f,l=this,a=l.constructor;if(!l.isFinite()||l.isZero())return new a(l);for(w=!1,s=l.s*C(l.s*l,1/3),!s||Math.abs(s)==1/0?(i=b(l.d),e=l.e,(s=(e-i.length+1)%3)&&(i+=s==1||s==-2?\"0\":\"00\"),s=C(i,1/3),e=R((e+1)/3)-(e%3==(e<0?-1:2)),s==1/0?i=\"5e\"+e:(i=s.toExponential(),i=i.slice(0,i.indexOf(\"e\")+1)+e),t=new a(i),t.s=l.s):t=new a(s.toString()),o=(e=a.precision)+3;;)if(u=t,c=u.times(u).times(u),f=c.plus(l),t=k(f.plus(l).times(u),f.plus(c),o+2,1),b(u.d).slice(0,o)===(i=b(t.d)).slice(0,o))if(i=i.slice(o-3,o+1),i==\"9999\"||!r&&i==\"4999\"){if(!r&&(p(u,e+1,0),u.times(u).times(u).eq(l))){t=u;break}o+=4,r=1}else{(!+i||!+i.slice(1)&&i.charAt(0)==\"5\")&&(p(t,e+1,1),n=!t.times(t).times(t).eq(l));break}return w=!0,p(t,e,a.rounding,n)};h.decimalPlaces=h.dp=function(){var e,n=this.d,i=NaN;if(n){if(e=n.length-1,i=(e-R(this.e/m))*m,e=n[e],e)for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i};h.dividedBy=h.div=function(e){return k(this,new this.constructor(e))};h.dividedToIntegerBy=h.divToInt=function(e){var n=this,i=n.constructor;return p(k(n,new i(e),0,1,1),i.precision,i.rounding)};h.equals=h.eq=function(e){return this.cmp(e)===0};h.floor=function(){return p(new this.constructor(this),this.e+1,3)};h.greaterThan=h.gt=function(e){return this.cmp(e)>0};h.greaterThanOrEqualTo=h.gte=function(e){var n=this.cmp(e);return n==1||n===0};h.hyperbolicCosine=h.cosh=function(){var e,n,i,t,r,s=this,o=s.constructor,u=new o(1);if(!s.isFinite())return new o(s.s?1/0:NaN);if(s.isZero())return u;i=o.precision,t=o.rounding,o.precision=i+Math.max(s.e,s.sd())+4,o.rounding=1,r=s.d.length,r<32?(e=Math.ceil(r/3),n=(1/ae(4,e)).toString()):(e=16,n=\"2.3283064365386962890625e-10\"),s=j(o,1,s.times(n),new o(1),!0);for(var c,f=e,l=new o(8);f--;)c=s.times(s),s=u.minus(c.times(l.minus(c.times(l))));return p(s,o.precision=i,o.rounding=t,!0)};h.hyperbolicSine=h.sinh=function(){var e,n,i,t,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(n=s.precision,i=s.rounding,s.precision=n+Math.max(r.e,r.sd())+4,s.rounding=1,t=r.d.length,t<3)r=j(s,2,r,r,!0);else{e=1.4*Math.sqrt(t),e=e>16?16:e|0,r=r.times(1/ae(5,e)),r=j(s,2,r,r,!0);for(var o,u=new s(5),c=new s(16),f=new s(20);e--;)o=r.times(r),r=r.times(u.plus(o.times(c.times(o).plus(f))))}return s.precision=n,s.rounding=i,p(r,n,i,!0)};h.hyperbolicTangent=h.tanh=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+7,t.rounding=1,k(i.sinh(),i.cosh(),t.precision=e,t.rounding=n)):new t(i.s)};h.inverseCosine=h.acos=function(){var e=this,n=e.constructor,i=e.abs().cmp(1),t=n.precision,r=n.rounding;return i!==-1?i===0?e.isNeg()?F(n,t,r):new n(0):new n(NaN):e.isZero()?F(n,t+4,r).times(.5):(n.precision=t+6,n.rounding=1,e=new n(1).minus(e).div(e.plus(1)).sqrt().atan(),n.precision=t,n.rounding=r,e.times(2))};h.inverseHyperbolicCosine=h.acosh=function(){var e,n,i=this,t=i.constructor;return i.lte(1)?new t(i.eq(1)?0:NaN):i.isFinite()?(e=t.precision,n=t.rounding,t.precision=e+Math.max(Math.abs(i.e),i.sd())+4,t.rounding=1,w=!1,i=i.times(i).minus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln()):new t(i)};h.inverseHyperbolicSine=h.asinh=function(){var e,n,i=this,t=i.constructor;return!i.isFinite()||i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,t.rounding=1,w=!1,i=i.times(i).plus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln())};h.inverseHyperbolicTangent=h.atanh=function(){var e,n,i,t,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,n=s.rounding,t=r.sd(),Math.max(t,e)<2*-r.e-1?p(new s(r),e,n,!0):(s.precision=i=t-r.e,r=k(r.plus(1),new s(1).minus(r),i+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=n,r.times(.5))):new s(NaN)};h.inverseSine=h.asin=function(){var e,n,i,t,r=this,s=r.constructor;return r.isZero()?new s(r):(n=r.abs().cmp(1),i=s.precision,t=s.rounding,n!==-1?n===0?(e=F(s,i+4,t).times(.5),e.s=r.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=t,r.times(2)))};h.inverseTangent=h.atan=function(){var e,n,i,t,r,s,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding;if(f.isFinite()){if(f.isZero())return new l(f);if(f.abs().eq(1)&&a+4<=ke)return o=F(l,a+4,d).times(.25),o.s=f.s,o}else{if(!f.s)return new l(NaN);if(a+4<=ke)return o=F(l,a+4,d).times(.5),o.s=f.s,o}for(l.precision=u=a+10,l.rounding=1,i=Math.min(28,u/m+2|0),e=i;e;--e)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(w=!1,n=Math.ceil(u/m),t=1,c=f.times(f),o=new l(f),r=f;e!==-1;)if(r=r.times(c),s=o.minus(r.div(t+=2)),r=r.times(c),o=s.plus(r.div(t+=2)),o.d[n]!==void 0)for(e=n;o.d[e]===s.d[e]&&e--;);return i&&(o=o.times(2<<i-1)),w=!0,p(o,l.precision=a,l.rounding=d,!0)};h.isFinite=function(){return!!this.d};h.isInteger=h.isInt=function(){return!!this.d&&R(this.e/m)>this.d.length-2};h.isNaN=function(){return!this.s};h.isNegative=h.isNeg=function(){return this.s<0};h.isPositive=h.isPos=function(){return this.s>0};h.isZero=function(){return!!this.d&&this.d[0]===0};h.lessThan=h.lt=function(e){return this.cmp(e)<0};h.lessThanOrEqualTo=h.lte=function(e){return this.cmp(e)<1};h.logarithm=h.log=function(e){var n,i,t,r,s,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding,g=5;if(e==null)e=new l(10),n=!0;else{if(e=new l(e),i=e.d,e.s<0||!i||!i[0]||e.eq(1))return new l(NaN);n=e.eq(10)}if(i=f.d,f.s<0||!i||!i[0]||f.eq(1))return new l(i&&!i[0]?-1/0:f.s!=1?NaN:i?0:1/0);if(n)if(i.length>1)s=!0;else{for(r=i[0];r%10===0;)r/=10;s=r!==1}if(w=!1,u=a+g,o=B(f,u),t=n?fe(l,u+10):B(e,u),c=k(o,t,u,1),Q(c.d,r=a,d))do if(u+=10,o=B(f,u),t=n?fe(l,u+10):B(e,u),c=k(o,t,u,1),!s){+b(c.d).slice(r+1,r+15)+1==1e14&&(c=p(c,a+1,0));break}while(Q(c.d,r+=10,d));return w=!0,p(c,a,d)};h.minus=h.sub=function(e){var n,i,t,r,s,o,u,c,f,l,a,d,g=this,v=g.constructor;if(e=new v(e),!g.d||!e.d)return!g.s||!e.s?e=new v(NaN):g.d?e.s=-e.s:e=new v(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(f=g.d,d=e.d,u=v.precision,c=v.rounding,!f[0]||!d[0]){if(d[0])e.s=-e.s;else if(f[0])e=new v(g);else return new v(c===3?-0:0);return w?p(e,u,c):e}if(i=R(e.e/m),l=R(g.e/m),f=f.slice(),s=l-i,s){for(a=s<0,a?(n=f,s=-s,o=d.length):(n=d,i=l,o=f.length),t=Math.max(Math.ceil(u/m),o)+2,s>t&&(s=t,n.length=1),n.reverse(),t=s;t--;)n.push(0);n.reverse()}else{for(t=f.length,o=d.length,a=t<o,a&&(o=t),t=0;t<o;t++)if(f[t]!=d[t]){a=f[t]<d[t];break}s=0}for(a&&(n=f,f=d,d=n,e.s=-e.s),o=f.length,t=d.length-o;t>0;--t)f[o++]=0;for(t=d.length;t>s;){if(f[--t]<d[t]){for(r=t;r&&f[--r]===0;)f[r]=D-1;--f[r],f[t]+=D}f[t]-=d[t]}for(;f[--o]===0;)f.pop();for(;f[0]===0;f.shift())--i;return f[0]?(e.d=f,e.e=le(f,i),w?p(e,u,c):e):new v(c===3?-0:0)};h.modulo=h.mod=function(e){var n,i=this,t=i.constructor;return e=new t(e),!i.d||!e.s||e.d&&!e.d[0]?new t(NaN):!e.d||i.d&&!i.d[0]?p(new t(i),t.precision,t.rounding):(w=!1,t.modulo==9?(n=k(i,e.abs(),0,3,1),n.s*=e.s):n=k(i,e,0,t.modulo,1),n=n.times(e),w=!0,i.minus(n))};h.naturalExponential=h.exp=function(){return Se(this)};h.naturalLogarithm=h.ln=function(){return B(this)};h.negated=h.neg=function(){var e=new this.constructor(this);return e.s=-e.s,p(e)};h.plus=h.add=function(e){var n,i,t,r,s,o,u,c,f,l,a=this,d=a.constructor;if(e=new d(e),!a.d||!e.d)return!a.s||!e.s?e=new d(NaN):a.d||(e=new d(e.d||a.s===e.s?a:NaN)),e;if(a.s!=e.s)return e.s=-e.s,a.minus(e);if(f=a.d,l=e.d,u=d.precision,c=d.rounding,!f[0]||!l[0])return l[0]||(e=new d(a)),w?p(e,u,c):e;if(s=R(a.e/m),t=R(e.e/m),f=f.slice(),r=s-t,r){for(r<0?(i=f,r=-r,o=l.length):(i=l,t=s,o=f.length),s=Math.ceil(u/m),o=s>o?s+1:o+1,r>o&&(r=o,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for(o=f.length,r=l.length,o-r<0&&(r=o,i=l,l=f,f=i),n=0;r;)n=(f[--r]=f[r]+l[r]+n)/D|0,f[r]%=D;for(n&&(f.unshift(n),++t),o=f.length;f[--o]==0;)f.pop();return e.d=f,e.e=le(f,t),w?p(e,u,c):e};h.precision=h.sd=function(e){var n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error($+e);return i.d?(n=Ze(i.d),e&&i.e+1>n&&(n=i.e+1)):n=NaN,n};h.round=function(){var e=this,n=e.constructor;return p(new n(e),e.e+1,n.rounding)};h.sine=h.sin=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=hn(t,He(t,i)),t.precision=e,t.rounding=n,p(Z>2?i.neg():i,e,n,!0)):new t(NaN)};h.squareRoot=h.sqrt=function(){var e,n,i,t,r,s,o=this,u=o.d,c=o.e,f=o.s,l=o.constructor;if(f!==1||!u||!u[0])return new l(!f||f<0&&(!u||u[0])?NaN:u?o:1/0);for(w=!1,f=Math.sqrt(+o),f==0||f==1/0?(n=b(u),(n.length+c)%2==0&&(n+=\"0\"),f=Math.sqrt(n),c=R((c+1)/2)-(c<0||c%2),f==1/0?n=\"5e\"+c:(n=f.toExponential(),n=n.slice(0,n.indexOf(\"e\")+1)+c),t=new l(n)):t=new l(f.toString()),i=(c=l.precision)+3;;)if(s=t,t=s.plus(k(o,s,i+2,1)).times(.5),b(s.d).slice(0,i)===(n=b(t.d)).slice(0,i))if(n=n.slice(i-3,i+1),n==\"9999\"||!r&&n==\"4999\"){if(!r&&(p(s,c+1,0),s.times(s).eq(o))){t=s;break}i+=4,r=1}else{(!+n||!+n.slice(1)&&n.charAt(0)==\"5\")&&(p(t,c+1,1),e=!t.times(t).eq(o));break}return w=!0,p(t,c,l.rounding,e)};h.tangent=h.tan=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+10,t.rounding=1,i=i.sin(),i.s=1,i=k(i,new t(1).minus(i.times(i)).sqrt(),e+10,0),t.precision=e,t.rounding=n,p(Z==2||Z==4?i.neg():i,e,n,!0)):new t(NaN)};h.times=h.mul=function(e){var n,i,t,r,s,o,u,c,f,l=this,a=l.constructor,d=l.d,g=(e=new a(e)).d;if(e.s*=l.s,!d||!d[0]||!g||!g[0])return new a(!e.s||d&&!d[0]&&!g||g&&!g[0]&&!d?NaN:!d||!g?e.s/0:e.s*0);for(i=R(l.e/m)+R(e.e/m),c=d.length,f=g.length,c<f&&(s=d,d=g,g=s,o=c,c=f,f=o),s=[],o=c+f,t=o;t--;)s.push(0);for(t=f;--t>=0;){for(n=0,r=c+t;r>t;)u=s[r]+g[t]*d[r-t-1]+n,s[r--]=u%D|0,n=u/D|0;s[r]=(s[r]+n)%D|0}for(;!s[--o];)s.pop();return n?++i:s.shift(),e.d=s,e.e=le(s,i),w?p(e,a.precision,a.rounding):e};h.toBinary=function(e,n){return Me(this,2,e,n)};h.toDecimalPlaces=h.toDP=function(e,n){var i=this,t=i.constructor;return i=new t(i),e===void 0?i:(q(e,0,H),n===void 0?n=t.rounding:q(n,0,8),p(i,e+i.e+1,n))};h.toExponential=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=L(t,!0):(q(e,0,H),n===void 0?n=r.rounding:q(n,0,8),t=p(new r(t),e+1,n),i=L(t,!0,e+1)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toFixed=function(e,n){var i,t,r=this,s=r.constructor;return e===void 0?i=L(r):(q(e,0,H),n===void 0?n=s.rounding:q(n,0,8),t=p(new s(r),e+r.e+1,n),i=L(t,!1,e+t.e+1)),r.isNeg()&&!r.isZero()?\"-\"+i:i};h.toFraction=function(e){var n,i,t,r,s,o,u,c,f,l,a,d,g=this,v=g.d,N=g.constructor;if(!v)return new N(g);if(f=i=new N(1),t=c=new N(0),n=new N(t),s=n.e=Ze(v)-g.e-1,o=s%m,n.d[0]=C(10,o<0?m+o:o),e==null)e=s>0?n:f;else{if(u=new N(e),!u.isInt()||u.lt(f))throw Error($+u);e=u.gt(n)?s>0?n:f:u}for(w=!1,u=new N(b(v)),l=N.precision,N.precision=s=v.length*m*2;a=k(u,n,0,1,1),r=i.plus(a.times(t)),r.cmp(e)!=1;)i=t,t=r,r=f,f=c.plus(a.times(r)),c=r,r=n,n=u.minus(a.times(r)),u=r;return r=k(e.minus(i),t,0,1,1),c=c.plus(r.times(f)),i=i.plus(r.times(t)),c.s=f.s=g.s,d=k(f,t,s,1).minus(g).abs().cmp(k(c,i,s,1).minus(g).abs())<1?[f,t]:[c,i],N.precision=l,w=!0,d};h.toHexadecimal=h.toHex=function(e,n){return Me(this,16,e,n)};h.toNearest=function(e,n){var i=this,t=i.constructor;if(i=new t(i),e==null){if(!i.d)return i;e=new t(1),n=t.rounding}else{if(e=new t(e),n===void 0?n=t.rounding:q(n,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(w=!1,i=k(i,e,0,n,1).times(e),w=!0,p(i)):(e.s=i.s,i=e),i};h.toNumber=function(){return+this};h.toOctal=function(e,n){return Me(this,8,e,n)};h.toPower=h.pow=function(e){var n,i,t,r,s,o,u=this,c=u.constructor,f=+(e=new c(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new c(C(+u,f));if(u=new c(u),u.eq(1))return u;if(t=c.precision,s=c.rounding,e.eq(1))return p(u,t,s);if(n=R(e.e/m),n>=e.d.length-1&&(i=f<0?-f:f)<=cn)return r=Ue(c,u,i,t),e.s<0?new c(1).div(r):p(r,t,s);if(o=u.s,o<0){if(n<e.d.length-1)return new c(NaN);if((e.d[n]&1)==0&&(o=1),u.e==0&&u.d[0]==1&&u.d.length==1)return u.s=o,u}return i=C(+u,f),n=i==0||!isFinite(i)?R(f*(Math.log(\"0.\"+b(u.d))/Math.LN10+u.e+1)):new c(i+\"\").e,n>c.maxE+1||n<c.minE-1?new c(n>0?o/0:0):(w=!1,c.rounding=u.s=1,i=Math.min(12,(n+\"\").length),r=Se(e.times(B(u,t+i)),t),r.d&&(r=p(r,t+5,1),Q(r.d,t,s)&&(n=t+10,r=p(Se(e.times(B(u,n+i)),n),n+5,1),+b(r.d).slice(t+1,t+15)+1==1e14&&(r=p(r,t+1,0)))),r.s=o,w=!0,c.rounding=s,p(r,t,s))};h.toPrecision=function(e,n){var i,t=this,r=t.constructor;return e===void 0?i=L(t,t.e<=r.toExpNeg||t.e>=r.toExpPos):(q(e,1,H),n===void 0?n=r.rounding:q(n,0,8),t=p(new r(t),e,n),i=L(t,e<=t.e||t.e<=r.toExpNeg,e)),t.isNeg()&&!t.isZero()?\"-\"+i:i};h.toSignificantDigits=h.toSD=function(e,n){var i=this,t=i.constructor;return e===void 0?(e=t.precision,n=t.rounding):(q(e,1,H),n===void 0?n=t.rounding:q(n,0,8)),p(new t(i),e,n)};h.toString=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()&&!e.isZero()?\"-\"+i:i};h.truncated=h.trunc=function(){return p(new this.constructor(this),this.e+1,1)};h.valueOf=h.toJSON=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()?\"-\"+i:i};function b(e){var n,i,t,r=e.length-1,s=\"\",o=e[0];if(r>0){for(s+=o,n=1;n<r;n++)t=e[n]+\"\",i=m-t.length,i&&(s+=U(i)),s+=t;o=e[n],t=o+\"\",i=m-t.length,i&&(s+=U(i))}else if(o===0)return\"0\";for(;o%10===0;)o/=10;return s+o}function q(e,n,i){if(e!==~~e||e<n||e>i)throw Error($+e)}function Q(e,n,i,t){var r,s,o,u;for(s=e[0];s>=10;s/=10)--n;return--n<0?(n+=m,r=0):(r=Math.ceil((n+1)/m),n%=m),s=C(10,m-n),u=e[r]%s|0,t==null?n<3?(n==0?u=u/100|0:n==1&&(u=u/10|0),o=i<4&&u==99999||i>3&&u==49999||u==5e4||u==0):o=(i<4&&u+1==s||i>3&&u+1==s/2)&&(e[r+1]/s/100|0)==C(10,n-2)-1||(u==s/2||u==0)&&(e[r+1]/s/100|0)==0:n<4?(n==0?u=u/1e3|0:n==1?u=u/100|0:n==2&&(u=u/10|0),o=(t||i<4)&&u==9999||!t&&i>3&&u==4999):o=((t||i<4)&&u+1==s||!t&&i>3&&u+1==s/2)&&(e[r+1]/s/1e3|0)==C(10,n-3)-1,o}function re(e,n,i){for(var t,r=[0],s,o=0,u=e.length;o<u;){for(s=r.length;s--;)r[s]*=n;for(r[0]+=ve.indexOf(e.charAt(o++)),t=0;t<r.length;t++)r[t]>i-1&&(r[t+1]===void 0&&(r[t+1]=0),r[t+1]+=r[t]/i|0,r[t]%=i)}return r.reverse()}function an(e,n){var i,t,r;if(n.isZero())return n;t=n.d.length,t<32?(i=Math.ceil(t/3),r=(1/ae(4,i)).toString()):(i=16,r=\"2.3283064365386962890625e-10\"),e.precision+=i,n=j(e,1,n.times(r),new e(1));for(var s=i;s--;){var o=n.times(n);n=o.times(o).minus(o).times(8).plus(1)}return e.precision-=i,n}var k=function(){function e(t,r,s){var o,u=0,c=t.length;for(t=t.slice();c--;)o=t[c]*r+u,t[c]=o%s|0,u=o/s|0;return u&&t.unshift(u),t}function n(t,r,s,o){var u,c;if(s!=o)c=s>o?1:-1;else for(u=c=0;u<s;u++)if(t[u]!=r[u]){c=t[u]>r[u]?1:-1;break}return c}function i(t,r,s,o){for(var u=0;s--;)t[s]-=u,u=t[s]<r[s]?1:0,t[s]=u*o+t[s]-r[s];for(;!t[0]&&t.length>1;)t.shift()}return function(t,r,s,o,u,c){var f,l,a,d,g,v,N,A,M,_,E,P,x,I,de,z,W,he,T,y,ee=t.constructor,pe=t.s==r.s?1:-1,O=t.d,S=r.d;if(!O||!O[0]||!S||!S[0])return new ee(!t.s||!r.s||(O?S&&O[0]==S[0]:!S)?NaN:O&&O[0]==0||!S?pe*0:pe/0);for(c?(g=1,l=t.e-r.e):(c=D,g=m,l=R(t.e/g)-R(r.e/g)),T=S.length,W=O.length,M=new ee(pe),_=M.d=[],a=0;S[a]==(O[a]||0);a++);if(S[a]>(O[a]||0)&&l--,s==null?(I=s=ee.precision,o=ee.rounding):u?I=s+(t.e-r.e)+1:I=s,I<0)_.push(1),v=!0;else{if(I=I/g+2|0,a=0,T==1){for(d=0,S=S[0],I++;(a<W||d)&&I--;a++)de=d*c+(O[a]||0),_[a]=de/S|0,d=de%S|0;v=d||a<W}else{for(d=c/(S[0]+1)|0,d>1&&(S=e(S,d,c),O=e(O,d,c),T=S.length,W=O.length),z=T,E=O.slice(0,T),P=E.length;P<T;)E[P++]=0;y=S.slice(),y.unshift(0),he=S[0],S[1]>=c/2&&++he;do d=0,f=n(S,E,T,P),f<0?(x=E[0],T!=P&&(x=x*c+(E[1]||0)),d=x/he|0,d>1?(d>=c&&(d=c-1),N=e(S,d,c),A=N.length,P=E.length,f=n(N,E,A,P),f==1&&(d--,i(N,T<A?y:S,A,c))):(d==0&&(f=d=1),N=S.slice()),A=N.length,A<P&&N.unshift(0),i(E,N,P,c),f==-1&&(P=E.length,f=n(S,E,T,P),f<1&&(d++,i(E,T<P?y:S,P,c))),P=E.length):f===0&&(d++,E=[0]),_[a++]=d,f&&E[0]?E[P++]=O[z]||0:(E=[O[z]],P=1);while((z++<W||E[0]!==void 0)&&I--);v=E[0]!==void 0}_[0]||_.shift()}if(g==1)M.e=l,Te=v;else{for(a=1,d=_[0];d>=10;d/=10)a++;M.e=a+l*g-1,p(M,u?s+M.e+1:s,o,v)}return M}}();function p(e,n,i,t){var r,s,o,u,c,f,l,a,d,g=e.constructor;e:if(n!=null){if(a=e.d,!a)return e;for(r=1,u=a[0];u>=10;u/=10)r++;if(s=n-r,s<0)s+=m,o=n,l=a[d=0],c=l/C(10,r-o-1)%10|0;else if(d=Math.ceil((s+1)/m),u=a.length,d>=u)if(t){for(;u++<=d;)a.push(0);l=c=0,r=1,s%=m,o=s-m+1}else break e;else{for(l=u=a[d],r=1;u>=10;u/=10)r++;s%=m,o=s-m+r,c=o<0?0:l/C(10,r-o-1)%10|0}if(t=t||n<0||a[d+1]!==void 0||(o<0?l:l%C(10,r-o-1)),f=i<4?(c||t)&&(i==0||i==(e.s<0?3:2)):c>5||c==5&&(i==4||t||i==6&&(s>0?o>0?l/C(10,r-o):0:a[d-1])%10&1||i==(e.s<0?8:7)),n<1||!a[0])return a.length=0,f?(n-=e.e+1,a[0]=C(10,(m-n%m)%m),e.e=-n||0):a[0]=e.e=0,e;if(s==0?(a.length=d,u=1,d--):(a.length=d+1,u=C(10,m-s),a[d]=o>0?(l/C(10,r-o)%C(10,o)|0)*u:0),f)for(;;)if(d==0){for(s=1,o=a[0];o>=10;o/=10)s++;for(o=a[0]+=u,u=1;o>=10;o/=10)u++;s!=u&&(e.e++,a[0]==D&&(a[0]=1));break}else{if(a[d]+=u,a[d]!=D)break;a[d--]=0,u=1}for(s=a.length;a[--s]===0;)a.pop()}return w&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function L(e,n,i){if(!e.isFinite())return $e(e);var t,r=e.e,s=b(e.d),o=s.length;return n?(i&&(t=i-o)>0?s=s.charAt(0)+\".\"+s.slice(1)+U(t):o>1&&(s=s.charAt(0)+\".\"+s.slice(1)),s=s+(e.e<0?\"e\":\"e+\")+e.e):r<0?(s=\"0.\"+U(-r-1)+s,i&&(t=i-o)>0&&(s+=U(t))):r>=o?(s+=U(r+1-o),i&&(t=i-r-1)>0&&(s=s+\".\"+U(t))):((t=r+1)<o&&(s=s.slice(0,t)+\".\"+s.slice(t)),i&&(t=i-o)>0&&(r+1===o&&(s+=\".\"),s+=U(t))),s}function le(e,n){var i=e[0];for(n*=m;i>=10;i/=10)n++;return n}function fe(e,n,i){if(n>ln)throw w=!0,i&&(e.precision=i),Error(De);return p(new e(oe),n,1,!0)}function F(e,n,i){if(n>ke)throw Error(De);return p(new e(ue),n,i,!0)}function Ze(e){var n=e.length-1,i=n*m+1;if(n=e[n],n){for(;n%10==0;n/=10)i--;for(n=e[0];n>=10;n/=10)i++}return i}function U(e){for(var n=\"\";e--;)n+=\"0\";return n}function Ue(e,n,i,t){var r,s=new e(1),o=Math.ceil(t/m+4);for(w=!1;;){if(i%2&&(s=s.times(n),qe(s.d,o)&&(r=!0)),i=R(i/2),i===0){i=s.d.length-1,r&&s.d[i]===0&&++s.d[i];break}n=n.times(n),qe(n.d,o)}return w=!0,s}function Ae(e){return e.d[e.d.length-1]&1}function Be(e,n,i){for(var t,r,s=new e(n[0]),o=0;++o<n.length;){if(r=new e(n[o]),!r.s){s=r;break}t=s.cmp(r),(t===i||t===0&&s.s===i)&&(s=r)}return s}function Se(e,n){var i,t,r,s,o,u,c,f=0,l=0,a=0,d=e.constructor,g=d.rounding,v=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(n==null?(w=!1,c=v):c=n,u=new d(.03125);e.e>-2;)e=e.times(u),a+=5;for(t=Math.log(C(2,a))/Math.LN10*2+5|0,c+=t,i=s=o=new d(1),d.precision=c;;){if(s=p(s.times(e),c,1),i=i.times(++l),u=o.plus(k(s,i,c,1)),b(u.d).slice(0,c)===b(o.d).slice(0,c)){for(r=a;r--;)o=p(o.times(o),c,1);if(n==null)if(f<3&&Q(o.d,c-t,g,f))d.precision=c+=10,i=s=u=new d(1),l=0,f++;else return p(o,d.precision=v,g,w=!0);else return d.precision=v,o}o=u}}function B(e,n){var i,t,r,s,o,u,c,f,l,a,d,g=1,v=10,N=e,A=N.d,M=N.constructor,_=M.rounding,E=M.precision;if(N.s<0||!A||!A[0]||!N.e&&A[0]==1&&A.length==1)return new M(A&&!A[0]?-1/0:N.s!=1?NaN:A?0:N);if(n==null?(w=!1,l=E):l=n,M.precision=l+=v,i=b(A),t=i.charAt(0),Math.abs(s=N.e)<15e14){for(;t<7&&t!=1||t==1&&i.charAt(1)>3;)N=N.times(e),i=b(N.d),t=i.charAt(0),g++;s=N.e,t>1?(N=new M(\"0.\"+i),s++):N=new M(t+\".\"+i.slice(1))}else return f=fe(M,l+2,E).times(s+\"\"),N=B(new M(t+\".\"+i.slice(1)),l-v).plus(f),M.precision=E,n==null?p(N,E,_,w=!0):N;for(a=N,c=o=N=k(N.minus(1),N.plus(1),l,1),d=p(N.times(N),l,1),r=3;;){if(o=p(o.times(d),l,1),f=c.plus(k(o,new M(r),l,1)),b(f.d).slice(0,l)===b(c.d).slice(0,l))if(c=c.times(2),s!==0&&(c=c.plus(fe(M,l+2,E).times(s+\"\"))),c=k(c,new M(g),l,1),n==null)if(Q(c.d,l-v,_,u))M.precision=l+=v,f=o=N=k(a.minus(1),a.plus(1),l,1),d=p(N.times(N),l,1),r=u=1;else return p(c,M.precision=E,_,w=!0);else return M.precision=E,c;c=f,r+=2}}function $e(e){return String(e.s*e.s/0)}function se(e,n){var i,t,r;for((i=n.indexOf(\".\"))>-1&&(n=n.replace(\".\",\"\")),(t=n.search(/e/i))>0?(i<0&&(i=t),i+=+n.slice(t+1),n=n.substring(0,t)):i<0&&(i=n.length),t=0;n.charCodeAt(t)===48;t++);for(r=n.length;n.charCodeAt(r-1)===48;--r);if(n=n.slice(t,r),n){if(r-=t,e.e=i=i-t-1,e.d=[],t=(i+1)%m,i<0&&(t+=m),t<r){for(t&&e.d.push(+n.slice(0,t)),r-=m;t<r;)e.d.push(+n.slice(t,t+=m));n=n.slice(t),t=m-n.length}else t-=r;for(;t--;)n+=\"0\";e.d.push(+n),w&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function dn(e,n){var i,t,r,s,o,u,c,f,l;if(n.indexOf(\"_\")>-1){if(n=n.replace(/(\\d)_(?=\\d)/g,\"$1\"),Ie.test(n))return se(e,n)}else if(n===\"Infinity\"||n===\"NaN\")return+n||(e.s=NaN),e.e=NaN,e.d=null,e;if(un.test(n))i=16,n=n.toLowerCase();else if(on.test(n))i=2;else if(fn.test(n))i=8;else throw Error($+n);for(s=n.search(/p/i),s>0?(c=+n.slice(s+1),n=n.substring(2,s)):n=n.slice(2),s=n.indexOf(\".\"),o=s>=0,t=e.constructor,o&&(n=n.replace(\".\",\"\"),u=n.length,s=u-s,r=Ue(t,new t(i),s,s*2)),f=re(n,i,D),l=f.length-1,s=l;f[s]===0;--s)f.pop();return s<0?new t(e.s*0):(e.e=le(f,l),e.d=f,w=!1,o&&(e=k(e,r,u*4)),c&&(e=e.times(Math.abs(c)<54?C(2,c):Y.pow(2,c))),w=!0,e)}function hn(e,n){var i,t=n.d.length;if(t<3)return n.isZero()?n:j(e,2,n,n);i=1.4*Math.sqrt(t),i=i>16?16:i|0,n=n.times(1/ae(5,i)),n=j(e,2,n,n);for(var r,s=new e(5),o=new e(16),u=new e(20);i--;)r=n.times(n),n=n.times(s.plus(r.times(o.times(r).minus(u))));return n}function j(e,n,i,t,r){var s,o,u,c,f=1,l=e.precision,a=Math.ceil(l/m);for(w=!1,c=i.times(i),u=new e(t);;){if(o=k(u.times(c),new e(n++*n++),l,1),u=r?t.plus(o):t.minus(o),t=k(o.times(c),new e(n++*n++),l,1),o=u.plus(t),o.d[a]!==void 0){for(s=a;o.d[s]===u.d[s]&&s--;);if(s==-1)break}s=u,u=t,t=o,o=s,f++}return w=!0,o.d.length=a+1,o}function ae(e,n){for(var i=e;--n;)i*=e;return i}function He(e,n){var i,t=n.s<0,r=F(e,e.precision,1),s=r.times(.5);if(n=n.abs(),n.lte(s))return Z=t?4:1,n;if(i=n.divToInt(r),i.isZero())Z=t?3:2;else{if(n=n.minus(i.times(r)),n.lte(s))return Z=Ae(i)?t?2:3:t?4:1,n;Z=Ae(i)?t?1:4:t?3:2}return n.minus(r).abs()}function Me(e,n,i,t){var r,s,o,u,c,f,l,a,d,g=e.constructor,v=i!==void 0;if(v?(q(i,1,H),t===void 0?t=g.rounding:q(t,0,8)):(i=g.precision,t=g.rounding),!e.isFinite())l=$e(e);else{for(l=L(e),o=l.indexOf(\".\"),v?(r=2,n==16?i=i*4-3:n==8&&(i=i*3-2)):r=n,o>=0&&(l=l.replace(\".\",\"\"),d=new g(1),d.e=l.length-o,d.d=re(L(d),10,r),d.e=d.d.length),a=re(l,10,r),s=c=a.length;a[--c]==0;)a.pop();if(!a[0])l=v?\"0p+0\":\"0\";else{if(o<0?s--:(e=new g(e),e.d=a,e.e=s,e=k(e,d,i,t,0,r),a=e.d,s=e.e,f=Te),o=a[i],u=r/2,f=f||a[i+1]!==void 0,f=t<4?(o!==void 0||f)&&(t===0||t===(e.s<0?3:2)):o>u||o===u&&(t===4||f||t===6&&a[i-1]&1||t===(e.s<0?8:7)),a.length=i,f)for(;++a[--i]>r-1;)a[i]=0,i||(++s,a.unshift(1));for(c=a.length;!a[c-1];--c);for(o=0,l=\"\";o<c;o++)l+=ve.charAt(a[o]);if(v){if(c>1)if(n==16||n==8){for(o=n==16?4:3,--c;c%o;c++)l+=\"0\";for(a=re(l,r,n),c=a.length;!a[c-1];--c);for(o=1,l=\"1.\";o<c;o++)l+=ve.charAt(a[o])}else l=l.charAt(0)+\".\"+l.slice(1);l=l+(s<0?\"p\":\"p+\")+s}else if(s<0){for(;++s;)l=\"0\"+l;l=\"0.\"+l}else if(++s>c)for(s-=c;s--;)l+=\"0\";else s<c&&(l=l.slice(0,s)+\".\"+l.slice(s))}l=(n==16?\"0x\":n==2?\"0b\":n==8?\"0o\":\"\")+l}return e.s<0?\"-\"+l:l}function qe(e,n){if(e.length>n)return e.length=n,!0}function pn(e){return new this(e).abs()}function gn(e){return new this(e).acos()}function mn(e){return new this(e).acosh()}function wn(e,n){return new this(e).plus(n)}function Nn(e){return new this(e).asin()}function vn(e){return new this(e).asinh()}function En(e){return new this(e).atan()}function kn(e){return new this(e).atanh()}function Sn(e,n){e=new this(e),n=new this(n);var i,t=this.precision,r=this.rounding,s=t+4;return!e.s||!n.s?i=new this(NaN):!e.d&&!n.d?(i=F(this,s,1).times(n.s>0?.25:.75),i.s=e.s):!n.d||e.isZero()?(i=n.s<0?F(this,t,r):new this(0),i.s=e.s):!e.d||n.isZero()?(i=F(this,s,1).times(.5),i.s=e.s):n.s<0?(this.precision=s,this.rounding=1,i=this.atan(k(e,n,s,1)),n=F(this,s,1),this.precision=t,this.rounding=r,i=e.s<0?i.minus(n):i.plus(n)):i=this.atan(k(e,n,s,1)),i}function Mn(e){return new this(e).cbrt()}function Cn(e){return p(e=new this(e),e.e+1,2)}function bn(e,n,i){return new this(e).clamp(n,i)}function Pn(e){if(!e||typeof e!=\"object\")throw Error(ce+\"Object expected\");var n,i,t,r=e.defaults===!0,s=[\"precision\",1,H,\"rounding\",0,8,\"toExpNeg\",-V,0,\"toExpPos\",0,V,\"maxE\",0,V,\"minE\",-V,0,\"modulo\",0,9];for(n=0;n<s.length;n+=3)if(i=s[n],r&&(this[i]=Ee[i]),(t=e[i])!==void 0)if(R(t)===t&&t>=s[n+1]&&t<=s[n+2])this[i]=t;else throw Error($+i+\": \"+t);if(i=\"crypto\",r&&(this[i]=Ee[i]),(t=e[i])!==void 0)if(t===!0||t===!1||t===0||t===1)if(t)if(typeof crypto<\"u\"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(Fe);else this[i]=!1;else throw Error($+i+\": \"+t);return this}function On(e){return new this(e).cos()}function Rn(e){return new this(e).cosh()}function Ve(e){var n,i,t;function r(s){var o,u,c,f=this;if(!(f instanceof r))return new r(s);if(f.constructor=r,_e(s)){f.s=s.s,w?!s.d||s.e>r.maxE?(f.e=NaN,f.d=null):s.e<r.minE?(f.e=0,f.d=[0]):(f.e=s.e,f.d=s.d.slice()):(f.e=s.e,f.d=s.d?s.d.slice():s.d);return}if(c=typeof s,c===\"number\"){if(s===0){f.s=1/s<0?-1:1,f.e=0,f.d=[0];return}if(s<0?(s=-s,f.s=-1):f.s=1,s===~~s&&s<1e7){for(o=0,u=s;u>=10;u/=10)o++;w?o>r.maxE?(f.e=NaN,f.d=null):o<r.minE?(f.e=0,f.d=[0]):(f.e=o,f.d=[s]):(f.e=o,f.d=[s]);return}if(s*0!==0){s||(f.s=NaN),f.e=NaN,f.d=null;return}return se(f,s.toString())}if(c===\"string\")return(u=s.charCodeAt(0))===45?(s=s.slice(1),f.s=-1):(u===43&&(s=s.slice(1)),f.s=1),Ie.test(s)?se(f,s):dn(f,s);if(c===\"bigint\")return s<0?(s=-s,f.s=-1):f.s=1,se(f,s.toString());throw Error($+s)}if(r.prototype=h,r.ROUND_UP=0,r.ROUND_DOWN=1,r.ROUND_CEIL=2,r.ROUND_FLOOR=3,r.ROUND_HALF_UP=4,r.ROUND_HALF_DOWN=5,r.ROUND_HALF_EVEN=6,r.ROUND_HALF_CEIL=7,r.ROUND_HALF_FLOOR=8,r.EUCLID=9,r.config=r.set=Pn,r.clone=Ve,r.isDecimal=_e,r.abs=pn,r.acos=gn,r.acosh=mn,r.add=wn,r.asin=Nn,r.asinh=vn,r.atan=En,r.atanh=kn,r.atan2=Sn,r.cbrt=Mn,r.ceil=Cn,r.clamp=bn,r.cos=On,r.cosh=Rn,r.div=An,r.exp=qn,r.floor=_n,r.hypot=Tn,r.ln=Dn,r.log=Fn,r.log10=In,r.log2=Ln,r.max=Zn,r.min=Un,r.mod=Bn,r.mul=$n,r.pow=Hn,r.random=Vn,r.round=jn,r.sign=Wn,r.sin=Gn,r.sinh=Jn,r.sqrt=Xn,r.sub=Kn,r.sum=Qn,r.tan=Yn,r.tanh=xn,r.trunc=zn,e===void 0&&(e={}),e&&e.defaults!==!0)for(t=[\"precision\",\"rounding\",\"toExpNeg\",\"toExpPos\",\"maxE\",\"minE\",\"modulo\",\"crypto\"],n=0;n<t.length;)e.hasOwnProperty(i=t[n++])||(e[i]=this[i]);return r.config(e),r}function An(e,n){return new this(e).div(n)}function qn(e){return new this(e).exp()}function _n(e){return p(e=new this(e),e.e+1,3)}function Tn(){var e,n,i=new this(0);for(w=!1,e=0;e<arguments.length;)if(n=new this(arguments[e++]),n.d)i.d&&(i=i.plus(n.times(n)));else{if(n.s)return w=!0,new this(1/0);i=n}return w=!0,i.sqrt()}function _e(e){return e instanceof Y||e&&e.toStringTag===Le||!1}function Dn(e){return new this(e).ln()}function Fn(e,n){return new this(e).log(n)}function Ln(e){return new this(e).log(2)}function In(e){return new this(e).log(10)}function Zn(){return Be(this,arguments,-1)}function Un(){return Be(this,arguments,1)}function Bn(e,n){return new this(e).mod(n)}function $n(e,n){return new this(e).mul(n)}function Hn(e,n){return new this(e).pow(n)}function Vn(e){var n,i,t,r,s=0,o=new this(1),u=[];if(e===void 0?e=this.precision:q(e,1,H),t=Math.ceil(e/m),this.crypto)if(crypto.getRandomValues)for(n=crypto.getRandomValues(new Uint32Array(t));s<t;)r=n[s],r>=429e7?n[s]=crypto.getRandomValues(new Uint32Array(1))[0]:u[s++]=r%1e7;else if(crypto.randomBytes){for(n=crypto.randomBytes(t*=4);s<t;)r=n[s]+(n[s+1]<<8)+(n[s+2]<<16)+((n[s+3]&127)<<24),r>=214e7?crypto.randomBytes(4).copy(n,s):(u.push(r%1e7),s+=4);s=t/4}else throw Error(Fe);else for(;s<t;)u[s++]=Math.random()*1e7|0;for(t=u[--s],e%=m,t&&e&&(r=C(10,m-e),u[s]=(t/r|0)*r);u[s]===0;s--)u.pop();if(s<0)i=0,u=[0];else{for(i=-1;u[0]===0;i-=m)u.shift();for(t=1,r=u[0];r>=10;r/=10)t++;t<m&&(i-=m-t)}return o.e=i,o.d=u,o}function jn(e){return p(e=new this(e),e.e+1,this.rounding)}function Wn(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Gn(e){return new this(e).sin()}function Jn(e){return new this(e).sinh()}function Xn(e){return new this(e).sqrt()}function Kn(e,n){return new this(e).sub(n)}function Qn(){var e=0,n=arguments,i=new this(n[e]);for(w=!1;i.s&&++e<n.length;)i=i.plus(n[e]);return w=!0,p(i,this.precision,this.rounding)}function Yn(e){return new this(e).tan()}function xn(e){return new this(e).tanh()}function zn(e){return p(e=new this(e),e.e+1,1)}h[Symbol.for(\"nodejs.util.inspect.custom\")]=h.toString;h[Symbol.toStringTag]=\"Decimal\";var Y=h.constructor=Ve(Ee);oe=new Y(oe);ue=new Y(ue);var je=Y;0&&(0);\n/*! Bundled license information:\n\ndecimal.js/decimal.mjs:\n  (*!\n   *  decimal.js v10.5.0\n   *  An arbitrary-precision Decimal type for JavaScript.\n   *  https://github.com/MikeMcl/decimal.js\n   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>\n   *  MIT Licence\n   *)\n*/\n//# sourceMappingURL=index-browser.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.prisma/client/runtime/index-browser.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/@prisma/client/index-browser.js":
/*!**********************************************************!*\
  !*** ../../node_modules/@prisma/client/index-browser.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("const prisma = __webpack_require__(/*! .prisma/client/index-browser */ \"(pages-dir-browser)/../../node_modules/.prisma/client/index-browser.js\")\n\nmodule.exports = prisma\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvQHByaXNtYS9jbGllbnQvaW5kZXgtYnJvd3Nlci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxlQUFlLG1CQUFPLENBQUMsNEdBQThCOztBQUVyRCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3RzXFxHaXRIdWJcXG15LXF1aXotYXBwXFxub2RlX21vZHVsZXNcXEBwcmlzbWFcXGNsaWVudFxcaW5kZXgtYnJvd3Nlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBwcmlzbWEgPSByZXF1aXJlKCcucHJpc21hL2NsaWVudC9pbmRleC1icm93c2VyJylcblxubW9kdWxlLmV4cG9ydHMgPSBwcmlzbWFcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/@prisma/client/index-browser.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cprojects%5CGitHub%5Cmy-quiz-app%5Capps%5Cweb%5Cpages%5Cindex.tsx&page=%2F!":
/*!*****************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cprojects%5CGitHub%5Cmy-quiz-app%5Capps%5Cweb%5Cpages%5Cindex.tsx&page=%2F! ***!
  \*****************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/\",\n      function () {\n        return __webpack_require__(/*! ./pages/index.tsx */ \"(pages-dir-browser)/./pages/index.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD1EJTNBJTVDcHJvamVjdHMlNUNHaXRIdWIlNUNteS1xdWl6LWFwcCU1Q2FwcHMlNUN3ZWIlNUNwYWdlcyU1Q2luZGV4LnRzeCZwYWdlPSUyRiEiLCJtYXBwaW5ncyI6IjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQU8sQ0FBQyxnRUFBbUI7QUFDMUM7QUFDQTtBQUNBLE9BQU8sSUFBVTtBQUNqQixNQUFNLFVBQVU7QUFDaEI7QUFDQSxPQUFPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvXCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy9pbmRleC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL1wiXSlcbiAgICAgIH0pO1xuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cprojects%5CGitHub%5Cmy-quiz-app%5Capps%5Cweb%5Cpages%5Cindex.tsx&page=%2F!\n"));

/***/ }),

/***/ "(pages-dir-browser)/../../node_modules/next/router.js":
/*!*****************************************!*\
  !*** ../../node_modules/next/router.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/router */ \"(pages-dir-browser)/../../node_modules/next/dist/client/router.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9yb3V0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEscUlBQWdEIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXEdpdEh1YlxcbXktcXVpei1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dFxccm91dGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9yb3V0ZXInKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/next/router.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./components/ParentPortal.tsx":
/*!*************************************!*\
  !*** ./components/ParentPortal.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ParentPortal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nfunction ParentPortal(param) {\n    let { onAssignHomework } = param;\n    var _session_user, _session_user1;\n    _s();\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('progress');\n    const [selectedTopics, setSelectedTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dueDate, setDueDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for incomplete quizzes\n    const [incompleteQuizzes, setIncompleteQuizzes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingIncompleteQuizzes, setLoadingIncompleteQuizzes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [incompleteQuizzesError, setIncompleteQuizzesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // New state for analytics data\n    const [translationAnalytics, setTranslationAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [knowledgeAnalytics, setKnowledgeAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingAnalytics, setLoadingAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [analyticsError, setAnalyticsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Mock available subjects with icons\n    const availableSubjects = {\n        Science: '🧪',\n        Mathematics: '🔢',\n        English: '📚'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParentPortal.useEffect\": ()=>{\n            // Fetch syllabus data from API endpoint\n            fetch('/api/syllabus').then({\n                \"ParentPortal.useEffect\": (res)=>res.json()\n            }[\"ParentPortal.useEffect\"]).then({\n                \"ParentPortal.useEffect\": (data)=>{\n                    if (data && data.syllabus) {\n                        setSyllabus(data.syllabus);\n                    } else {\n                        console.error('Syllabus data not in expected format:', data);\n                        setSyllabus([]);\n                    }\n                }\n            }[\"ParentPortal.useEffect\"]).catch({\n                \"ParentPortal.useEffect\": (error)=>{\n                    console.error('Error loading syllabus:', error);\n                    setSyllabus([]);\n                }\n            }[\"ParentPortal.useEffect\"]);\n        }\n    }[\"ParentPortal.useEffect\"], []);\n    // New useEffect to fetch analytics data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParentPortal.useEffect\": ()=>{\n            const fetchAnalytics = {\n                \"ParentPortal.useEffect.fetchAnalytics\": async ()=>{\n                    setLoadingAnalytics(true);\n                    setAnalyticsError(null);\n                    try {\n                        // TODO: Get actual childId from auth context or selected child\n                        const childId = 1; // Placeholder childId\n                        const [translationRes, knowledgeRes] = await Promise.all([\n                            fetch(\"/api/analytics/translations?childId=\".concat(childId)),\n                            fetch(\"/api/analytics/knowledge?childId=\".concat(childId))\n                        ]);\n                        if (!translationRes.ok) throw new Error('Failed to fetch translation analytics');\n                        if (!knowledgeRes.ok) throw new Error('Failed to fetch knowledge analytics');\n                        const translationData = await translationRes.json();\n                        const knowledgeData = await knowledgeRes.json();\n                        setTranslationAnalytics(translationData);\n                        setKnowledgeAnalytics(knowledgeData);\n                    } catch (error) {\n                        console.error('Error fetching analytics:', error);\n                        setAnalyticsError(error.message);\n                    } finally{\n                        setLoadingAnalytics(false);\n                    }\n                }\n            }[\"ParentPortal.useEffect.fetchAnalytics\"];\n            fetchAnalytics();\n        }\n    }[\"ParentPortal.useEffect\"], []); // Add dependencies here if filtering by child or time period is implemented\n    // Fetch incomplete quizzes for the parent's children\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParentPortal.useEffect\": ()=>{\n            const fetchIncompleteQuizzes = {\n                \"ParentPortal.useEffect.fetchIncompleteQuizzes\": async ()=>{\n                    var _session_user;\n                    if (session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id) {\n                        setLoadingIncompleteQuizzes(true);\n                        setIncompleteQuizzesError(null);\n                        try {\n                            const response = await fetch(\"/api/parent/incomplete-quizzes?parentId=\".concat(session.user.id)); // Pass parentId\n                            if (!response.ok) {\n                                throw new Error(\"Error fetching incomplete quizzes: \".concat(response.statusText));\n                            }\n                            const data = await response.json();\n                            setIncompleteQuizzes(data.incompleteQuizzes);\n                        } catch (error) {\n                            console.error('Error fetching incomplete quizzes:', error);\n                            setIncompleteQuizzesError(error.message);\n                        } finally{\n                            setLoadingIncompleteQuizzes(false);\n                        }\n                    }\n                }\n            }[\"ParentPortal.useEffect.fetchIncompleteQuizzes\"];\n            // Only fetch if session is authenticated\n            if (status === 'authenticated') {\n                fetchIncompleteQuizzes();\n            }\n        }\n    }[\"ParentPortal.useEffect\"], [\n        session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id,\n        status\n    ]); // Depend on session.user.id and status\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    console.log('ParentPortal session state:', session);\n    // Mock parent data (keep for structure, but use session.user.name for display)\n    const parentData = {\n        name: (session === null || session === void 0 ? void 0 : (_session_user1 = session.user) === null || _session_user1 === void 0 ? void 0 : _session_user1.name) || 'Parent',\n        children: [\n            {\n                name: 'John Smith',\n                grade: '10th Grade',\n                recentActivity: [\n                    {\n                        type: 'quiz',\n                        subject: 'Science',\n                        score: 85,\n                        date: '2025-04-22'\n                    },\n                    {\n                        type: 'homework',\n                        subject: 'Mathematics',\n                        status: 'Completed',\n                        date: '2025-04-21'\n                    }\n                ],\n                progress: {\n                    Mathematics: {\n                        score: 75,\n                        trend: '+5%'\n                    },\n                    Science: {\n                        score: 85,\n                        trend: '+2%'\n                    },\n                    History: {\n                        score: 92,\n                        trend: '+8%'\n                    }\n                },\n                upcomingTasks: [\n                    {\n                        type: 'quiz',\n                        subject: 'Mathematics',\n                        dueDate: '2025-04-25'\n                    },\n                    {\n                        type: 'homework',\n                        subject: 'History',\n                        dueDate: '2025-04-24'\n                    }\n                ]\n            }\n        ],\n        notifications: [\n            {\n                type: 'milestone',\n                message: 'John completed Science Chapter 5 with 85%',\n                date: '2025-04-22'\n            },\n            {\n                type: 'review',\n                message: 'Mathematics needs review - Chapter 4 scores below average',\n                date: '2025-04-21'\n            }\n        ]\n    };\n    const handleTopicSelect = (unit)=>{\n        setSelectedTopics((prev)=>{\n            if (prev.includes(unit)) {\n                return prev.filter((t)=>t !== unit);\n            }\n            return [\n                ...prev,\n                unit\n            ];\n        });\n    };\n    const handleSubjectSelect = (subject)=>{\n        setSelectedSubject(subject);\n        setSelectedTopics([]); // Reset selected topics when changing subject\n    };\n    const handleAssignTopics = ()=>{\n        if (selectedTopics.length > 0 && dueDate) {\n            // Here you would typically make an API call to save the assignment\n            console.log('Assigning topics:', selectedTopics, 'Due date:', dueDate);\n            if (onAssignHomework) {\n                onAssignHomework();\n            }\n            // Reset selection\n            setSelectedTopics([]);\n            setDueDate('');\n        }\n    };\n    // Show loading state while session is loading\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n            lineNumber: 188,\n            columnNumber: 12\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (status === 'unauthenticated') {\n        router.push('/login');\n        return null; // Return null or a loading indicator while redirecting\n    }\n    // If authenticated, render the portal\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: \"QuizApp\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm bg-green-100 text-green-800 px-3 py-1 rounded-full\",\n                                    children: \"Parent Portal\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right mr-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: parentData.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Parent Account\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                            className: \"flex items-center space-x-2 focus:outline-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white\",\n                                                    children: parentData.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"▼\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-2 text-xs text-gray-500\",\n                                                    children: \"Signed in as Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Account Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Email Preferences\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Help Center\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n                                                            callbackUrl: '/login'\n                                                        }),\n                                                    className: \"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 focus:outline-none\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: [\n                            'progress',\n                            'assign topics',\n                            'resources',\n                            'notifications'\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedTab(tab),\n                                className: \"\".concat(selectedTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm capitalize\"),\n                                children: tab\n                            }, tab, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: [\n                    selectedTab === 'progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Incomplete Quizzes\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    loadingIncompleteQuizzes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Loading incomplete quizzes...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 44\n                                    }, this),\n                                    incompleteQuizzesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500\",\n                                        children: [\n                                            \"Error loading incomplete quizzes: \",\n                                            incompleteQuizzesError\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 42\n                                    }, this),\n                                    !loadingIncompleteQuizzes && incompleteQuizzes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No incomplete quizzes found for your children.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    !loadingIncompleteQuizzes && incompleteQuizzes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: incompleteQuizzes.map((quiz)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-gray-50 rounded-lg border-l-4 border-yellow-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        quiz.childName,\n                                                                        \" - \",\n                                                                        quiz.subject,\n                                                                        \" \",\n                                                                        quiz.unit ? \"Unit \".concat(quiz.unit) : ''\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500 mt-1\",\n                                                                    children: [\n                                                                        quiz.quizType,\n                                                                        \" Quiz - Attempted \",\n                                                                        quiz.attemptedQuestions,\n                                                                        \" of \",\n                                                                        quiz.totalQuestions,\n                                                                        \" questions\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Started: \",\n                                                                new Date(quiz.startTime).toLocaleString(),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Elapsed: \",\n                                                                Math.floor(quiz.elapsedTimeSeconds / 60),\n                                                                \"m \",\n                                                                quiz.elapsedTimeSeconds % 60,\n                                                                \"s\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, quiz.id, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this),\n                            loadingAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Loading analytics...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 34\n                            }, this),\n                            analyticsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-500\",\n                                children: [\n                                    \"Error loading analytics: \",\n                                    analyticsError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 32\n                            }, this),\n                            translationAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Translation Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Total Translations: \",\n                                            translationAnalytics.translationCount || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Common Translated Words:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: translationAnalytics.commonWords && translationAnalytics.commonWords.length > 0 ? translationAnalytics.commonWords.slice(0, 10).map((word, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    word.word || 'Unknown',\n                                                    \" (\",\n                                                    word.count || 0,\n                                                    \")\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No common words found\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this),\n                            knowledgeAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Knowledge Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Total Quiz Attempts: \",\n                                            knowledgeAnalytics.attemptCount || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Attempts by Subject:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.subjectAttemptCounts && Object.entries(knowledgeAnalytics.subjectAttemptCounts).length > 0 ? Object.entries(knowledgeAnalytics.subjectAttemptCounts).map((param)=>{\n                                            let [subject, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    subject,\n                                                    \": \",\n                                                    count\n                                                ]\n                                            }, subject, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 23\n                                            }, this);\n                                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No subject data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Attempts by Unit:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.unitAttemptCounts && Object.entries(knowledgeAnalytics.unitAttemptCounts).length > 0 ? Object.entries(knowledgeAnalytics.unitAttemptCounts).map((param)=>{\n                                            let [unit, count] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    unit,\n                                                    \": \",\n                                                    count\n                                                ]\n                                            }, unit, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 23\n                                            }, this);\n                                        }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No unit data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Subject Success Rates:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.subjectSuccessRates && knowledgeAnalytics.subjectSuccessRates.length > 0 ? knowledgeAnalytics.subjectSuccessRates.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    item.subject,\n                                                    \": \",\n                                                    (item.successRate || 0).toFixed(2),\n                                                    \"%\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No subject success data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Unit Success Rates:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.unitSuccessRates && knowledgeAnalytics.unitSuccessRates.length > 0 ? knowledgeAnalytics.unitSuccessRates.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    item.unit,\n                                                    \": \",\n                                                    (item.successRate || 0).toFixed(2),\n                                                    \"%\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No unit success data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'assign topics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: !selectedSubject ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"\\uD83D\\uDCDA\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Select a Subject\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                            children: Object.entries(availableSubjects).map((param)=>{\n                                                let [subject, icon] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSubjectSelect(subject),\n                                                    className: \"p-4 rounded-lg border-2 border-transparent hover:border-blue-500 bg-blue-50 hover:bg-blue-100 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl mb-2\",\n                                                            children: icon\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: subject\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, subject, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mr-2\",\n                                                            children: availableSubjects[selectedSubject]\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedSubject,\n                                                        \" Topics\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedSubject(null),\n                                                    className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                                    children: \"Change Subject\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                            children: syllabus && syllabus.length > 0 ? syllabus.map((topic)=>{\n                                                var _topic_topic, _topic_topic1;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleTopicSelect(topic.unit),\n                                                    className: \"p-3 rounded-md flex items-center \".concat(selectedTopics.includes(topic.unit) ? 'bg-blue-100 border-2 border-blue-500' : 'bg-blue-50 hover:bg-blue-100 border-2 border-transparent'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedTopics.includes(topic.unit),\n                                                            onChange: ()=>{},\n                                                            className: \"h-4 w-4 text-blue-600 rounded mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: [\n                                                                        \"Unit \",\n                                                                        topic.unit,\n                                                                        \": \",\n                                                                        ((_topic_topic = topic.topic) === null || _topic_topic === void 0 ? void 0 : _topic_topic.en) || 'Unknown'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: ((_topic_topic1 = topic.topic) === null || _topic_topic1 === void 0 ? void 0 : _topic_topic1.zh) || ''\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, topic.unit, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 25\n                                                }, this);\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center py-4 text-gray-500\",\n                                                children: \"No topics available for this subject\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Assignment Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Due Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dueDate,\n                                                        onChange: (e)=>setDueDate(e.target.value),\n                                                        className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Selected Topics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1 text-sm text-gray-500\",\n                                                        children: selectedTopics.length === 0 ? 'No topics selected' : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc pl-5\",\n                                                            children: selectedTopics.map((unit)=>{\n                                                                var _topic_topic;\n                                                                const topic = syllabus && syllabus.find((t)=>t.unit === unit);\n                                                                return topic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"Unit \",\n                                                                        topic.unit,\n                                                                        \": \",\n                                                                        ((_topic_topic = topic.topic) === null || _topic_topic === void 0 ? void 0 : _topic_topic.en) || 'Unknown'\n                                                                    ]\n                                                                }, unit, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"Unit \",\n                                                                        unit\n                                                                    ]\n                                                                }, unit, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAssignTopics,\n                                                disabled: selectedTopics.length === 0 || !dueDate,\n                                                className: \"w-full py-3 rounded-md text-white font-medium \".concat(selectedTopics.length > 0 && dueDate ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'),\n                                                children: [\n                                                    \"Assign Topics (\",\n                                                    selectedTopics.length,\n                                                    \" selected)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'homework' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Upcoming Tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onAssignHomework,\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                            children: \"Assign New Task\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        parentData.children && parentData.children[0] && parentData.children[0].upcomingTasks && parentData.children[0].upcomingTasks.map((task, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: task.subject\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: task.type\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Due: \",\n                                                                task.dueDate\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 21\n                                            }, this)),\n                                        (!parentData.children || !parentData.children[0] || !parentData.children[0].upcomingTasks || parentData.children[0].upcomingTasks.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 text-center py-4\",\n                                            children: \"No upcoming tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'resources' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold mb-4\",\n                                    children: \"Recommended Resources\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        'Mathematics Practice Sets',\n                                        'Science Video Tutorials',\n                                        'History Study Guides',\n                                        'Language Arts Worksheets'\n                                    ].map((resource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: resource\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mb-4\",\n                                                    children: \"Supplementary materials to support learning\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 text-sm hover:text-blue-700\",\n                                                    children: \"Access Resource →\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, resource, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Recent Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            parentData.notifications && parentData.notifications.map((notification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: notification.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                                        children: notification.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: notification.date\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            (!parentData.notifications || parentData.notifications.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-500 text-center py-4\",\n                                                children: \"No notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Notification Preferences\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            'Weekly Progress Reports',\n                                            'Assignment Alerts',\n                                            'Performance Updates',\n                                            'Resource Recommendations'\n                                        ].map((pref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: pref\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Receive updates via email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                className: \"sr-only peer\",\n                                                                defaultChecked: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, pref, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(ParentPortal, \"f4HweI/FKUy/rxT++z4Qn70HRaM=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ParentPortal;\nvar _c;\n$RefreshReg$(_c, \"ParentPortal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/ParentPortal.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ParentPortal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ParentPortal */ \"(pages-dir-browser)/./components/ParentPortal.tsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"(pages-dir-browser)/../../node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n// Test component to switch between roles\n\nvar _s = $RefreshSig$();\n\n // Import useRouter\n\n\n\n// Test component to switch between roles\nfunction RoleSwitcher(param) {\n    let { onSwitch } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 bg-white p-4 rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-bold mb-2\",\n                children: \"Test Mode: Switch Role\"\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onSwitch(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD),\n                        className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                        children: \"Student View\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onSwitch(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.PARENT),\n                        className: \"px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600\",\n                        children: \"Parent View\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c = RoleSwitcher;\nfunction Home() {\n    _s();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)(); // Initialize useRouter\n    // Function to switch roles for testing\n    const switchRole = async (role)=>{\n        // Sign out current session\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)({\n            redirect: false\n        });\n        // Mock credentials based on role\n        const credentials = role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD ? {\n            username: 'johndoe',\n            pin: '1234'\n        } // Mock child credentials\n         : {\n            email: '<EMAIL>',\n            password: 'password123'\n        }; // Mock parent credentials\n        // Sign in with new role\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)('credentials', {\n            ...credentials,\n            redirect: false\n        });\n    };\n    // Handle redirects based on authentication status and user role\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            } else if (status === 'authenticated' && (session === null || session === void 0 ? void 0 : session.user)) {\n                const user = session.user;\n                if (user.role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD) {\n                    router.push('/student-dashboard');\n                }\n            // Parent users stay on this page to see the ParentPortal\n            }\n        }\n    }[\"Home.useEffect\"], [\n        status,\n        session,\n        router\n    ]);\n    // Show loading state while session is loading or redirecting\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated, show loading while redirecting\n    if (status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Redirecting to login...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    const user = session === null || session === void 0 ? void 0 : session.user;\n    // If child user, show loading while redirecting\n    if ((user === null || user === void 0 ? void 0 : user.role) === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Redirecting to dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (user === null || user === void 0 ? void 0 : user.role) === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.PARENT ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ParentPortal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onAssignHomework: ()=>{}\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 109,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"You don't have permission to access this page.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 112,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 111,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n_s(Home, \"IsB+X4/uCtap/BkD4g9WA4/8vZ8=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = Home;\nvar _c, _c1;\n$RefreshReg$(_c, \"RoleSwitcher\");\n$RefreshReg$(_c1, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/index.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/../../node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cprojects%5CGitHub%5Cmy-quiz-app%5Capps%5Cweb%5Cpages%5Cindex.tsx&page=%2F!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);