import React from 'react';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import useSWR from 'swr';
import QuizConfigCard from '../../components/admin/QuizConfigCard';
import Link from 'next/link';

// Fetcher function for SWR
const fetcher = (url: string) => fetch(url).then((res) => res.json());

const QuizConfigPage: React.FC = () => {
  const router = useRouter();
  const { data: session, status } = useSession();
  
  // Fetch quiz configs
  const { data: configs, error, isLoading } = useSWR('/api/admin/quiz-config', fetcher);
  
  // Redirect if not authenticated or not an admin
  React.useEffect(() => {
    if (status === 'unauthenticated' || (session && session.user?.role !== 'ADMIN')) {
      router.push('/login');
    }
  }, [session, status, router]);
  
  // Show loading state
  if (status === 'loading' || isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-gray-600">Loading...</div>
      </div>
    );
  }
  
  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-red-600">Error loading quiz configurations</div>
      </div>
    );
  }
  
  // Find configs for each mode
  const masteryConfig = configs?.find(config => config.mode === 'MASTERY') || {
    id: 0,
    mode: 'MASTERY',
    numQuestions: 10,
    questionTypes: [],
    allowTranslate: true,
    allowHints: true,
    allowAiTutor: true,
    updatedAt: new Date().toISOString(),
  };
  
  const testConfig = configs?.find(config => config.mode === 'TEST') || {
    id: 0,
    mode: 'TEST',
    numQuestions: 20,
    questionTypes: [],
    allowTranslate: false,
    allowHints: false,
    allowAiTutor: false,
    updatedAt: new Date().toISOString(),
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Link href="/admin" className="text-2xl font-bold text-blue-600">QuizApp</Link>
            <span className="text-sm bg-purple-100 text-purple-800 px-3 py-1 rounded-full">Admin Portal</span>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right mr-4">
              <div className="font-medium text-gray-900">{session?.user?.name || 'Admin'}</div>
              <div className="text-sm text-gray-500">{session?.user?.role || 'ADMIN'}</div>
            </div>
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-6 flex justify-between items-center">
          <h1 className="text-2xl font-bold">Quiz Configuration</h1>
          <Link href="/admin" className="text-blue-600 hover:text-blue-800">
            &larr; Back to Dashboard
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Mastery Quiz Config */}
          <QuizConfigCard mode="MASTERY" config={masteryConfig} />
          
          {/* Test Quiz Config */}
          <QuizConfigCard mode="TEST" config={testConfig} />
        </div>
        
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">About Quiz Configuration</h2>
          <p className="mb-4">
            These settings control the behavior of quizzes in the application. Each quiz mode (Mastery and Test) can have different settings.
          </p>
          <ul className="list-disc pl-5 space-y-2">
            <li><strong>Number of Questions:</strong> The default number of questions to include in a quiz of this type.</li>
            <li><strong>Question Types:</strong> The types of questions that can appear in this quiz mode.</li>
            <li><strong>Allow Translation:</strong> Whether students can translate questions during the quiz.</li>
            <li><strong>Allow Hints:</strong> Whether hints are available for difficult questions.</li>
            <li><strong>Allow AI Tutor:</strong> Whether the AI tutor is available to help students.</li>
          </ul>
        </div>
      </main>
    </div>
  );
};

export default QuizConfigPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (!session || session.user?.role !== 'ADMIN') {
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};
