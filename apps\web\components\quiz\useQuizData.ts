import { useState, useEffect } from 'react';
import use<PERSON><PERSON> from 'swr';
import { useSession } from 'next-auth/react';
import { Question, QuizAttempt } from '../../types/quiz';

// Using QuizAttempt from types/quiz.ts

interface QuizData {
  quizAttempt: QuizAttempt;
  questions: Question[];
  isCompleted?: boolean;
  message?: string;
  childLanguagePreferences?: {
    quizLanguage: string;
    menuLanguage: string;
  };
}

// Track when the component is mounted to prevent unnecessary revalidation
let isMounted = false;

const fetcher = async (url: string) => {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Error fetching quiz data: ${response.statusText}`);
  }
  return response.json();
};

export function useQuizData(attemptId: string | string[] | undefined) {
  const { status } = useSession();
  const [quizType, setQuizType] = useState<'MASTERY' | 'TEST' | 'QUICK'>('TEST');
  const [currentQuestionIndexCache, setCurrentQuestionIndexCache] = useState<number | null>(null);

  // Only fetch if authenticated and attemptId exists and is a string
  const shouldFetch = status === 'authenticated' && attemptId && !Array.isArray(attemptId);

  // Use SWR to fetch quiz data with configuration to prevent revalidation on focus/visibility changes
  const { data, error, mutate } = useSWR<QuizData>(
    shouldFetch ? `/api/quiz/${attemptId}` : null,
    fetcher,
    {
      revalidateOnFocus: false,  // Disable revalidation when window gets focus
      revalidateOnReconnect: false, // Disable revalidation when browser reconnects
      revalidateIfStale: false, // Don't revalidate if data is stale
      dedupingInterval: 10000, // Dedupe requests with the same key in this time span (ms)
      onSuccess: (data) => {
        console.log('SWR data loaded successfully:', data.quizAttempt.currentQuestionIndex);
        console.log('Child language preferences:', data.childLanguagePreferences);
      }
    }
  );

  // Fetch quiz type if not included in the response and cache the current question index
  useEffect(() => {
    if (data?.quizAttempt) {
      // Cache the current question index when data is loaded
      if (currentQuestionIndexCache === null ||
          (data.quizAttempt.currentQuestionIndex !== undefined &&
           data.quizAttempt.currentQuestionIndex !== currentQuestionIndexCache)) {
        console.log('Caching question index:', data.quizAttempt.currentQuestionIndex);
        setCurrentQuestionIndexCache(data.quizAttempt.currentQuestionIndex);
      }

      // Set quiz type if available
      if (data.quizAttempt.quizType) {
        setQuizType(data.quizAttempt.quizType);
      }
    } else if (shouldFetch && attemptId) {
      const fetchQuizType = async () => {
        try {
          const quizTypeResponse = await fetch(`/api/quiz-type?attemptId=${attemptId}`);
          if (quizTypeResponse.ok) {
            const quizTypeData = await quizTypeResponse.json();
            setQuizType(quizTypeData.quizType.toUpperCase());
          }
        } catch (error) {
          console.error('Error fetching quiz type:', error);
          // Default to TEST if there's an error
          setQuizType('TEST');
        }
      };
      fetchQuizType();
    }
  }, [data, shouldFetch, attemptId, currentQuestionIndexCache]);

  // Save quiz state when navigating away (only if authenticated)
  useEffect(() => {
    if (status !== 'authenticated' || !data) return;

    const quizAttemptId = data.quizAttempt?.id;
    const currentIndex = data.quizAttempt?.currentQuestionIndex || 0;
    const total = data.questions?.length || 0;

    const handleBeforeUnload = async () => {
      if (quizAttemptId && currentIndex < total && total > 0) {
        // Save current progress
        try {
          await fetch('/api/log-quiz-attempt', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              quizAttemptId: quizAttemptId,
              currentQuestionIndex: currentIndex,
            }),
          });
        } catch (error) {
          console.error('Error saving quiz progress on beforeunload:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Cleanup function: remove listener and save on unmount/navigation
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Save progress on component unmount (for client-side navigation)
      if (quizAttemptId && currentIndex < total && total > 0) {
        fetch('/api/log-quiz-attempt', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            quizAttemptId: quizAttemptId,
            currentQuestionIndex: currentIndex,
          }),
        }).catch(error => console.error('Error saving quiz progress on unmount:', error));
      }
    };
  }, [data, status]);

  // If we have a cached question index but the data shows a different index,
  // create a modified version of the data with our cached index
  const processedData = data && currentQuestionIndexCache !== null &&
    data.quizAttempt &&
    data.quizAttempt.currentQuestionIndex !== currentQuestionIndexCache
    ? {
        ...data,
        quizAttempt: {
          ...data.quizAttempt,
          currentQuestionIndex: currentQuestionIndexCache
        }
      }
    : data;

  // Add a component mount effect to prevent unnecessary revalidation
  useEffect(() => {
    isMounted = true;

    return () => {
      isMounted = false;
    };
  }, []);

  return {
    data: processedData,
    error,
    isLoading: !error && !data,
    quizType,
    mutate
  };
}

export default useQuizData;
