<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1747307883026" clover="3.2.0">
  <project timestamp="1747307883026" name="All files">
    <metrics statements="294" coveredstatements="30" conditionals="167" coveredconditionals="12" methods="35" coveredmethods="4" elements="496" coveredelements="46" complexity="0" loc="294" ncloc="294" packages="5" files="12" classes="12"/>
    <package name="lib">
      <metrics statements="224" coveredstatements="0" conditionals="146" coveredconditionals="0" methods="21" coveredmethods="0"/>
      <file name="api.ts" path="D:\projects\GitHub\my-quiz-app\lib\api.ts">
        <metrics statements="24" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="49" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
      </file>
      <file name="auth.ts" path="D:\projects\GitHub\my-quiz-app\lib\auth.ts">
        <metrics statements="12" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="22" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
      </file>
      <file name="grading.ts" path="D:\projects\GitHub\my-quiz-app\lib\grading.ts">
        <metrics statements="101" coveredstatements="0" conditionals="51" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="9" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="16" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="9"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="81" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="100" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="125" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
      </file>
      <file name="highlightKeywords.ts" path="D:\projects\GitHub\my-quiz-app\lib\highlightKeywords.ts">
        <metrics statements="22" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
      </file>
      <file name="i18n.ts" path="D:\projects\GitHub\my-quiz-app\lib\i18n.ts">
        <metrics statements="33" coveredstatements="0" conditionals="62" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="13" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="73" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="93" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="prisma.ts" path="D:\projects\GitHub\my-quiz-app\lib\prisma.ts">
        <metrics statements="4" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="7" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="rate-limit.ts" path="D:\projects\GitHub\my-quiz-app\lib\rate-limit.ts">
        <metrics statements="28" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="33" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
      </file>
    </package>
    <package name="lib.stats">
      <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="1" methods="1" coveredmethods="1"/>
      <file name="wilson.ts" path="D:\projects\GitHub\my-quiz-app\lib\stats\wilson.ts">
        <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="1" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="2" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
      </file>
    </package>
    <package name="lib.validation">
      <metrics statements="25" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="questionSpec.ts" path="D:\projects\GitHub\my-quiz-app\lib\validation\questionSpec.ts">
        <metrics statements="25" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="6" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="113" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="23" coveredstatements="23" conditionals="13" coveredconditionals="11" methods="3" coveredmethods="3"/>
      <file name="computeMastery.ts" path="D:\projects\GitHub\my-quiz-app\services\computeMastery.ts">
        <metrics statements="23" coveredstatements="23" conditionals="13" coveredconditionals="11" methods="3" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="21" count="4" type="stmt"/>
        <line num="22" count="4" type="stmt"/>
        <line num="34" count="24" type="stmt"/>
        <line num="40" count="4" type="stmt"/>
        <line num="41" count="44" type="stmt"/>
        <line num="42" count="44" type="stmt"/>
        <line num="43" count="44" type="cond" truecount="1" falsecount="0"/>
        <line num="47" count="4" type="stmt"/>
        <line num="48" count="24" type="cond" truecount="3" falsecount="0"/>
        <line num="54" count="4" type="stmt"/>
        <line num="55" count="4" type="stmt"/>
        <line num="56" count="24" type="cond" truecount="3" falsecount="0"/>
        <line num="57" count="3" type="stmt"/>
        <line num="63" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="70" count="4" type="stmt"/>
        <line num="76" count="4" type="stmt"/>
      </file>
    </package>
    <package name="services.storage">
      <metrics statements="15" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="LocalDiskStorage.ts" path="D:\projects\GitHub\my-quiz-app\services\storage\LocalDiskStorage.ts">
        <metrics statements="10" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="D:\projects\GitHub\my-quiz-app\services\storage\index.ts">
        <metrics statements="5" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="14" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
