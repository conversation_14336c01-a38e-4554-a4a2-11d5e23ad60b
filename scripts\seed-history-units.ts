import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting to seed Year 5 History units...');

    // Find Year 5 record
    const year5 = await prisma.year.findUnique({
      where: { yearNumber: 5 },
    });

    // Find History subject record
    const historySubject = await prisma.subject.findUnique({
      where: { name: 'History' },
    });

    if (year5 && historySubject) {
      // Define the Year 5 History syllabus
      const year5HistorySyllabus = {
        "year": 5,
        "subject": "History",
        "syllabus": [
          {
            "unit": 1,
            "topic": {
              "zh": "君主立宪是国家的保护伞",
              "en": "The Monarchy: Umbrella of the Nation",
              "ms": "Institusi Raja Payung Negara"
            }
          },
          {
            "unit": 2,
            "topic": {
              "zh": "马来西亚的伊斯兰教",
              "en": "Islam in Malaysia",
              "ms": "Agama Islam di Malaysia"
            }
          },
          {
            "unit": 3,
            "topic": {
              "zh": "马来语是我们的遗产",
              "en": "Bahasa Melayu: Our Heritage",
              "ms": "Bahasa Melayu Warisan Kita"
            }
          },
          {
            "unit": 4,
            "topic": {
              "zh": "国家主权的挑战",
              "en": "Challenges to National Sovereignty",
              "ms": "Kedaulatan Negara Dicabar"
            }
          },
          {
            "unit": 5,
            "topic": {
              "zh": "地方领袖的兴起与抗争",
              "en": "Rise and Struggle Against Colonialists",
              "ms": "Bangkit Berjuang Penjajah Ditentang"
            }
          },
          {
            "unit": 6,
            "topic": {
              "zh": "国家独立的历史",
              "en": "The History of Independence",
              "ms": "Sejarah Kemerdekaan"
            }
          },
          {
            "unit": 7,
            "topic": {
              "zh": "国家元首是国家主权的核心",
              "en": "The Yang Di-Pertuan Agong: Core of National Sovereignty",
              "ms": "Yang Di-Pertuan Agong Teras Kedaulatan Negara"
            }
          },
          {
            "unit": 8,
            "topic": {
              "zh": "马来西亚的国徽",
              "en": "The National Emblem (Jata Negara)",
              "ms": "Jata Negara"
            }
          },
          {
            "unit": 9,
            "topic": {
              "zh": "马来西亚的国旗",
              "en": "The National Flag of Malaysia",
              "ms": "Bendera Kebangsaan Malaysia"
            }
          },
          {
            "unit": 10,
            "topic": {
              "zh": "马来西亚的国歌",
              "en": "The National Anthem of Malaysia",
              "ms": "Lagu Kebangsaan Malaysia"
            }
          },
          {
            "unit": 11,
            "topic": {
              "zh": "马来语是国语",
              "en": "The National Language (Bahasa Kebangsaan)",
              "ms": "Bahasa Kebangsaan"
            }
          },
          {
            "unit": 12,
            "topic": {
              "zh": "我国的国花大红花",
              "en": "The National Flower (Bunga Raya)",
              "ms": "Bunga Raya Bunga Kebangsaan"
            }
          }
        ]
      };

      // Seed the units
      for (const unitData of year5HistorySyllabus.syllabus) {
        await prisma.unit.upsert({
          where: {
            unitNumber_subjectId_yearId: {
              unitNumber: unitData.unit,
              subjectId: historySubject.id,
              yearId: year5.id,
            }
          },
          update: {
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms
          },
          create: {
            unitNumber: unitData.unit,
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms,
            yearId: year5.id,
            subjectId: historySubject.id,
          },
        });
        console.log(`Seeded Unit ${unitData.unit}: ${unitData.topic.en}`);
      }
      console.log('Unit table for Year 5 History seeded successfully.');
    } else {
      console.warn('Could not find Year 5 or History subject to seed units.');
    }
  } catch (error) {
    console.error('Error seeding History units:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error('Fatal error:', e);
    process.exit(1);
  });
