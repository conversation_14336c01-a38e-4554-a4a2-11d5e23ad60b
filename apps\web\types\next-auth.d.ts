import "next-auth";

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      /** The user's postal address. */
      id: string; // Assuming the user id is a string
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role?: string; // Add the role property
      parentId?: string; // Add parentId property
    } & DefaultSession["user"];
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User {
    id: string; // Assuming the user id is a string
    role?: string; // Add the role property
    parentId?: string; // Add parentId property
  }
}

import "next-auth/jwt";

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    id: string; // Assuming the user id is a string
    role?: string; // Add the role property
    parentId?: string; // Add parentId property
  }
}
