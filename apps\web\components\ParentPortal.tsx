import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/router';

interface Topic {
  unit: number;
  topic: {
    zh: string;
    en: string;
  };
}

interface ParentPortalProps {
  onAssignHomework?: () => void;
}

export default function ParentPortal({ onAssignHomework }: ParentPortalProps) {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState('progress');
  const [selectedTopics, setSelectedTopics] = useState<number[]>([]);
  const [syllabus, setSyllabus] = useState<Topic[]>([]);
  const [dueDate, setDueDate] = useState('');
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null);

  // State for incomplete quizzes
  const [incompleteQuizzes, setIncompleteQuizzes] = useState<any[]>([]);
  const [loadingIncompleteQuizzes, setLoadingIncompleteQuizzes] = useState(true);
  const [incompleteQuizzesError, setIncompleteQuizzesError] = useState<string | null>(null);

  const { data: session, status } = useSession();

  // New state for analytics data
  const [translationAnalytics, setTranslationAnalytics] = useState<any>(null);
  const [knowledgeAnalytics, setKnowledgeAnalytics] = useState<any>(null);
  const [loadingAnalytics, setLoadingAnalytics] = useState(true);
  const [analyticsError, setAnalyticsError] = useState<string | null>(null);

  // Mock available subjects with icons
  const availableSubjects = {
    Science: '🧪',
    Mathematics: '🔢',
    English: '📚'
  };

  useEffect(() => {
    // Fetch syllabus data from API endpoint
    fetch('/api/syllabus')
      .then(res => res.json())
      .then(data => {
        if (data && data.syllabus) {
          setSyllabus(data.syllabus);
        } else {
          console.error('Syllabus data not in expected format:', data);
          setSyllabus([]);
        }
      })
      .catch(error => {
        console.error('Error loading syllabus:', error);
        setSyllabus([]);
      });
  }, []);

  // New useEffect to fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoadingAnalytics(true);
      setAnalyticsError(null);
      try {
        // TODO: Get actual childId from auth context or selected child
        const childId = 1; // Placeholder childId

        const [translationRes, knowledgeRes] = await Promise.all([
          fetch(`/api/analytics/translations?childId=${childId}`),
          fetch(`/api/analytics/knowledge?childId=${childId}`),
        ]);

        if (!translationRes.ok) throw new Error('Failed to fetch translation analytics');
        if (!knowledgeRes.ok) throw new Error('Failed to fetch knowledge analytics');

        const translationData = await translationRes.json();
        const knowledgeData = await knowledgeRes.json();

        setTranslationAnalytics(translationData);
        setKnowledgeAnalytics(knowledgeData);

      } catch (error: any) {
        console.error('Error fetching analytics:', error);
        setAnalyticsError(error.message);
      } finally {
        setLoadingAnalytics(false);
      }
    };

    fetchAnalytics();
  }, []); // Add dependencies here if filtering by child or time period is implemented

  // Fetch incomplete quizzes for the parent's children
  useEffect(() => {
    const fetchIncompleteQuizzes = async () => {
      if (session?.user?.id) { // Ensure session and user.id are available
        setLoadingIncompleteQuizzes(true);
        setIncompleteQuizzesError(null);
        try {
          const response = await fetch(`/api/parent/incomplete-quizzes?parentId=${session.user.id}`); // Pass parentId
          if (!response.ok) {
            throw new Error(`Error fetching incomplete quizzes: ${response.statusText}`);
          }
          const data = await response.json();
          setIncompleteQuizzes(data.incompleteQuizzes);
        } catch (error: any) {
          console.error('Error fetching incomplete quizzes:', error);
          setIncompleteQuizzesError(error.message);
        } finally {
          setLoadingIncompleteQuizzes(false);
        }
      }
    };

    // Only fetch if session is authenticated
    if (status === 'authenticated') {
      fetchIncompleteQuizzes();
    }
  }, [session?.user?.id, status]); // Depend on session.user.id and status


  const router = useRouter();


  console.log('ParentPortal session state:', session);

  // Mock parent data (keep for structure, but use session.user.name for display)
  const parentData = {
    name: session?.user?.name || 'Parent', // Use logged-in user's name
    children: [
      {
        name: 'John Smith', // This might need to be fetched based on the parent user
        grade: '10th Grade',
        recentActivity: [
          { type: 'quiz', subject: 'Science', score: 85, date: '2025-04-22' },
          { type: 'homework', subject: 'Mathematics', status: 'Completed', date: '2025-04-21' }
        ],
        progress: {
          Mathematics: { score: 75, trend: '+5%' },
          Science: { score: 85, trend: '+2%' },
          History: { score: 92, trend: '+8%' }
        },
        upcomingTasks: [
          { type: 'quiz', subject: 'Mathematics', dueDate: '2025-04-25' },
          { type: 'homework', subject: 'History', dueDate: '2025-04-24' }
        ]
      }
    ],
    notifications: [
      { type: 'milestone', message: 'John completed Science Chapter 5 with 85%', date: '2025-04-22' },
      { type: 'review', message: 'Mathematics needs review - Chapter 4 scores below average', date: '2025-04-21' }
    ]
  };

  const handleTopicSelect = (unit: number) => {
    setSelectedTopics(prev => {
      if (prev.includes(unit)) {
        return prev.filter(t => t !== unit);
      }
      return [...prev, unit];
    });
  };

  const handleSubjectSelect = (subject: string) => {
    setSelectedSubject(subject);
    setSelectedTopics([]); // Reset selected topics when changing subject
  };

  const handleAssignTopics = () => {
    if (selectedTopics.length > 0 && dueDate) {
      // Here you would typically make an API call to save the assignment
      console.log('Assigning topics:', selectedTopics, 'Due date:', dueDate);
      if (onAssignHomework) {
        onAssignHomework();
      }
      // Reset selection
      setSelectedTopics([]);
      setDueDate('');
    }
  };

  // Show loading state while session is loading
  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  // Redirect if not authenticated
  if (status === 'unauthenticated') {
    router.push('/login');
    return null; // Return null or a loading indicator while redirecting
  }

  // If authenticated, render the portal
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="text-2xl font-bold text-blue-600">QuizApp</div>
            <span className="text-sm bg-green-100 text-green-800 px-3 py-1 rounded-full">Parent Portal</span>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right mr-4">
              <div className="font-medium text-gray-900">{parentData.name}</div>
              <div className="text-sm text-gray-500">Parent Account</div>
            </div>
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center space-x-2 focus:outline-none"
              >
                <div className="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white">
                  {parentData.name.charAt(0)}
                </div>
                <span>▼</span>
              </button>
              {isUserMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1">
                  <div className="px-4 py-2 text-xs text-gray-500">Signed in as Parent</div>
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Account Settings</a>
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Email Preferences</a>
                  <a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Help Center</a>
                  <div className="border-t border-gray-100"></div>
                  <button
                    onClick={() => signOut({ callbackUrl: '/login' })}
                    className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 focus:outline-none"
                  >
                    Sign Out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4">
          <nav className="-mb-px flex space-x-8">
            {['progress', 'assign topics', 'resources', 'notifications'].map((tab) => (
              <button
                key={tab}
                onClick={() => setSelectedTab(tab)}
                className={`${
                  selectedTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm capitalize`}
              >
                {tab}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {selectedTab === 'progress' && (
          <div className="space-y-6">
            {/* Incomplete Quizzes Section */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Incomplete Quizzes</h2>
              {loadingIncompleteQuizzes && <p>Loading incomplete quizzes...</p>}
              {incompleteQuizzesError && <p className="text-red-500">Error loading incomplete quizzes: {incompleteQuizzesError}</p>}
              {!loadingIncompleteQuizzes && incompleteQuizzes.length === 0 && (
                <p>No incomplete quizzes found for your children.</p>
              )}
              {!loadingIncompleteQuizzes && incompleteQuizzes.length > 0 && (
                <div className="space-y-4">
                  {incompleteQuizzes.map((quiz) => (
                    <div key={quiz.id} className="p-4 bg-gray-50 rounded-lg border-l-4 border-yellow-500">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">{quiz.childName} - {quiz.subject} {quiz.unit ? `Unit ${quiz.unit}` : ''}</div>
                          <div className="text-sm text-gray-500 mt-1">
                            {quiz.quizType} Quiz - Attempted {quiz.attemptedQuestions} of {quiz.totalQuestions} questions
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          Started: {new Date(quiz.startTime).toLocaleString()}
                          <br />
                          Elapsed: {Math.floor(quiz.elapsedTimeSeconds / 60)}m {quiz.elapsedTimeSeconds % 60}s
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>


            {loadingAnalytics && <p>Loading analytics...</p>}
            {analyticsError && <p className="text-red-500">Error loading analytics: {analyticsError}</p>}

            {translationAnalytics && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Translation Analytics</h2>
                <p>Total Translations: {translationAnalytics.translationCount || 0}</p>
                <h3 className="text-lg font-medium mt-4 mb-2">Common Translated Words:</h3>
                <ul>
                  {translationAnalytics.commonWords && translationAnalytics.commonWords.length > 0 ? (
                    translationAnalytics.commonWords.slice(0, 10).map((word: any, index: number) => (
                      <li key={index}>{word.word || 'Unknown'} ({word.count || 0})</li>
                    ))
                  ) : (
                    <li>No common words found</li>
                  )}
                </ul>
                {/* You can add more detailed translation logs here if needed */}
              </div>
            )}

            {knowledgeAnalytics && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold mb-4">Knowledge Analytics</h2>
                <p>Total Quiz Attempts: {knowledgeAnalytics.attemptCount || 0}</p>

                <h3 className="text-lg font-medium mt-4 mb-2">Attempts by Subject:</h3>
                <ul>
                  {knowledgeAnalytics.subjectAttemptCounts && Object.entries(knowledgeAnalytics.subjectAttemptCounts).length > 0 ? (
                    Object.entries(knowledgeAnalytics.subjectAttemptCounts).map(([subject, count]: [string, any]) => (
                      <li key={subject}>{subject}: {count}</li>
                    ))
                  ) : (
                    <li>No subject data available</li>
                  )}
                </ul>

                <h3 className="text-lg font-medium mt-4 mb-2">Attempts by Unit:</h3>
                <ul>
                  {knowledgeAnalytics.unitAttemptCounts && Object.entries(knowledgeAnalytics.unitAttemptCounts).length > 0 ? (
                    Object.entries(knowledgeAnalytics.unitAttemptCounts).map(([unit, count]: [string, any]) => (
                      <li key={unit}>{unit}: {count}</li>
                    ))
                  ) : (
                    <li>No unit data available</li>
                  )}
                </ul>

                <h3 className="text-lg font-medium mt-4 mb-2">Subject Success Rates:</h3>
                <ul>
                  {knowledgeAnalytics.subjectSuccessRates && knowledgeAnalytics.subjectSuccessRates.length > 0 ? (
                    knowledgeAnalytics.subjectSuccessRates.map((item: any, index: number) => (
                      <li key={index}>{item.subject}: {(item.successRate || 0).toFixed(2)}%</li>
                    ))
                  ) : (
                    <li>No subject success data available</li>
                  )}
                </ul>

                <h3 className="text-lg font-medium mt-4 mb-2">Unit Success Rates:</h3>
                <ul>
                  {knowledgeAnalytics.unitSuccessRates && knowledgeAnalytics.unitSuccessRates.length > 0 ? (
                    knowledgeAnalytics.unitSuccessRates.map((item: any, index: number) => (
                      <li key={index}>{item.unit}: {(item.successRate || 0).toFixed(2)}%</li>
                    ))
                  ) : (
                    <li>No unit success data available</li>
                  )}
                </ul>

                {/* You can add more detailed quiz attempt logs here if needed */}
              </div>
            )}
          </div>
        )}

        {selectedTab === 'assign topics' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Subject and Topic Selection */}
            <div className="bg-white rounded-lg shadow p-6">
              {!selectedSubject ? (
                <>
                  <h2 className="text-xl font-semibold mb-4 flex items-center">
                    <span className="mr-2">📚</span>
                    Select a Subject
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {Object.entries(availableSubjects).map(([subject, icon]) => (
                      <button
                        key={subject}
                        onClick={() => handleSubjectSelect(subject)}
                        className="p-4 rounded-lg border-2 border-transparent hover:border-blue-500 bg-blue-50 hover:bg-blue-100 transition-colors"
                      >
                        <div className="text-3xl mb-2">{icon}</div>
                        <div className="font-medium">{subject}</div>
                      </button>
                    ))}
                  </div>
                </>
              ) : (
                <>
                  <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-semibold flex items-center">
                      <span className="mr-2">{availableSubjects[selectedSubject]}</span>
                      {selectedSubject} Topics
                    </h2>
                    <button
                      onClick={() => setSelectedSubject(null)}
                      className="text-sm text-blue-600 hover:text-blue-800"
                    >
                      Change Subject
                    </button>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {syllabus && syllabus.length > 0 ? (
                      syllabus.map((topic) => (
                        <button
                          key={topic.unit}
                          onClick={() => handleTopicSelect(topic.unit)}
                          className={`p-3 rounded-md flex items-center ${
                            selectedTopics.includes(topic.unit)
                              ? 'bg-blue-100 border-2 border-blue-500'
                              : 'bg-blue-50 hover:bg-blue-100 border-2 border-transparent'
                          }`}
                        >
                          <input
                            type="checkbox"
                            checked={selectedTopics.includes(topic.unit)}
                            onChange={() => {}}
                            className="h-4 w-4 text-blue-600 rounded mr-3"
                          />
                          <div className="text-left flex-1">
                            <div className="font-medium text-sm">Unit {topic.unit}: {topic.topic?.en || 'Unknown'}</div>
                            <div className="text-xs text-gray-600">{topic.topic?.zh || ''}</div>
                          </div>
                        </button>
                      ))
                    ) : (
                      <div className="col-span-2 text-center py-4 text-gray-500">
                        No topics available for this subject
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>

            {/* Assignment Details */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Assignment Details</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Due Date</label>
                  <input
                    type="date"
                    value={dueDate}
                    onChange={(e) => setDueDate(e.target.value)}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Selected Topics</label>
                  <div className="mt-1 text-sm text-gray-500">
                    {selectedTopics.length === 0 ? (
                      'No topics selected'
                    ) : (
                      <ul className="list-disc pl-5">
                        {selectedTopics.map((unit) => {
                          const topic = syllabus && syllabus.find(t => t.unit === unit);
                          return topic ? (
                            <li key={unit}>Unit {topic.unit}: {topic.topic?.en || 'Unknown'}</li>
                          ) : (
                            <li key={unit}>Unit {unit}</li>
                          );
                        })}
                      </ul>
                    )}
                  </div>
                </div>
                <button
                  onClick={handleAssignTopics}
                  disabled={selectedTopics.length === 0 || !dueDate}
                  className={`w-full py-3 rounded-md text-white font-medium ${
                    selectedTopics.length > 0 && dueDate
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-gray-400 cursor-not-allowed'
                  }`}
                >
                  Assign Topics ({selectedTopics.length} selected)
                </button>
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'homework' && (
          <div className="space-y-6">
            {/* Upcoming Tasks */}
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Upcoming Tasks</h2>
                <button
                  onClick={onAssignHomework}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Assign New Task
                </button>
              </div>
              <div className="space-y-4">
                {parentData.children && parentData.children[0] && parentData.children[0].upcomingTasks &&
                  parentData.children[0].upcomingTasks.map((task, index) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium">{task.subject}</div>
                        <div className="text-sm text-gray-500">{task.type}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-500">Due: {task.dueDate}</div>
                      </div>
                    </div>
                  ))
                }
                {(!parentData.children || !parentData.children[0] || !parentData.children[0].upcomingTasks || parentData.children[0].upcomingTasks.length === 0) && (
                  <div className="text-gray-500 text-center py-4">No upcoming tasks</div>
                )}
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'resources' && (
          <div className="space-y-6">
            {/* Resource Library */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Recommended Resources</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {['Mathematics Practice Sets', 'Science Video Tutorials', 'History Study Guides', 'Language Arts Worksheets'].map((resource) => (
                  <div key={resource} className="p-4 bg-gray-50 rounded-lg">
                    <div className="font-medium mb-2">{resource}</div>
                    <p className="text-sm text-gray-500 mb-4">Supplementary materials to support learning</p>
                    <button className="text-blue-600 text-sm hover:text-blue-700">Access Resource →</button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {selectedTab === 'notifications' && (
          <div className="space-y-6">
            {/* Notifications */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Recent Notifications</h2>
              <div className="space-y-4">
                {parentData.notifications && parentData.notifications.map((notification, index) => (
                  <div key={index} className="p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="font-medium">{notification.message}</div>
                        <div className="text-sm text-gray-500 mt-1">{notification.type}</div>
                      </div>
                      <div className="text-sm text-gray-500">{notification.date}</div>
                    </div>
                  </div>
                ))}
                {(!parentData.notifications || parentData.notifications.length === 0) && (
                  <div className="text-gray-500 text-center py-4">No notifications</div>
                )}
              </div>
            </div>

            {/* Email Preferences */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Notification Preferences</h2>
              <div className="space-y-4">
                {['Weekly Progress Reports', 'Assignment Alerts', 'Performance Updates', 'Resource Recommendations'].map((pref) => (
                  <div key={pref} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium">{pref}</div>
                      <div className="text-sm text-gray-500">Receive updates via email</div>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input type="checkbox" className="sr-only peer" defaultChecked />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
