interface Props {
  tp: number;                    // 0 when unknown
  confidence: 'secure' | 'emerging' | 'low';
}

export const TpPill = ({ tp, confidence }: Props) => {
  const colour =
    confidence === 'secure'
      ? 'bg-green-500'
      : confidence === 'emerging'
      ? 'bg-amber-500'
      : 'bg-gray-400';

  return (
    <span
      className={`${colour} text-white text-[10px] sm:text-xs px-2 py-[2px] rounded-full whitespace-nowrap`}
      title={
        tp
          ? `Mastery TP${tp} • ${confidence}`
          : 'Mastery not established yet'
      }
    >
      TP&nbsp;{tp || '—'}
    </span>
  );
};
