import React, { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import QuestionModal from '../../components/admin/QuestionModal';

interface FlaggedQuestion {
  id: number;
  questionId: number;
  childId: number;
  quizAttemptId: number;
  submittedKey: string | null;
  submittedText: string | null;
  status: 'PENDING' | 'REVIEWED' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  reviewedAt: string | null;
  question: {
    id: number;
    questionId: string;
    type: string;
    promptEn: string;
    promptZh: string;
    subject: {
      name: string;
    };
    unit: {
      unitNumber: number;
      topicEn: string;
    };
  };
  child: {
    name: string;
    username: string;
  };
}

export default function FlaggedQuestionsPage() {
  const { data: session, status } = useSession();
  const [flaggedQuestions, setFlaggedQuestions] = useState<FlaggedQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previewId, setPreviewId] = useState<number | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    const fetchFlaggedQuestions = async () => {
      try {
        const response = await fetch('/api/admin/flagged-questions');
        if (!response.ok) {
          throw new Error('Failed to fetch flagged questions');
        }
        const data = await response.json();
        setFlaggedQuestions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (session?.user?.role === 'ADMIN') {
      fetchFlaggedQuestions();
    }
  }, [session]);

  const handleStatusChange = async (flagId: number, newStatus: 'REVIEWED' | 'APPROVED' | 'REJECTED') => {
    try {
      const response = await fetch(`/api/admin/update-flag-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          flagId,
          status: newStatus,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update flag status');
      }

      // Update the local state
      setFlaggedQuestions(prev => 
        prev.map(flag => 
          flag.id === flagId 
            ? { ...flag, status: newStatus, reviewedAt: new Date().toISOString() } 
            : flag
        )
      );
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const filteredQuestions = filterStatus === 'all' 
    ? flaggedQuestions 
    : flaggedQuestions.filter(q => q.status === filterStatus);

  if (status === 'loading' || (session && loading)) {
    return <div className="p-8 text-center">Loading...</div>;
  }

  if (status === 'unauthenticated' || (session?.user?.role !== 'ADMIN')) {
    return (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p>You must be logged in as an admin to view this page.</p>
        <Link href="/login" className="text-blue-600 hover:underline mt-4 inline-block">
          Go to Login
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Flagged Questions</h1>
        <div className="flex space-x-2">
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="border border-gray-300 rounded px-3 py-2"
          >
            <option value="all">All Statuses</option>
            <option value="PENDING">Pending</option>
            <option value="REVIEWED">Reviewed</option>
            <option value="APPROVED">Approved</option>
            <option value="REJECTED">Rejected</option>
          </select>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {filteredQuestions.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No flagged questions found.</p>
        </div>
      ) : (
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Question
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Student
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Submitted Answer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredQuestions.map((flag) => (
                <tr key={flag.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {flag.question.questionId}
                    </div>
                    <div className="text-sm text-gray-500">
                      {flag.question.subject.name} - Unit {flag.question.unit.unitNumber}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {flag.question.promptEn.length > 50 
                        ? `${flag.question.promptEn.substring(0, 50)}...` 
                        : flag.question.promptEn}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{flag.child.name}</div>
                    <div className="text-sm text-gray-500">{flag.child.username}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {flag.submittedKey || flag.submittedText || 'N/A'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                      ${flag.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 
                        flag.status === 'REVIEWED' ? 'bg-blue-100 text-blue-800' :
                        flag.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                        'bg-red-100 text-red-800'}`}>
                      {flag.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(flag.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => setPreviewId(flag.question.id)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      Preview
                    </button>
                    {flag.status === 'PENDING' && (
                      <>
                        <button
                          onClick={() => handleStatusChange(flag.id, 'APPROVED')}
                          className="text-green-600 hover:text-green-900 mr-3"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleStatusChange(flag.id, 'REJECTED')}
                          className="text-red-600 hover:text-red-900"
                        >
                          Reject
                        </button>
                      </>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Question Preview Modal */}
      {previewId && (
        <QuestionModal
          id={previewId}
          onClose={() => setPreviewId(null)}
        />
      )}
    </div>
  );
}
