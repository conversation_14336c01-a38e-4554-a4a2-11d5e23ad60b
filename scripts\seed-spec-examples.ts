import { PrismaClient, QuestionType } from '@prisma/client';
import { validateSpec } from '../lib/validation/questionSpec';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed example questions with specs...');

  // First, ensure we have a subject, year, and unit to work with
  const subject = await prisma.subject.upsert({
    where: { name: 'Science' },
    update: {},
    create: { name: 'Science' }
  });

  const year = await prisma.year.upsert({
    where: { yearNumber: 5 },
    update: {},
    create: { yearNumber: 5 }
  });

  const unit = await prisma.unit.upsert({
    where: {
      unitNumber_subjectId_yearId: {
        unitNumber: 1,
        subjectId: subject.id,
        yearId: year.id
      }
    },
    update: {},
    create: {
      unitNumber: 1,
      topicEn: 'Earth and Space',
      topicZh: '地球与太空',
      subjectId: subject.id,
      yearId: year.id
    }
  });

  // Create a media item for use in questions
  const media = await prisma.media.create({
    data: {
      url: 'https://example.com/images/solar-system.jpg',
      altEn: 'The Solar System',
      altZh: '太阳系'
    }
  });

  // Example 1: Multiple Choice Image
  const multipleChoiceImageSpec = {
    shuffleChoices: true,
    choiceImageStyle: 'square'
  };

  await createQuestion(
    'q_mci_1',
    QuestionType.MULTIPLE_CHOICE_IMAGE,
    'Which planet is known for its rings?',
    '哪个行星以其环而闻名？',
    media.id,
    multipleChoiceImageSpec,
    subject.id,
    year.id,
    unit.id,
    'Planets',
    '行星',
    [
      { key: 'A', textEn: 'Earth', textZh: '地球', mediaId: media.id },
      { key: 'B', textEn: 'Mars', textZh: '火星', mediaId: media.id },
      { key: 'C', textEn: 'Saturn', textZh: '土星', mediaId: media.id },
      { key: 'D', textEn: 'Jupiter', textZh: '木星', mediaId: media.id }
    ],
    'C',
    'Saturn is known for its prominent ring system that consists of ice particles, rocky debris, and dust.',
    '土星以其由冰粒子、岩石碎片和尘埃组成的显著环系而闻名。'
  );

  // Example 2: Fill in the Blank
  const fillBlankSpec = {
    template: 'The Earth rotates on its ___ and revolves around the ___.',
    blanks: [
      {
        correct: 'axis',
        distractors: ['equator', 'poles', 'center']
      },
      {
        correct: 'Sun',
        distractors: ['Moon', 'stars', 'galaxy']
      }
    ],
    caseSensitive: false
  };

  await createQuestion(
    'q_fib_1',
    QuestionType.FILL_IN_THE_BLANK,
    'Complete the sentence about Earth\'s movement.',
    '完成关于地球运动的句子。',
    null,
    fillBlankSpec,
    subject.id,
    year.id,
    unit.id,
    'Earth Movement',
    '地球运动',
    [],
    JSON.stringify(['axis', 'Sun']),
    'The Earth rotates on its axis, which causes day and night, and revolves around the Sun, which causes the seasons.',
    '地球绕其轴旋转，这导致昼夜交替，并围绕太阳公转，这导致季节变化。'
  );

  // Example 3: True/False
  const trueFalseSpec = {
    statement: 'The Moon is larger than Earth.',
    correctAnswer: false
  };

  await createQuestion(
    'q_tf_1',
    QuestionType.TRUE_FALSE,
    'Determine if the statement is true or false.',
    '判断陈述是对还是错。',
    null,
    trueFalseSpec,
    subject.id,
    year.id,
    unit.id,
    'Moon Facts',
    '月球事实',
    [],
    'false',
    'The Moon is actually much smaller than Earth. The Moon\'s diameter is about 3,474 km, while Earth\'s diameter is about 12,742 km.',
    '月球实际上比地球小得多。月球的直径约为3,474公里，而地球的直径约为12,742公里。'
  );

  // Example 5: Sequencing
  const sequencingSpec = {
    items: [
      {
        textEn: 'Water evaporates from oceans and lakes',
        textZh: '水从海洋和湖泊蒸发',
        textMs: 'Air mengewap dari lautan dan tasik'
      },
      {
        textEn: 'Water vapor rises into the atmosphere',
        textZh: '水蒸气上升到大气中',
        textMs: 'Wap air naik ke atmosfera'
      },
      {
        textEn: 'Water vapor condenses to form clouds',
        textZh: '水蒸气凝结形成云',
        textMs: 'Wap air memeluwap untuk membentuk awan'
      },
      {
        textEn: 'Water falls as precipitation (rain, snow)',
        textZh: '水以降水形式落下（雨，雪）',
        textMs: 'Air jatuh sebagai hujan atau salji'
      }
    ],
    correctOrder: [0, 1, 2, 3],
    allowPartialCredit: false
  };

  await createQuestion(
    'q_seq_1',
    QuestionType.SEQUENCING,
    'Arrange the steps of the water cycle in the correct order.',
    '按正确顺序排列水循环的步骤。',
    null,
    sequencingSpec,
    subject.id,
    year.id,
    unit.id,
    'Water Cycle',
    '水循环',
    [],
    JSON.stringify([0, 1, 2, 3]),
    'The water cycle begins with evaporation from bodies of water, followed by the rise of water vapor, condensation into clouds, and finally precipitation.',
    '水循环始于水体的蒸发，然后是水蒸气的上升，凝结成云，最后是降水。'
  );

  // Example 4: Matching
  const matchingSpec = {
    pairs: [
      {
        left: 'Mercury',
        leftZh: '水星',
        leftMs: 'Merkuri',
        right: 'Closest to the Sun',
        rightZh: '离太阳最近',
        rightMs: 'Paling dekat dengan Matahari'
      },
      {
        left: 'Venus',
        leftZh: '金星',
        leftMs: 'Venus',
        right: 'Hottest planet',
        rightZh: '最热的行星',
        rightMs: 'Planet paling panas'
      },
      {
        left: 'Earth',
        leftZh: '地球',
        leftMs: 'Bumi',
        right: 'Only planet known to support life',
        rightZh: '唯一已知支持生命的行星',
        rightMs: 'Satu-satunya planet yang diketahui menyokong kehidupan'
      },
      {
        left: 'Mars',
        leftZh: '火星',
        leftMs: 'Marikh',
        right: 'Known as the Red Planet',
        rightZh: '被称为红色星球',
        rightMs: 'Dikenali sebagai Planet Merah'
      }
    ],
    shuffleLeft: false,
    shuffleRight: true
  };

  await createQuestion(
    'q_match_1',
    QuestionType.MATCHING,
    'Match each planet with its characteristic.',
    '将每个行星与其特征匹配。',
    null,
    matchingSpec,
    subject.id,
    year.id,
    unit.id,
    'Planet Characteristics',
    '行星特征',
    [],
    JSON.stringify({ 0: 0, 1: 1, 2: 2, 3: 3 }),
    'Mercury is closest to the Sun, Venus is the hottest planet, Earth is the only planet known to support life, and Mars is known as the Red Planet.',
    '水星离太阳最近，金星是最热的行星，地球是唯一已知支持生命的行星，火星被称为红色星球。'
  );

  console.log('Seeding completed successfully!');
}

async function createQuestion(
  questionId: string,
  type: QuestionType,
  promptEn: string,
  promptZh: string,
  promptMediaId: number | null,
  spec: any,
  subjectId: number,
  yearId: number,
  unitId: number,
  subTopicEn: string,
  subTopicZh: string,
  choices: { key: string; textEn: string; textZh: string; mediaId?: number }[],
  answerKey: string,
  explanationEn: string,
  explanationZh: string
) {
  try {
    // Validate the spec based on question type
    validateSpec(type, spec);

    // Create the question
    const question = await prisma.question.create({
      data: {
        questionId,
        type,
        promptEn,
        promptZh,
        promptMediaId,
        spec,
        subjectId,
        yearId,
        unitId,
        subTopicEn,
        subTopicZh,
        choices: {
          create: choices
        },
        explanation: {
          create: {
            textEn: explanationEn,
            textZh: explanationZh
          }
        },
        answer: {
          create: {
            key: type === QuestionType.MULTIPLE_CHOICE || type === QuestionType.MULTIPLE_CHOICE_IMAGE || type === QuestionType.TRUE_FALSE
              ? answerKey
              : null,
            textEn: type !== QuestionType.MULTIPLE_CHOICE && type !== QuestionType.MULTIPLE_CHOICE_IMAGE && type !== QuestionType.TRUE_FALSE
              ? answerKey
              : null,
            textZh: type !== QuestionType.MULTIPLE_CHOICE && type !== QuestionType.MULTIPLE_CHOICE_IMAGE && type !== QuestionType.TRUE_FALSE
              ? answerKey
              : null
          }
        }
      }
    });

    console.log(`Created question: ${questionId} (${type})`);
    return question;
  } catch (error) {
    console.error(`Error creating question ${questionId}:`, error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
