import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import prisma from '../../../lib/prisma';
import { QuestionType, Language } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

// Type for the request body
interface GenerateQuestionsRequest {
  yearId: number;
  subjectId: number;
  unitId?: number;
  numQuestions?: number;
  questionTypes: QuestionType[];
  provider: string; // 'openrouter' or 'gemini'
  model: string;
  language?: Language; // Optional, defaults to ZH in the schema
  tpDistribution?: number[]; // Optional array of TP levels (1-6) for distribution
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check authentication and admin role
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (session.user?.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Forbidden - Admin access required' });
    }

    // Parse and validate request body
    const {
      yearId,
      subjectId,
      unitId,
      numQuestions = 10, // Default to 10 questions if not specified
      questionTypes,
      provider = 'openrouter', // Default to OpenRouter if not specified
      model,
      language = Language.ZH, // Default to Chinese if not specified
      tpDistribution // Optional TP level distribution
    } = req.body as GenerateQuestionsRequest;

    // Validate required fields
    if (!yearId || !subjectId || !questionTypes || !model) {
      return res.status(400).json({ message: 'Missing required fields' });
    }

    // Validate numQuestions (max 100)
    if (numQuestions > 100) {
      return res.status(400).json({ message: 'Maximum number of questions is 100' });
    }

    // Validate questionTypes is not empty
    if (!Array.isArray(questionTypes) || questionTypes.length === 0) {
      return res.status(400).json({ message: 'Question types must be a non-empty array' });
    }

    // Fetch matching Note IDs
    const notes = await prisma.note.findMany({
      where: {
        yearId,
        subjectId,
        ...(unitId && { unitId }),
      },
      select: {
        id: true,
      },
    });

    if (notes.length === 0) {
      return res.status(404).json({ message: 'No notes found for the specified criteria' });
    }

    // Create a new generation batch
    const generationBatch = await prisma.generationBatch.create({
      data: {
        adminId: parseInt(session.user.id),
        yearId,
        subjectId,
        unitId,
        questionTypes,
        numQuestions,
        provider, // Add the provider field
        modelUsed: model,
        language, // Add the language field
        status: 'PENDING',
        notes: {
          connect: notes.map(note => ({ id: note.id })),
        }
        // Note: metadata field is not available in the schema yet
        // We'll store tpDistribution in the queue file only
      }
    });

    // Create queue marker file
    const queueDir = path.join(process.cwd(), 'queue');

    // Create the queue directory if it doesn't exist
    try {
      await fs.mkdir(queueDir, { recursive: true });
    } catch (mkdirError) {
      console.log(`Note: Queue directory creation attempted: ${mkdirError}`);
      // Continue even if there's an error, as the directory might already exist
    }

    const queueFile = path.join(queueDir, `generation-${generationBatch.id}.json`);

    await fs.writeFile(
      queueFile,
      JSON.stringify({
        batchId: generationBatch.id,
        provider: provider, // Include provider in the queue file
        language: language, // Include language in the queue file
        tpDistribution: tpDistribution // Include TP distribution if provided
      }),
      'utf-8'
    );

    // Return success response with batch ID
    return res.status(200).json({ ok: true, batchId: generationBatch.id });
  } catch (error) {
    console.error('Error generating questions:', error);
    return res.status(500).json({ message: 'Internal server error', error: String(error) });
  }
}
