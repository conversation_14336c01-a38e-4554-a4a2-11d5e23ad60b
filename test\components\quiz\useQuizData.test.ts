import { renderHook, act } from '@testing-library/react-hooks';
import { useSession } from 'next-auth/react';
import useSWR from 'swr';
import useQuizData from '../../../components/quiz/useQuizData';

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock SWR
jest.mock('swr', () => ({
  __esModule: true,
  default: jest.fn(),
}));

describe('useQuizData', () => {
  // Mock data
  const mockQuizData = {
    quizAttempt: {
      id: 123,
      currentQuestionIndex: 2,
      quizType: 'MASTERY',
    },
    questions: [
      { id: 1, type: 'MULTIPLE_CHOICE' },
      { id: 2, type: 'SHORT_ANSWER' },
      { id: 3, type: 'TRUE_FALSE' },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock authenticated session
    (useSession as jest.Mock).mockReturnValue({
      status: 'authenticated',
      data: { user: { id: '1' } },
    });
    
    // Mock SWR to return quiz data
    (useSWR as jest.Mock).mockReturnValue({
      data: mockQuizData,
      error: undefined,
      mutate: jest.fn(),
    });
    
    // Mock fetch for API calls
    global.fetch = jest.fn().mockImplementation(() => 
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ quizType: 'TEST' }),
      })
    );
  });

  it('should return quiz data when authenticated and attemptId is provided', () => {
    const { result } = renderHook(() => useQuizData('123'));
    
    expect(result.current.data).toEqual(mockQuizData);
    expect(result.current.error).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.quizType).toBe('MASTERY'); // Should use the quizType from the data
  });

  it('should not fetch data when not authenticated', () => {
    (useSession as jest.Mock).mockReturnValue({
      status: 'unauthenticated',
      data: null,
    });
    
    renderHook(() => useQuizData('123'));
    
    // SWR should be called with null as the key
    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should not fetch data when attemptId is missing', () => {
    renderHook(() => useQuizData(undefined));
    
    // SWR should be called with null as the key
    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should not fetch data when attemptId is an array', () => {
    renderHook(() => useQuizData(['123', '456']));
    
    // SWR should be called with null as the key
    expect(useSWR).toHaveBeenCalledWith(null, expect.any(Function));
  });

  it('should fetch quiz type separately if not included in the response', () => {
    // Mock SWR to return quiz data without quizType
    (useSWR as jest.Mock).mockReturnValue({
      data: {
        quizAttempt: {
          id: 123,
          currentQuestionIndex: 2,
        },
        questions: mockQuizData.questions,
      },
      error: undefined,
      mutate: jest.fn(),
    });
    
    const { result, waitForNextUpdate } = renderHook(() => useQuizData('123'));
    
    // Should default to TEST initially
    expect(result.current.quizType).toBe('TEST');
    
    // After the effect runs, it should fetch the quiz type
    expect(global.fetch).toHaveBeenCalledWith('/api/quiz-type?attemptId=123');
  });

  it('should handle errors when fetching quiz data', () => {
    const mockError = new Error('Failed to fetch');
    
    (useSWR as jest.Mock).mockReturnValue({
      data: undefined,
      error: mockError,
      mutate: jest.fn(),
    });
    
    const { result } = renderHook(() => useQuizData('123'));
    
    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBe(mockError);
    expect(result.current.isLoading).toBe(false);
  });

  it('should set up beforeunload event listener', () => {
    // Spy on addEventListener and removeEventListener
    const addEventListenerSpy = jest.spyOn(window, 'addEventListener');
    const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener');
    
    const { unmount } = renderHook(() => useQuizData('123'));
    
    // Should add beforeunload event listener
    expect(addEventListenerSpy).toHaveBeenCalledWith('beforeunload', expect.any(Function));
    
    // Unmount to trigger cleanup
    unmount();
    
    // Should remove beforeunload event listener
    expect(removeEventListenerSpy).toHaveBeenCalledWith('beforeunload', expect.any(Function));
  });
});
