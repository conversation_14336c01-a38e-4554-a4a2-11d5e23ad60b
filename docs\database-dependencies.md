# Database Dependencies and Deletion Order

This document outlines the dependencies between database tables and the correct order for deletion operations in the My Quiz App.

## Question Entity Dependencies

When deleting questions or modifying the Question schema, you must be aware of the following related entities and their dependencies:

### Deletion Order

When deleting `Question` records, you must delete related records in the following order due to foreign key constraints:

1. **Choice** - Multiple choice options for questions
2. **Answer** - Correct answers for questions
3. **ExplanationText** - Explanations for questions
4. **TranslationLog** - Logs of translation operations on questions
5. **StudentAnswer** - Student responses to questions
6. **Question** - The actual question records

### Example Code

Here's an example of how to properly delete questions with their related records:

```typescript
// Get the IDs of questions to be deleted
const questionsToDelete = await prisma.question.findMany({
  where: {
    // Your filter criteria here
    generationBatchId: null
  },
  select: {
    id: true
  }
});

const questionIds = questionsToDelete.map(q => q.id);

// 1. Delete related choices
await prisma.choice.deleteMany({
  where: {
    questionId: {
      in: questionIds
    }
  }
});

// 2. Delete related answers
await prisma.answer.deleteMany({
  where: {
    questionId: {
      in: questionIds
    }
  }
});

// 3. Delete related explanation texts
await prisma.explanationText.deleteMany({
  where: {
    questionId: {
      in: questionIds
    }
  }
});

// 4. Delete related translation logs
await prisma.translationLog.deleteMany({
  where: {
    questionId: {
      in: questionIds
    }
  }
});

// 5. Delete related student answers
await prisma.studentAnswer.deleteMany({
  where: {
    questionId: {
      in: questionIds
    }
  }
});

// 6. Finally delete the questions
await prisma.question.deleteMany({
  where: {
    id: {
      in: questionIds
    }
  }
});
```

## Other Important Dependencies

Add other entity dependencies here as they are identified.

## Schema Modifications

When modifying the database schema:

1. If adding new relations to the `Question` model, update this document with the new dependencies
2. Ensure that any scripts that delete questions are updated to handle the new relations
3. Consider the impact on existing data and whether migration scripts are needed

## Reference Scripts

For practical examples, refer to these scripts in the codebase:

- `scripts/delete-null-batch-questions.ts` - Deletes questions with null generationBatchId