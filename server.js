// server.js
const next = require('next');
const express = require('express');
const path = require('path');

const hostname = process.env.HOSTNAME || '127.0.0.1';
const port = parseInt(process.env.PORT, 10) || 3000;

const nextApp = next({ dev: true });
const nextHandler = nextApp.getRequestHandler();

nextApp.prepare().then(() => {
  const app = express();

  // Serve static files from the uploads directory
  app.use('/uploads', express.static(path.join(process.cwd(), 'uploads')));

  // Handle all other requests with Next.js
  app.use((req, res) => {
    return nextHandler(req, res);
  });

  // Start the server
  app.listen(port, hostname, err => {
    if (err) throw err;
    console.log(`> Server listening at http://${hostname}:${port}`);
  });
});
