import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { QuizProvider } from '../components/quiz/QuizContext';
import AiTutorPanel from '../components/quiz/AiTutorPanel';

// Sample question for testing
const sampleQuestion = {
  id: 1,
  promptEn: 'What is photosynthesis?',
  promptZh: '什么是光合作用？',
  promptMs: 'Apakah fotosintesis?',
  originalLanguage: 'ZH',
  type: 'MULTIPLE_CHOICE',
  choices: [
    { key: 'A', textEn: 'A process where plants make food using sunlight', textZh: '植物利用阳光制造食物的过程' },
    { key: 'B', textEn: 'A process where animals digest food', textZh: '动物消化食物的过程' },
    { key: 'C', textEn: 'A type of plant disease', textZh: '一种植物疾病' },
    { key: 'D', textEn: 'A chemical reaction in the atmosphere', textZh: '大气中的化学反应' }
  ],
  answer: 'A',
  topic: 'Science'
};

export default function TestDirectMessage() {
  const [displayLanguage, setDisplayLanguage] = useState<'en' | 'zh' | 'ms'>('en');
  const [originalText, setOriginalText] = useState('光合作用是绿色植物和一些其他生物利用阳光在叶绿素的帮助下合成食物的过程。');
  const [translatedText, setTranslatedText] = useState('Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with the help of chlorophyll.');
  
  // Toggle language
  const toggleLanguage = () => {
    setDisplayLanguage(prev => prev === 'en' ? 'zh' : prev === 'zh' ? 'ms' : 'en');
  };
  
  // Add a direct message to the AI tutor chat
  const addDirectMessage = () => {
    const translationPrefix = displayLanguage === 'en'
      ? 'Translation of "'
      : displayLanguage === 'ms'
        ? 'Terjemahan untuk "'
        : '翻译 "';

    const translationSuffix = displayLanguage === 'en'
      ? '": '
      : displayLanguage === 'ms'
        ? '": '
        : '": ';

    const message = `${translationPrefix}${originalText}${translationSuffix}${translatedText}`;
    
    console.log('Attempting to add direct message to AI tutor chat:', message);
    
    // Try all possible methods to add the message
    
    // Method 1: Use the global function if available
    if (typeof window !== 'undefined' && (window as any).__addAiTutorMessage) {
      console.log('Using global __addAiTutorMessage function');
      (window as any).__addAiTutorMessage('ai', message);
    } else {
      console.log('Global __addAiTutorMessage function not available');
    }
    
    // Method 2: Dispatch a custom event
    if (typeof window !== 'undefined') {
      console.log('Dispatching translationComplete event');
      const event = new CustomEvent('translationComplete', {
        detail: {
          translatedText,
          originalText
        }
      });
      window.dispatchEvent(event);
      
      console.log('Dispatching directTranslationMessage event');
      const directEvent = new CustomEvent('directTranslationMessage', {
        detail: {
          message,
          sender: 'ai',
          translatedText,
          originalText
        }
      });
      window.dispatchEvent(directEvent);
    }
    
    // Method 3: Set a global variable
    if (typeof window !== 'undefined') {
      console.log('Setting global __lastTranslation variable');
      (window as any).__lastTranslation = {
        message,
        sender: 'ai',
        translatedText,
        originalText,
        timestamp: new Date().toISOString()
      };
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Head>
        <title>Test Direct Message</title>
      </Head>
      
      <main className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Direct Message Test Page</h1>
        
        <div className="mb-4">
          <button 
            onClick={toggleLanguage}
            className="bg-blue-500 text-white px-4 py-2 rounded mr-2"
          >
            {displayLanguage === 'en' ? 'Switch to Chinese' : 
             displayLanguage === 'zh' ? 'Switch to Malay' : 'Switch to English'}
          </button>
        </div>
        
        <QuizProvider initialQuestions={[sampleQuestion]}>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="bg-white p-4 rounded-lg shadow-md flex-1">
              <h2 className="text-xl font-semibold mb-2">
                {displayLanguage === 'en' ? 'Test Direct Message' : 
                 displayLanguage === 'zh' ? '测试直接消息' : 'Ujian Mesej Langsung'}
              </h2>
              
              <div className="mb-4">
                <p className="font-bold">
                  {displayLanguage === 'en' ? 'Instructions:' : 
                   displayLanguage === 'zh' ? '说明：' : 'Arahan:'}
                </p>
                <p>
                  {displayLanguage === 'en' 
                    ? 'Fill in the text fields below and click the "Add Direct Message" button to add a message to the AI tutor chat.' 
                    : displayLanguage === 'zh'
                      ? '填写下面的文本字段，然后点击"添加直接消息"按钮将消息添加到AI导师聊天中。'
                      : 'Isi medan teks di bawah dan klik butang "Tambah Mesej Langsung" untuk menambah mesej ke sembang tutor AI.'}
                </p>
              </div>
              
              <div className="mb-4">
                <label className="block mb-2 font-semibold">
                  {displayLanguage === 'en' ? 'Original Text:' : 
                   displayLanguage === 'zh' ? '原文：' : 'Teks Asal:'}
                </label>
                <textarea 
                  value={originalText}
                  onChange={(e) => setOriginalText(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={3}
                />
              </div>
              
              <div className="mb-4">
                <label className="block mb-2 font-semibold">
                  {displayLanguage === 'en' ? 'Translated Text:' : 
                   displayLanguage === 'zh' ? '翻译文本：' : 'Teks Terjemahan:'}
                </label>
                <textarea 
                  value={translatedText}
                  onChange={(e) => setTranslatedText(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded"
                  rows={3}
                />
              </div>
              
              <button 
                onClick={addDirectMessage}
                className="bg-green-500 text-white px-4 py-2 rounded"
              >
                {displayLanguage === 'en' ? 'Add Direct Message' : 
                 displayLanguage === 'zh' ? '添加直接消息' : 'Tambah Mesej Langsung'}
              </button>
            </div>
            
            <div className="bg-white p-4 rounded-lg shadow-md md:w-2/5">
              <h2 className="text-xl font-semibold mb-2">
                {displayLanguage === 'en' ? 'AI Tutor Chat' : 
                 displayLanguage === 'zh' ? 'AI 导师聊天' : 'Sembang Tutor AI'}
              </h2>
              
              <AiTutorPanel 
                displayLanguage={displayLanguage === 'ms' ? 'en' : displayLanguage} 
                visible={true} 
              />
            </div>
          </div>
        </QuizProvider>
      </main>
    </div>
  );
}
