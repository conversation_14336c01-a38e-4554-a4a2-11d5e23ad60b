import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import QuestionGenerator from '../../../components/admin/QuestionGenerator';
import { QuestionType, Language } from '@prisma/client';

// Mock fetch
const originalFetch = global.fetch;

describe('QuestionGenerator Component', () => {
  beforeEach(() => {
    // Mock fetch before each test
    global.fetch = jest.fn();
    
    // Mock successful API responses
    (global.fetch as jest.Mock).mockImplementation((url) => {
      if (url.includes('/api/admin/years')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([
            { id: 1, yearNumber: 1 },
            { id: 2, yearNumber: 2 }
          ]),
        });
      } else if (url.includes('/api/admin/subjects')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([
            { id: 1, name: 'Mathematics' },
            { id: 2, name: '<PERSON>' }
          ]),
        });
      } else if (url.includes('/api/admin/units')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([
            { id: 1, unitNumber: 1, topicEn: 'Numbers', topicZh: '数字' },
            { id: 2, unitNumber: 2, topicEn: 'Shapes', topicZh: '形状' }
          ]),
        });
      } else if (url.includes('/api/admin/generate-questions')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ ok: true, batchId: 123 }),
        });
      } else if (url.includes('/api/admin/generation-batches')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve([]),
        });
      }
      
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
      });
    });
  });

  afterEach(() => {
    // Restore original fetch after each test
    global.fetch = originalFetch;
  });

  test('renders the form with all required fields', async () => {
    render(<QuestionGenerator />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Generate Questions')).toBeInTheDocument();
    });
    
    // Check for form elements
    expect(screen.getByText('Year *')).toBeInTheDocument();
    expect(screen.getByText('Subject *')).toBeInTheDocument();
    expect(screen.getByText('Unit')).toBeInTheDocument();
    expect(screen.getByText('Number of Questions *')).toBeInTheDocument();
    expect(screen.getByText('Question Types *')).toBeInTheDocument();
    expect(screen.getByText('AI Provider *')).toBeInTheDocument();
    expect(screen.getByText('AI Model *')).toBeInTheDocument();
    expect(screen.getByText('Language')).toBeInTheDocument();
    expect(screen.getByText('Generate')).toBeInTheDocument();
  });

  test('submits the form with valid data', async () => {
    render(<QuestionGenerator />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Generate Questions')).toBeInTheDocument();
    });
    
    // Fill out the form
    // Select Year
    fireEvent.change(screen.getByLabelText(/Year/i), { target: { value: '1' } });
    
    // Select Subject
    fireEvent.change(screen.getByLabelText(/Subject/i), { target: { value: '1' } });
    
    // Select Unit (optional)
    fireEvent.change(screen.getByLabelText(/Unit/i), { target: { value: '1' } });
    
    // Set Number of Questions
    fireEvent.change(screen.getByLabelText(/Number of Questions/i), { target: { value: '5' } });
    
    // Select Question Types (assuming checkboxes)
    const multipleChoiceCheckbox = screen.getByLabelText(/Multiple Choice/i);
    fireEvent.click(multipleChoiceCheckbox);
    
    // Select Provider
    fireEvent.change(screen.getByLabelText(/AI Provider/i), { target: { value: 'openrouter' } });
    
    // Select Model
    fireEvent.change(screen.getByLabelText(/AI Model/i), { target: { value: 'anthropic/claude-3-opus:beta' } });
    
    // Select Language
    fireEvent.change(screen.getByLabelText(/Language/i), { target: { value: 'ZH' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('Generate'));
    
    // Wait for the API call
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/admin/generate-questions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          }),
          body: expect.any(String)
        })
      );
    });
    
    // Verify the payload
    const calls = (global.fetch as jest.Mock).mock.calls;
    const generateCall = calls.find(call => call[0] === '/api/admin/generate-questions');
    const payload = JSON.parse(generateCall[1].body);
    
    expect(payload).toEqual(expect.objectContaining({
      yearId: 1,
      subjectId: 1,
      unitId: 1,
      numQuestions: 5,
      questionTypes: expect.arrayContaining([QuestionType.MULTIPLE_CHOICE]),
      provider: 'openrouter',
      model: 'anthropic/claude-3-opus:beta',
      language: Language.ZH
    }));
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText(/Job queued successfully/i)).toBeInTheDocument();
    });
  });

  test('handles API errors', async () => {
    // Override the mock for generate-questions to return an error
    (global.fetch as jest.Mock).mockImplementation((url) => {
      if (url.includes('/api/admin/generate-questions')) {
        return Promise.resolve({
          ok: false,
          json: () => Promise.resolve({ message: 'Missing required fields' }),
        });
      }
      
      // Use the default implementation for other URLs
      return originalFetch(url);
    });
    
    render(<QuestionGenerator />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Generate Questions')).toBeInTheDocument();
    });
    
    // Fill out minimal form data and submit
    fireEvent.change(screen.getByLabelText(/Year/i), { target: { value: '1' } });
    fireEvent.change(screen.getByLabelText(/Subject/i), { target: { value: '1' } });
    fireEvent.click(screen.getByLabelText(/Multiple Choice/i));
    fireEvent.change(screen.getByLabelText(/AI Provider/i), { target: { value: 'openrouter' } });
    fireEvent.change(screen.getByLabelText(/AI Model/i), { target: { value: 'anthropic/claude-3-opus:beta' } });
    
    // Submit the form
    fireEvent.click(screen.getByText('Generate'));
    
    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(/Error: Missing required fields/i)).toBeInTheDocument();
    });
  });
});
