import { useState } from 'react';

export default function Register() {
  const [step, setStep] = useState(1);
  const [form, setForm] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    children: [{ name: '', year: 'Year 1', username: '', pin: '' }],
  });
  const [selectedPlan, setSelectedPlan] = useState('');
  const [voucherCode, setVoucherCode] = useState('');
  const [voucherApplied, setVoucherApplied] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, index?: number) => {
    if (index !== undefined) {
      const updatedChildren = [...form.children];
      updatedChildren[index] = { ...updatedChildren[index], [e.target.name]: e.target.value };
      setForm({ ...form, children: updatedChildren });
    } else {
      setForm({ ...form, [e.target.name]: e.target.value });
    }
  };

  const handleAddChild = () => {
    setForm({
      ...form,
      children: [...form.children, { name: '', year: 'Year 1', username: '', pin: '' }],
    });
  };

  const handleRemoveChild = (index: number) => {
    if (index === 0) return; // Prevent removing the first child
    const updatedChildren = form.children.filter((_, i) => i !== index);
    setForm({ ...form, children: updatedChildren });
  };

  const handleContinue = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 1 && form.password !== form.confirmPassword) {
      alert('Passwords do not match');
      return;
    }
    setStep(step + 1);
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const handleApplyVoucher = () => {
    if (voucherCode.trim() === '') {
      alert('Please enter a voucher code.');
      return;
    }
    // TODO: Add logic to validate and apply the voucher code
    setVoucherApplied(true);
    alert('Voucher applied successfully!');
  };

  const handleResendEmail = () => {
    // TODO: Add logic to resend the verification email
    alert('Verification email resent successfully!');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-8">
        {step === 1 && (
          <>
            <div className="mb-6 border-b pb-4">
              <h2 className="text-lg font-semibold text-gray-700">STEP 1 of 4: Create Your Account</h2>
            </div>
            <form onSubmit={handleContinue}>
              <div className="mb-4">
                <label className="block text-gray-700 mb-1" htmlFor="fullName">Full Name:</label>
                <input
                  id="fullName"
                  name="fullName"
                  type="text"
                  value={form.fullName}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 mb-1" htmlFor="email">Email:</label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={form.email}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="mb-4">
                <label className="block text-gray-700 mb-1" htmlFor="password">Password:</label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  value={form.password}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="mb-6">
                <label className="block text-gray-700 mb-1" htmlFor="confirmPassword">Confirm Password:</label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={form.confirmPassword}
                  onChange={handleChange}
                  className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div className="flex items-center justify-between mb-2">
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 font-semibold"
                >
                  Continue
                </button>
                <div className="text-sm text-gray-600 flex flex-col items-end">
                  <span>Already have an account?</span>
                  <a href="/login" className="text-blue-600 hover:underline font-medium">Log In</a>
                </div>
              </div>
            </form>
          </>
        )}

        {step === 2 && (
          <>
            <div className="mb-6 border-b pb-4">
              <h2 className="text-lg font-semibold text-gray-700">STEP 2 of 4: Add Your Child(ren)</h2>
            </div>
            <form onSubmit={handleContinue}>
              {form.children.map((child, index) => (
                <div key={index} className="mb-6 border-b pb-4">
                  <h3 className="text-md font-medium text-gray-600 mb-2">Manual Entry for Child #{index + 1}:</h3>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-1" htmlFor={`child-name-${index}`}>Name:</label>
                    <input
                      id={`child-name-${index}`}
                      name="name"
                      type="text"
                      value={child.name}
                      onChange={(e) => handleChange(e, index)}
                      className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-1" htmlFor={`child-year-${index}`}>Year:</label>
                    <select
                      id={`child-year-${index}`}
                      name="year"
                      value={child.year}
                      onChange={(e) => handleChange(e, index)}
                      className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5', 'Year 6'].map((year) => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-1" htmlFor={`child-username-${index}`}>Username:</label>
                    <input
                      id={`child-username-${index}`}
                      name="username"
                      type="text"
                      value={child.username}
                      onChange={(e) => handleChange(e, index)}
                      className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>
                  <div className="mb-4">
                    <label className="block text-gray-700 mb-1" htmlFor={`child-pin-${index}`}>PIN:</label>
                    <input
                      id={`child-pin-${index}`}
                      name="pin"
                      type="text"
                      value={child.pin}
                      onChange={(e) => handleChange(e, index)}
                      className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      maxLength={6}
                      required
                    />
                  </div>
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => handleRemoveChild(index)}
                      className="text-red-600 hover:underline font-medium"
                    >
                      Cancel
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={handleAddChild}
                className="text-blue-600 hover:underline font-medium mb-6"
              >
                + Add Another Child
              </button>
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={handleBack}
                  className="bg-gray-300 text-gray-700 px-6 py-2 rounded hover:bg-gray-400 font-semibold"
                >
                  Back
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 font-semibold"
                >
                  Continue
                </button>
              </div>
            </form>
          </>
        )}

        {step === 3 && (
          <>
            <div className="mb-6 border-b pb-4">
              <h2 className="text-lg font-semibold text-gray-700">STEP 3 of 4: Choose Plan or Apply Voucher</h2>
            </div>
            <form onSubmit={handleContinue}>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">
                  <input
                    type="radio"
                    name="plan"
                    value="free-trial"
                    checked={selectedPlan === 'free-trial'}
                    onChange={(e) => setSelectedPlan(e.target.value)}
                    className="mr-2"
                  />
                  Free Trial (7 days)
                </label>
                <label className="block text-gray-700 mb-2">
                  <input
                    type="radio"
                    name="plan"
                    value="standard-plan"
                    checked={selectedPlan === 'standard-plan'}
                    onChange={(e) => setSelectedPlan(e.target.value)}
                    className="mr-2"
                  />
                  Standard Plan
                </label>
              </div>
              <div className="mb-6">
                <label className="block text-gray-700 mb-2">
                  <input
                    type="checkbox"
                    checked={voucherApplied}
                    onChange={() => setVoucherApplied(!voucherApplied)}
                    className="mr-2"
                  />
                  I have a voucher code
                </label>
                {voucherApplied && (
                  <div className="flex items-center space-x-2 mt-2">
                    <input
                      type="text"
                      value={voucherCode}
                      onChange={(e) => setVoucherCode(e.target.value)}
                      placeholder="Enter voucher code"
                      className="flex-1 border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <button
                      type="button"
                      onClick={handleApplyVoucher}
                      className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold"
                    >
                      Apply Voucher
                    </button>
                  </div>
                )}
              </div>
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={handleBack}
                  className="bg-gray-300 text-gray-700 px-6 py-2 rounded hover:bg-gray-400 font-semibold"
                >
                  Back
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 font-semibold"
                >
                  Continue
                </button>
              </div>
            </form>
          </>
        )}

        {step === 4 && (
          <>
            <div className="mb-6 border-b pb-4">
              <h2 className="text-lg font-semibold text-gray-700">STEP 4 of 4: Verify & Onboard</h2>
            </div>
            <div className="mb-6">
              <p className="text-gray-700 mb-4">✔ A verification link has been sent to your email. Please click it to proceed.</p>
              <p className="text-gray-700 font-medium mb-4">Welcome! Here’s what you can do next:</p>
              <ul className="list-disc list-inside text-gray-700 space-y-2">
                <li>View Child Insights</li>
                <li>Set Weekly Progress Alerts</li>
                <li>Assign Homework</li>
              </ul>
            </div>
            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={handleResendEmail}
                className="bg-gray-300 text-gray-700 px-6 py-2 rounded hover:bg-gray-400 font-semibold"
              >
                Resend Email
              </button>
              <a
                href="/dashboard"
                className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 font-semibold text-center"
              >
                Go To Dashboard
              </a>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
