# Production Environment Configuration

# Logging Configuration
LOG_LEVEL=warn

# Database connection
DATABASE_URL="postgresql://username:password@host:port/database"

# NextAuth configuration
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-production-secret-key-here

# Storage configuration
STORAGE_DRIVER=local

# PostgreSQL configuration
POSTGRES_USER=appuser
POSTGRES_PASSWORD=your-production-password
POSTGRES_DB=education

# AI API keys for question generation and AI tutor
OPENROUTER_API_KEY=your-production-openrouter-api-key
GEMINI_API_KEY=your-production-gemini-api-key

# === Adaptive TP-Mastery ===
FEATURE_ADAPTIVE_V2=on
NEXT_PUBLIC_FEATURE_ADAPTIVE_V2=on

# === Quiz Version ===
NEXT_PUBLIC_QUIZ_VERSION=v2

# API Configuration
NEXT_PUBLIC_API_URL=https://your-api-domain.com

# === Error Tracking (Optional) ===
# NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn-here
