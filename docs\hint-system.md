# Hint System Documentation

## 1. Overview

The hint system in the quiz application provides students with progressive assistance when they're struggling with questions. It's designed to offer different levels of help without immediately giving away the answer, encouraging students to think critically and learn from their mistakes.

## 2. Key Components

### 2.1 HintFab Component

**File:** `components/quizV2/HintFab.tsx`

**Purpose:** Provides a floating action button (FAB) or inline button that students can click to request hints.

**Features:**
- Supports two display modes: floating button (default) or inline button
- Shows an infinity symbol (∞) to indicate unlimited hints are available
- Implements a progressive hint system with two stages:
  1. First click: Highlights keywords in the question
  2. Second click: Provides a more detailed hint

**Usage:**
```jsx
// Floating button (default)
<HintFab />

// Inline button (for header placement)
<HintFab inline={true} />
```

**Visibility Control:**
- Only visible when `allowHints` is true in quiz configuration
- Only available during the PRIMARY phase of the quiz (not during REVIEW)
- Disabled when an explanation is already showing

### 2.2 HintSheet Component

**File:** `components/quizV2/HintSheet.tsx`

**Purpose:** Displays the hint content in a sheet that slides up from the bottom of the screen.

**Features:**
- Renders markdown content for rich text formatting
- Includes a close button to dismiss the hint
- Automatically appears when a hint is requested and `explanation` state is set

**Usage:**
```jsx
// Typically included in the layout component
<HintSheet />
```

**Note:** The orange "Got it" button previously at the bottom of this component has been removed, as this feedback is now displayed elsewhere in the interface.

### 2.3 NextButtonBar Component

**File:** `components/quizV2/NextButtonBar.tsx`

**Purpose:** Provides navigation controls and includes a hint button in the button bar.

**Features:**
- Includes a HINT button alongside the NEXT/CHECK/CONTINUE buttons
- Handles hint requests through the `fetchHint` function
- Shows loading state while fetching hints

### 2.4 TeachMeModal Component

**File:** `components/quiz/TeachMeModal.tsx`

**Purpose:** Provides more comprehensive explanations after multiple incorrect attempts.

**Features:**
- Appears after three incorrect attempts on a question
- Displays a detailed explanation of the concept
- Offers options to try again or move to an easier question
- Supports multiple languages based on the display language setting

## 3. Configuration and Settings

### 3.1 Quiz Configuration

Hint functionality is controlled by the `allowHints` setting in the quiz configuration:

**Database Schema:**
```prisma
model QuizConfig {
  id                   Int        @id @default(autoincrement())
  mode                 QuizMode
  numQuestions         Int        @default(10)
  questionTypes        String[]   @default([])
  allowTranslate       Boolean    @default(true)
  allowHints           Boolean    @default(true)
  allowAiTutor         Boolean    @default(true)
  reviewMissedQuestions Boolean   @default(true)
  updatedAt            DateTime   @updatedAt

  @@unique([mode])
  @@map("TG_QuizConfig")
}
```

**Default Settings:**
- MASTERY mode: `allowHints = true` (hints enabled)
- TEST mode: `allowHints = false` (hints disabled)

### 3.2 Configuration Snapshot

When a quiz is created, a snapshot of the current configuration is stored in the quiz attempt's metadata to ensure consistent behavior:

```json
{
  "metadata": {
    "configSnapshot": {
      "allowTranslate": true,
      "allowHints": true,
      "allowAiTutor": true,
      "reviewMissedQuestions": true
    }
  }
}
```

### 3.3 Admin Configuration

Administrators can configure hint settings through the admin dashboard:
- Path: `/admin/quiz-config`
- Component: `components/admin/QuizConfigCard.tsx`
- API: `pages/api/admin/quiz-config.ts`

## 4. Hint System Flow

### 4.1 Hint Request Flow

1. Student clicks the hint button (HintFab or button in NextButtonBar)
2. The `fetchHint` function in QuizV2Provider is called
3. A request is sent to `/api/ai-tutor/hint` with the question ID
4. The API returns a hint based on the question
5. The hint is stored in the `explanation` state
6. The HintSheet component displays the hint to the student

### 4.2 Progressive Hint System (HintFab)

The HintFab component implements a two-stage hint system:

1. **Stage 1 (Keywords):**
   - First click calls `/api/ai-tutor/highlight`
   - Highlights important keywords in the question
   - Logs hint usage with `stage: 1`

2. **Stage 2 (Full Hint):**
   - Second click calls `/api/ai-tutor/hint`
   - Provides a more detailed hint about the concept
   - Logs hint usage with `stage: 2`

### 4.3 Incorrect Answer Progression

For mastery quizzes, the system provides increasing levels of assistance after incorrect answers:

1. **First incorrect attempt:**
   - Shows a "Try Again" message
   - Makes hint button available

2. **After multiple incorrect attempts:**
   - Shows the correct answer with a green checkmark on the right side
   - Provides an explanation of why the answer is correct

3. **After three incorrect attempts:**
   - Shows the TeachMeModal with a detailed explanation
   - Offers options to try again or move to an easier question

## 5. Hint Logging

The system logs hint usage for analytics and tracking:

**Database Schema:**
```prisma
model HintLog {
  id          Int      @id @default(autoincrement())
  questionId  Int
  childId     Int
  hint        String
  timestamp   DateTime @default(now())

  @@map("TG_HintLog")
}
```

**API Endpoint:**
- `/api/log-hint` - Records when a student uses a hint

## 6. Integration with Other Features

### 6.1 AI Tutor Integration

The hint system works alongside the AI Tutor feature:
- Both features can be enabled/disabled independently
- AI Tutor provides more interactive assistance through a chat interface
- Hints provide quick, targeted help for specific questions

### 6.2 Language Support

Hints support multiple languages:
- The hint API accepts a `language` parameter to provide hints in the student's preferred language
- Error messages for hint fetching are displayed in the current display language

### 6.3 Review Phase

During the REVIEW phase of a quiz:
- Hint functionality is disabled (`helpEnabled = false`)
- Students must rely on their knowledge without additional assistance

## 7. Implementation Notes

### 7.1 Component Hierarchy

```
QuizV2Layout
├── HintFab (floating or inline)
└── HintSheet (appears when a hint is active)

NextButtonBar
└── Hint Button (alternative way to request hints)
```

### 7.2 State Management

The hint system relies on several state variables in the QuizV2Provider:
- `explanation`: Stores the current hint text
- `allowHints`: Whether hints are enabled for this quiz
- `helpEnabled`: Whether help features are available in the current phase
- `isLoadingHint`: Tracks the loading state when fetching hints

## 8. Best Practices for Extending the Hint System

1. **Adding New Hint Types:**
   - Extend the `stage` state in HintFab to support additional hint levels
   - Add new API endpoints for different types of hints
   - Update the hint logging to track the new hint types

2. **Customizing Hint Appearance:**
   - Modify the HintSheet component to change the presentation of hints
   - Use different styling based on the hint type or content

3. **Implementing Hint Limits:**
   - Add a counter to track the number of hints used
   - Update the UI to show the remaining hints
   - Implement logic to disable hints when the limit is reached

4. **Enhancing Hint Analytics:**
   - Extend the HintLog model to capture more detailed information
   - Create admin reports to analyze hint usage patterns
   - Use hint data to improve question quality
