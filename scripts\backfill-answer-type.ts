import prisma from '../lib/prisma';

async function main() {
  console.log('Starting backfill of Answer.type field...');
  
  // Update all answers to SINGLE_CHOICE by default
  const singleChoiceCount = await prisma.answer.updateMany({
    where: {
      key: { not: null }
    },
    data: { 
      type: 'SINGLE_CHOICE'
    }
  });
  
  console.log(`Updated ${singleChoiceCount.count} answers to SINGLE_CHOICE`);
  
  // Update answers with text to SHORT_TEXT
  const shortTextCount = await prisma.answer.updateMany({
    where: {
      OR: [
        { textEn: { not: null } },
        { textZh: { not: null } },
        { textMs: { not: null } }
      ]
    },
    data: { 
      type: 'SHORT_TEXT'
    }
  });
  
  console.log(`Updated ${shortTextCount.count} answers to SHORT_TEXT`);
  
  // Find answers that might be TRUE_FALSE based on question type
  const trueFalseCount = await prisma.answer.updateMany({
    where: {
      question: {
        type: 'TRUE_FALSE'
      }
    },
    data: {
      type: 'TRUE_FALSE'
    }
  });
  
  console.log(`Updated ${trueFalseCount.count} answers to TRUE_FALSE`);
  
  // Find answers that might be FILL_IN_THE_BLANK based on question type
  const fillInBlankCount = await prisma.answer.updateMany({
    where: {
      question: {
        type: 'FILL_IN_THE_BLANK'
      }
    },
    data: {
      type: 'FILL_IN_THE_BLANK'
    }
  });
  
  console.log(`Updated ${fillInBlankCount.count} answers to FILL_IN_THE_BLANK`);
  
  console.log('Answer.type back-filled successfully');
  
  // Migrate submittedAnswer to submittedKey or submittedText
  console.log('Starting migration of StudentAnswer fields...');
  
  // Get all student answers
  const studentAnswers = await prisma.studentAnswer.findMany({
    include: {
      question: {
        include: {
          answer: true
        }
      }
    }
  });
  
  let keyCount = 0;
  let textCount = 0;
  
  // Process each student answer
  for (const sa of studentAnswers) {
    const answerType = sa.question.answer?.type || 'SINGLE_CHOICE';
    
    if (answerType === 'SINGLE_CHOICE' || answerType === 'TRUE_FALSE') {
      await prisma.studentAnswer.update({
        where: { id: sa.id },
        data: {
          submittedKey: sa['submittedAnswer'] as string
        }
      });
      keyCount++;
    } else if (answerType === 'SHORT_TEXT' || answerType === 'FILL_IN_THE_BLANK') {
      await prisma.studentAnswer.update({
        where: { id: sa.id },
        data: {
          submittedText: sa['submittedAnswer'] as string
        }
      });
      textCount++;
    }
  }
  
  console.log(`Updated ${keyCount} student answers to use submittedKey`);
  console.log(`Updated ${textCount} student answers to use submittedText`);
  console.log('StudentAnswer fields migrated successfully');
}

main()
  .catch((e) => {
    console.error('Error during backfill:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
    process.exit(0);
  });
