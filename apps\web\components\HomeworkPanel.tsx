import React, { useState } from 'react';
import useSWR, { mutate } from 'swr';
import { Language } from '@prisma/client';

interface HomeworkItem {
  id: number;
  subject: string;
  unitNumber: number | null;
  topic: {
    en: string | undefined;
    zh: string | undefined;
    ms: string | undefined;
  };
  progress: string;
  quizType: 'mastery' | 'test' | 'quick';
}

interface HomeworkPanelProps {
  childId: number;
  lang: Language;
}

/**
 * Picks the appropriate prompt text based on the language
 */
function pickPrompt({
  promptEn,
  promptZh,
  promptMs,
  originalLanguage
}: {
  promptEn?: string;
  promptZh?: string;
  promptMs?: string;
  originalLanguage: Language;
}): string {
  switch (originalLanguage) {
    case 'EN':
      return promptEn || '';
    case 'MS':
      return promptMs || promptEn || '';
    default: // Default to Chinese
      return promptZh || '';
  }
}

export default function HomeworkPanel({ childId, lang }: HomeworkPanelProps) {
  const [cancellingId, setCancellingId] = useState<number | null>(null);
  const { data, error } = useSWR<HomeworkItem[]>(
    `/api/homework?childId=${childId}`,
    (url: string) => fetch(url).then(r => r.json())
  );

  // Track analytics event (would implement with actual analytics library)
  const track = (event: string, properties: any) => {
    console.log(`Analytics event: ${event}`, properties);
    // In a real implementation, this would call your analytics tracking function
  };

  // Call analytics tracking when data loads
  React.useEffect(() => {
    if (data) {
      track('HomeworkPanelShown', { count: data.length });
    }
  }, [data]);

  // Handle quiz cancellation
  const handleCancelQuiz = async (attemptId: number) => {
    if (window.confirm('Are you sure you want to cancel this quiz attempt?')) {
      setCancellingId(attemptId);
      try {
        const response = await fetch('/api/quiz/cancel', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ attemptId }),
        });

        if (!response.ok) {
          throw new Error(`Error canceling quiz: ${response.statusText}`);
        }

        // Refresh the homework data
        mutate(`/api/homework?childId=${childId}`);
        alert('Quiz attempt canceled.');
      } catch (error) {
        console.error('Error canceling quiz:', error);
        alert('Failed to cancel quiz. Please try again.');
      } finally {
        setCancellingId(null);
      }
    }
  };

  if (error) return <p className="text-red-500">Error loading homework</p>;
  if (!data) return <p className="text-gray-500">Loading…</p>;

  // Function to handle starting a new quiz
  const handleStartQuiz = () => {
    // Navigate to the start quiz wizard
    window.location.href = '/start-quiz';
  };

  return (
    <section className="mt-6">
      <h2 className="text-lg font-semibold mb-2">Your Homework</h2>
      {data.length === 0 && <p className="text-gray-500 text-center py-8">No incomplete quizzes. 🎉</p>}
      <ul className="space-y-3">
        {data.map(q => (
          <li key={q.id} className="border p-3 rounded flex justify-between items-center">
            <div>
              <p className="font-medium">{q.subject} {q.unitNumber ? `· Unit ${q.unitNumber}` : ''}</p>
              <p className="text-sm text-gray-600">
                {pickPrompt({
                  promptEn: q.topic.en,
                  promptZh: q.topic.zh,
                  promptMs: q.topic.ms,
                  originalLanguage: lang
                })}
              </p>
              <div className="flex items-center mt-1">
                <p className="text-xs text-gray-500 mr-2">{q.progress} completed</p>
                <span className={`text-xs px-2 py-0.5 rounded-full ${
                  q.quizType === 'mastery' ? 'bg-blue-100 text-blue-800' :
                  q.quizType === 'test' ? 'bg-purple-100 text-purple-800' :
                  'bg-green-100 text-green-800'
                }`}>
                  {q.quizType.charAt(0).toUpperCase() + q.quizType.slice(1)}
                </span>
              </div>
            </div>
            <div className="flex space-x-2">
              <a
                href={`/quiz/${q.id}`}
                className="p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 flex items-center justify-center"
                title="Resume Quiz"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
              </a>
              <button
                onClick={() => handleCancelQuiz(q.id)}
                disabled={cancellingId === q.id}
                className="p-2 bg-red-100 text-red-600 rounded-full hover:bg-red-200 flex items-center justify-center"
                title="Cancel Quiz"
              >
                {cancellingId === q.id ? (
                  <span className="h-5 w-5 flex items-center justify-center">...</span>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            </div>
          </li>
        ))}
      </ul>

      {/* Start Quiz Button */}
      <div className="mt-6 flex justify-center">
        <button
          onClick={handleStartQuiz}
          className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-6 rounded-lg flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
          </svg>
          Start New Quiz
        </button>
      </div>
    </section>
  );
}
