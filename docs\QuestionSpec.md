# Common fields (all question types)

| Field         | Type                 | Description                                                       |
| ------------- | -------------------- | ----------------------------------------------------------------- |
| `id`          | `string`             | **Unique** ID for this object inside the batch (UUID or `q-123`). |
| `type`        | `string`             | One of the enum values in **QuestionType**.                       |
| `promptEn`    | `string`             | Question text, English.                                           |
| `promptZh`    | `string`             | Question text, Chinese.                                           |
| `spec`        | `object ?`           | Type-specific payload (shape defined per type below).             |
| `choices`     | `Choice[] ?`         | Only for choice-based types.                                      |
| `answer`      | `object`             | Correct response; shape depends on type.                          |
| `explanation` | `{ textEn, textZh }` | Pedagogical feedback.                                             |

```
type Choice = {
  key: "A" | "B" | "C" | "D" | string;
  textEn: string;
  textZh: string;
  mediaUrl?: string;   // only for image variants
};
```

# 1. MULTIPLE_CHOICE

| Field        | Required            | Notes                              |
| ------------ | ------------------- | ---------------------------------- |
| `choices`    | ✅                   | 2–6 options.                       |
| `spec`       | ⬜ (usually omitted) | Reserved for future shuffle flags. |
| `answer.key` | ✅                   | The letter of the correct option.  |

# 2. MULTIPLE_CHOICE_IMAGE
Same as above plus choices[*].mediaUrl.

```
"choices":[
  {"key":"A","textEn":"Bee","textZh":"蜜蜂","mediaUrl":"https://.../bee.jpg"},
  {"key":"B","textEn":"Ant","textZh":"蚂蚁","mediaUrl":"https://.../ant.jpg"}
]
```

# 3. PICTURE_PROMPT
| Field                 | Required     |
| --------------------- | ------------ |
| `spec.promptImageUrl` | ✅            |
| `choices`             | ✅ (like MCQ) |

```
{
  "id":"q-002",
  "type":"PICTURE_PROMPT",
  "promptEn":"Which habitat best fits this animal?",
  "promptZh":"这种动物最适合的栖息地是哪一个？",
  "spec":{"promptImageUrl":"https://.../polar-bear.jpg"},
  "choices":[ ... ],
  "answer":{"key":"C"},
  "explanation":{...}
}
```

# 4. TRUE_FALSE

| Field        | Required                  |
| ------------ | ------------------------- |
| `choices`    | ⬜ (can be omitted)        |
| `answer.key` | ✅ (`"TRUE"` or `"FALSE"`) |

```
{
  "id":"q-003",
  "type":"TRUE_FALSE",
  "promptEn":"The Earth orbits the Sun.",
  "promptZh":"地球绕太阳运行。",
  "answer":{"key":"TRUE"},
  "explanation":{...}
}
```

# 5. SHORT_ANSWER

| Field                      | Required |
| -------------------------- | -------- |
| `answer.textEn` / `textZh` | ✅        |

```
{
  "id":"q-004",
  "type":"SHORT_ANSWER",
  "promptEn":"What gas do plants absorb for photosynthesis?",
  "promptZh":"植物在光合作用中吸收哪种气体？",
  "answer":{"textEn":"Carbon dioxide","textZh":"二氧化碳"},
  "explanation":{...}
}
```

# 6. LONG_ANSWER

| Field                      | Required | Notes                        |
| -------------------------- | -------- | ---------------------------- |
| `answer.textEn` / `textZh` | ✅        | Model answer paragraph.      |
| `explanation.textEn`       | ✅        | Include rubric / key points. |

```
{
  "id":"q-005",
  "type":"LONG_ANSWER",
  "promptEn":"Explain the process of photosynthesis.",
  "promptZh":"解释光合作用的过程。",
  "answer":{"textEn":"Photosynthesis is the process ...", "textZh":"光合作用是..."},
  "explanation":{"textEn":"Key points: chlorophyll, CO₂ + H₂O, glucose, O₂ release", "textZh":"要点：叶绿素，二氧化碳+水，葡萄糖，氧气释放"}
}
```

# 7. FILL_IN_THE_BLANK

| Field                      | Required |
| -------------------------- | -------- |
| `spec.blanks[]`            | ✅        |
| `answer.textEn` / `textZh` | ✅        |

```
{
  "id":"q-006",
  "type":"FILL_IN_THE_BLANK",
  "promptEn":"The ___ is the largest planet in our solar system.",
  "promptZh":"___ 是太阳系中最大的行星。",
  "spec":{"blanks":[{"index":0,"correct":"Jupiter"}]},
  "answer":{"textEn":"Jupiter","textZh":"木星"},
  "explanation":{...}
}
```

# 8. MATCHING

| Field                     | Required           |
| ------------------------- | ------------------ |
| `spec.pairs[]`            | ✅                  |
| `answer.answerSpec.pairs` | ✅ (same structure) |

```
{
  "id":"q-007",
  "type":"MATCHING",
  "promptEn":"Match the inventors to their inventions.",
  "promptZh":"将发明家与他们的发明配对。",
  "spec":{"pairs":[{"left":"Thomas Edison","right":"Light Bulb"},{"left":"Alexander Graham Bell","right":"Telephone"}]},
  "answer":{"answerSpec":{"pairs":[{"left":"Edison","right":"Light Bulb"}, ...]}},
  "explanation":{...}
}
```

# 9. SEQUENCING
| Field                       | Required |
| --------------------------- | -------- |
| `spec.items[]`              | ✅        |
| `answer.answerSpec.order[]` | ✅        |

```
{
  "id":"q-008",
  "type":"SEQUENCING",
  "promptEn":"Arrange the phases of the water cycle.",
  "promptZh":"排列水循环的阶段。",
  "spec":{"items":["Condensation","Precipitation","Collection","Evaporation"]},
  "answer":{"answerSpec":{"order":[3,0,1,2]}},  // indices of correct order
  "explanation":{...}
}
```

# QuizConfig Snapshot

When a quiz is created, a snapshot of the current quiz configuration is stored in the quiz attempt's metadata. This ensures that the quiz behavior remains consistent even if the global configuration changes.

```json
{
  "metadata": {
    "configSnapshot": {
      "numQuestions": 10,
      "allowTranslate": true,
      "allowHints": true,
      "allowAiTutor": true
    }
  }
}
```

The configuration snapshot controls:

- `numQuestions`: The number of questions in the quiz
- `allowTranslate`: Whether translation is available during the quiz
- `allowHints`: Whether hints are available for difficult questions
- `allowAiTutor`: Whether the AI tutor is available to help students

These settings are applied when the quiz is loaded and affect the UI components and features available to the student during the quiz.

