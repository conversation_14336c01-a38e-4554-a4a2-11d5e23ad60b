import React, { useState } from 'react';
import { CheckIcon, LockIcon } from 'lucide-react';
const subjects = [{
  id: 1,
  name: 'Math',
  icon: '🔢',
  color: 'bg-blue-500',
  completed: 2,
  total: 5,
  unlocked: true
}, {
  id: 2,
  name: 'Science',
  icon: '🧪',
  color: 'bg-green-500',
  completed: 1,
  total: 4,
  unlocked: true
}, {
  id: 3,
  name: 'Chinese',
  icon: '🀄',
  color: 'bg-red-500',
  completed: 0,
  total: 6,
  unlocked: true
}, {
  id: 4,
  name: 'English',
  icon: '📚',
  color: 'bg-purple-500',
  completed: 0,
  total: 5,
  unlocked: false
}];
const units = {
  1: [{
    id: 1,
    name: 'Numbers 1-10',
    completed: true,
    unlocked: true
  }, {
    id: 2,
    name: 'Addition',
    completed: true,
    unlocked: true
  }, {
    id: 3,
    name: 'Subtraction',
    completed: false,
    unlocked: true
  }, {
    id: 4,
    name: 'Shapes',
    completed: false,
    unlocked: false
  }, {
    id: 5,
    name: 'Measurement',
    completed: false,
    unlocked: false
  }],
  2: [{
    id: 1,
    name: 'Plants',
    completed: true,
    unlocked: true
  }, {
    id: 2,
    name: 'Animals',
    completed: false,
    unlocked: true
  }, {
    id: 3,
    name: 'Weather',
    completed: false,
    unlocked: false
  }, {
    id: 4,
    name: 'Human Body',
    completed: false,
    unlocked: false
  }],
  3: [{
    id: 1,
    name: 'Basic Characters',
    completed: false,
    unlocked: true
  }, {
    id: 2,
    name: 'Greetings',
    completed: false,
    unlocked: false
  }, {
    id: 3,
    name: 'Family',
    completed: false,
    unlocked: false
  }, {
    id: 4,
    name: 'Numbers',
    completed: false,
    unlocked: false
  }, {
    id: 5,
    name: 'Colors',
    completed: false,
    unlocked: false
  }, {
    id: 6,
    name: 'Animals',
    completed: false,
    unlocked: false
  }],
  4: [{
    id: 1,
    name: 'Alphabet',
    completed: false,
    unlocked: false
  }, {
    id: 2,
    name: 'Greetings',
    completed: false,
    unlocked: false
  }, {
    id: 3,
    name: 'Colors',
    completed: false,
    unlocked: false
  }, {
    id: 4,
    name: 'Animals',
    completed: false,
    unlocked: false
  }, {
    id: 5,
    name: 'Family',
    completed: false,
    unlocked: false
  }]
};
interface LearningPathProps {
  onStartQuiz: (unit: any) => void;
}
export const LearningPath: React.FC<LearningPathProps> = ({
  onStartQuiz
}) => {
  const [selectedSubject, setSelectedSubject] = useState(1);
  return <main className="flex-1 p-4 bg-gray-100 overflow-y-auto">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-3">Subjects</h2>
        <div className="flex overflow-x-auto pb-2 space-x-3">
          {subjects.map(subject => <button key={subject.id} onClick={() => setSelectedSubject(subject.id)} className={`flex flex-col items-center justify-center p-4 rounded-lg shadow-md min-w-[100px] h-[100px] ${selectedSubject === subject.id ? 'ring-4 ring-yellow-400' : ''} ${subject.unlocked ? subject.color : 'bg-gray-400'}`}>
              <div className="text-3xl mb-1">{subject.icon}</div>
              <div className="text-white font-bold">{subject.name}</div>
              <div className="text-xs text-white/80 mt-1">
                {subject.completed}/{subject.total}
              </div>
            </button>)}
        </div>
      </div>
      <div>
        <h2 className="text-xl font-bold mb-3">Learning Path</h2>
        <div className="bg-white rounded-xl p-4 shadow-md">
          <div className="flex items-center mb-4">
            <div className="text-3xl mr-3">
              {subjects.find(s => s.id === selectedSubject)?.icon}
            </div>
            <div>
              <h3 className="font-bold text-lg">
                {subjects.find(s => s.id === selectedSubject)?.name}
              </h3>
              <div className="text-sm text-gray-500">
                Complete lessons to unlock new content
              </div>
            </div>
          </div>
          <div className="flex flex-col space-y-4">
            {units[selectedSubject]?.map((unit, index) => <div key={unit.id} className={`relative border-2 rounded-lg p-4 ${unit.completed ? 'border-[#0F5FA6] bg-[#04B2D9]/10' : unit.unlocked ? 'border-[#0A8CBF] bg-[#04B2D9]/5' : 'border-gray-300 bg-gray-100'}`}>
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-bold">
                      Unit {index + 1}: {unit.name}
                    </h4>
                    {unit.completed && <span className="text-sm text-[#0F5FA6]">Completed</span>}
                  </div>
                  {unit.unlocked ? <button onClick={() => onStartQuiz({
                id: unit.id,
                name: unit.name,
                subject: subjects.find(s => s.id === selectedSubject)?.name
              }, unit.completed)} className={`px-4 py-2 rounded-lg font-bold ${unit.completed ? 'bg-[#0F5FA6] text-white' : 'bg-[#04B2D9] text-white'}`}>
                      {unit.completed ? 'Practice' : 'Start'}
                    </button> : <div className="bg-gray-300 p-2 rounded-full">
                      <LockIcon size={20} className="text-gray-600" />
                    </div>}
                </div>
                {!unit.completed && unit.unlocked && <div className="mt-2 flex items-center">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div className="bg-[#04B2D9] h-2.5 rounded-full" style={{
                  width: '0%'
                }}></div>
                    </div>
                  </div>}
              </div>)}
          </div>
        </div>
      </div>
    </main>;
};