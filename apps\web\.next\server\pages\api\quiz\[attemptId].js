"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/quiz/[attemptId]";
exports.ids = ["pages/api/quiz/[attemptId]"];
exports.modules = {

/***/ "(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz%2F%5BattemptId%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz%5C%5BattemptId%5D.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz%2F%5BattemptId%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz%5C%5BattemptId%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_quiz_attemptId_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\quiz\\[attemptId].ts */ \"(api-node)/./pages/api/quiz/[attemptId].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_quiz_attemptId_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_quiz_attemptId_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/quiz/[attemptId]\",\n        pathname: \"/api/quiz/[attemptId]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_quiz_attemptId_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz%2F%5BattemptId%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz%5C%5BattemptId%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/../../packages/db/index.ts":
/*!**********************************!*\
  !*** ../../packages/db/index.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _prisma_client__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"prisma\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _prisma_client__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\nconst globalForPrisma = global;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log: [\n        'error'\n    ]\n});\nif (true) globalForPrisma.prisma = prisma;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (prisma);\n// Re-export Prisma types for convenience\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uLi8uLi9wYWNrYWdlcy9kYi9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FDWEYsZ0JBQWdCRSxNQUFNLElBQ3RCLElBQUlILHdEQUFZQSxDQUFDO0lBQ2ZJLEtBQUs7UUFBQztLQUFRO0FBQ2hCLEdBQUc7QUFFTCxJQUFJQyxJQUFxQyxFQUFFSixnQkFBZ0JFLE1BQU0sR0FBR0E7QUFFcEUsaUVBQWVBLE1BQU1BLEVBQUM7QUFFdEIseUNBQXlDO0FBQ1YiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxccGFja2FnZXNcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQcmlzbWFDbGllbnQgfSBmcm9tICdAcHJpc21hL2NsaWVudCc7XG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbCBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcblxuZXhwb3J0IGNvbnN0IHByaXNtYSA9XG4gIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgfHxcbiAgbmV3IFByaXNtYUNsaWVudCh7XG4gICAgbG9nOiBbJ2Vycm9yJ11cbiAgfSk7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID0gcHJpc21hO1xuXG5leHBvcnQgZGVmYXVsdCBwcmlzbWE7XG5cbi8vIFJlLWV4cG9ydCBQcmlzbWEgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgKiBmcm9tICdAcHJpc21hL2NsaWVudCc7XG4iXSwibmFtZXMiOlsiUHJpc21hQ2xpZW50IiwiZ2xvYmFsRm9yUHJpc21hIiwiZ2xvYmFsIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/../../packages/db/index.ts\n");

/***/ }),

/***/ "(api-node)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   hashPin: () => (/* binding */ hashPin),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var argon2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! argon2 */ \"argon2\");\n/* harmony import */ var argon2__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(argon2__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function hashPassword(password) {\n    const salt = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(16).toString('base64');\n    const hash = await argon2__WEBPACK_IMPORTED_MODULE_0__.hash(password + salt, {\n        type: argon2__WEBPACK_IMPORTED_MODULE_0__.argon2id,\n        memoryCost: 65536,\n        timeCost: 3,\n        parallelism: 4\n    });\n    return {\n        hash,\n        salt\n    };\n}\nasync function verifyPassword(password, hash, salt) {\n    return await argon2__WEBPACK_IMPORTED_MODULE_0__.verify(hash, password + salt);\n}\nasync function hashPin(pin, salt) {\n    // Reuse parent's salt if provided, otherwise generate new one\n    const pinSalt = salt || crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(16).toString('base64');\n    const hash = await argon2__WEBPACK_IMPORTED_MODULE_0__.hash(pin + pinSalt, {\n        type: argon2__WEBPACK_IMPORTED_MODULE_0__.argon2id,\n        memoryCost: 65536,\n        timeCost: 3,\n        parallelism: 4\n    });\n    return {\n        hash,\n        salt: pinSalt\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./lib/auth.ts\n");

/***/ }),

/***/ "(api-node)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _quiz_db__WEBPACK_IMPORTED_MODULE_0__.prisma)\n/* harmony export */ });\n/* harmony import */ var _quiz_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @quiz/db */ \"(api-node)/../../packages/db/index.ts\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL2xpYi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNkMiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0c1xcR2l0SHViXFxteS1xdWl6LWFwcFxcYXBwc1xcd2ViXFxsaWJcXHByaXNtYS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBwcmlzbWEgYXMgZGVmYXVsdCB9IGZyb20gJ0BxdWl6L2RiJzsiXSwibmFtZXMiOlsicHJpc21hIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api-node)/./lib/prisma.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"next-auth/providers/credentials\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/prisma */ \"(api-node)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/auth */ \"(api-node)/./lib/auth.ts\");\n\n\n\n // Assuming these are needed for verification\n// Define the configuration and export it\nconst authOptions = {\n    providers: [\n        next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1___default()({\n            name: 'Credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'text'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                },\n                username: {\n                    label: 'Username',\n                    type: 'text'\n                },\n                pin: {\n                    label: 'PIN',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials, req) {\n                if (!credentials) {\n                    return null;\n                }\n                const { email, password, username, pin } = credentials;\n                // Parent login with email/password\n                if (email && password) {\n                    const account = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].account.findUnique({\n                        where: {\n                            email\n                        },\n                        select: {\n                            id: true,\n                            email: true,\n                            name: true,\n                            role: true,\n                            password_hash: true,\n                            salt: true,\n                            status: true\n                        }\n                    });\n                    if (!account || account.status !== 'ACTIVE') {\n                        return null; // Account not found or not active\n                    }\n                    if (!account.password_hash || !account.salt) {\n                        console.error(`Invalid account state: Missing hash/salt for ${email}`);\n                        return null;\n                    }\n                    const isValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyPassword)(password, account.password_hash, account.salt);\n                    if (!isValid) {\n                        return null; // Invalid password\n                    }\n                    // Return user data to be stored in the session\n                    return {\n                        id: account.id.toString(),\n                        name: account.name,\n                        email: account.email,\n                        role: account.role\n                    };\n                }\n                // Child login with username/pin\n                if (username && pin) {\n                    const child = await _lib_prisma__WEBPACK_IMPORTED_MODULE_2__[\"default\"].child.findUnique({\n                        where: {\n                            username\n                        },\n                        select: {\n                            id: true,\n                            name: true,\n                            username: true,\n                            pin_hash: true,\n                            salt: true,\n                            accountId: true,\n                            account: {\n                                select: {\n                                    status: true\n                                }\n                            }\n                        }\n                    });\n                    if (!child || !child.account || child.account.status !== 'ACTIVE') {\n                        return null; // Child or associated account not found or not active\n                    }\n                    if (!child.pin_hash || !child.salt) {\n                        console.error(`Invalid account state: Missing hash/salt for child ${username}`);\n                        return null;\n                    }\n                    const isValid = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyPassword)(pin, child.pin_hash, child.salt);\n                    if (!isValid) {\n                        return null; // Invalid PIN\n                    }\n                    // Return user data to be stored in the session\n                    return {\n                        id: child.id.toString(),\n                        name: child.name,\n                        username: child.username,\n                        role: 'CHILD',\n                        parentId: child.accountId?.toString()\n                    };\n                }\n                return null; // Invalid credentials provided\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            // Add user role and other properties to the token\n            if (user) {\n                token.role = user.role;\n                if (user.parentId) {\n                    token.parentId = user.parentId;\n                }\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            // Add user role and other properties to the session\n            if (token) {\n                session.user.id = token.sub; // sub is the user id from authorize\n                session.user.role = token.role;\n                if (token.parentId) {\n                    session.user.parentId = token.parentId;\n                }\n            }\n            return session;\n        }\n    },\n    // Optional: Add pages for custom sign-in, sign-out, error\n    pages: {\n        signIn: '/login'\n    },\n    // Optional: Configure session\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    // Optional: Add secret for signing and encrypting tokens\n    secret: process.env.NEXTAUTH_SECRET\n};\n// Export the NextAuth handler\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/quiz/[attemptId].ts":
/*!***************************************!*\
  !*** ./pages/api/quiz/[attemptId].ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/prisma */ \"(api-node)/./lib/prisma.ts\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var lodash_shuffle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/shuffle */ \"lodash/shuffle\");\n/* harmony import */ var lodash_shuffle__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_shuffle__WEBPACK_IMPORTED_MODULE_3__);\n\n// import { getSession } from 'next-auth/react'; // Use getServerSession instead\n // Import recommended server-side utility\n // Import your authOptions\n\nasync function handler(req, res) {\n    if (req.method !== 'GET') {\n        return res.status(405).json({\n            message: 'Method Not Allowed'\n        });\n    }\n    // Use getServerSession for server-side session retrieval\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_2__.authOptions);\n    if (!session) {\n        return res.status(401).json({\n            message: 'Unauthorized'\n        });\n    }\n    const { attemptId } = req.query;\n    if (!attemptId || Array.isArray(attemptId)) {\n        return res.status(400).json({\n            message: 'Invalid attemptId'\n        });\n    }\n    try {\n        const quizAttempt = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].quizAttempt.findUnique({\n            where: {\n                id: Number(attemptId)\n            },\n            include: {\n                // Use include instead of select to get all fields\n                studentAnswers: true,\n                child: {\n                    select: {\n                        quizLanguage: true,\n                        menuLanguage: true,\n                        showDualLanguage: true\n                    }\n                }\n            }\n        });\n        if (!quizAttempt) {\n            return res.status(404).json({\n                message: 'Quiz attempt not found'\n            });\n        }\n        // Verify that the logged-in user is the child who owns the quiz attempt\n        // Convert both IDs to strings for proper comparison\n        const sessionUserId = session.user?.id;\n        const quizAttemptChildId = quizAttempt.childId.toString();\n        if (!sessionUserId || sessionUserId !== quizAttemptChildId) {\n            console.log('Access denied - User ID mismatch', {\n                sessionUserId,\n                quizAttemptChildId\n            });\n            return res.status(403).json({\n                message: 'Access denied - You can only view your own quizzes'\n            });\n        }\n        // Check if the quiz is already completed\n        if (quizAttempt.status === 'COMPLETED') {\n            // Return a response indicating that the quiz is already completed\n            return res.status(200).json({\n                quizAttempt,\n                isCompleted: true,\n                message: 'This quiz has already been completed.'\n            });\n        }\n        // Fetch the actual question data based on the stored questionIds\n        const questions = await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].question.findMany({\n            where: {\n                id: {\n                    in: quizAttempt.questionIds.map((id)=>Number(id))\n                }\n            },\n            include: {\n                choices: true,\n                answer: true,\n                explanation: true,\n                unit: {\n                    select: {\n                        unitNumber: true,\n                        topicEn: true,\n                        topicZh: true\n                    }\n                },\n                subject: {\n                    select: {\n                        name: true\n                    }\n                },\n                year: {\n                    select: {\n                        yearNumber: true\n                    }\n                }\n            }\n        });\n        console.log('Found questions:', questions.length);\n        // Get or initialize the shuffled choices map from metadata\n        let shuffledChoicesMap = quizAttempt.metadata?.shuffledChoices || {};\n        let needsMetadataUpdate = false;\n        // Order the questions based on the order in questionIds and transform structure\n        const transformedQuestions = quizAttempt.questionIds // Assign result to transformedQuestions\n        .map((id)=>questions.find((q)=>q.id === Number(id))).filter((q)=>q !== undefined) // Filter out any questions not found (shouldn't happen if questionIds are valid)\n        .map((q)=>{\n            // Create answer structure from the actual answer\n            let answerData;\n            if (q.answer) {\n                // Use type assertion to handle the Answer type\n                const answer = q.answer;\n                console.log('Processing answer for question:', q.id, 'Answer:', JSON.stringify(answer));\n                // Use the actual answer data\n                if (answer.type === 'SINGLE_CHOICE') {\n                    // For single choice, use the key\n                    answerData = answer.key;\n                    console.log('SINGLE_CHOICE answer key:', answer.key);\n                } else if (answer.type === 'SHORT_TEXT') {\n                    // For short text, use the text fields\n                    answerData = {\n                        en: answer.textEn || '',\n                        zh: answer.textZh || '',\n                        ms: answer.textMs || ''\n                    };\n                    console.log('SHORT_TEXT answer:', answerData);\n                } else if (answer.answerSpec) {\n                    // For complex answers, use the answerSpec\n                    answerData = answer.answerSpec;\n                    console.log('Complex answer with answerSpec:', answer.answerSpec);\n                } else {\n                    // Fallback\n                    answerData = {\n                        key: answer.key\n                    };\n                    console.log('Fallback answer with key:', answer.key);\n                }\n            } else {\n                // Fallback if no answer is found\n                answerData = {\n                    en: 'No answer available',\n                    zh: '没有可用的答案'\n                };\n                console.log('No answer found for question:', q.id);\n            }\n            // Create explanation structure from the actual explanation\n            const explanationData = q.explanation ? {\n                en: q.explanation.textEn || 'No explanation available',\n                zh: q.explanation.textZh || '没有可用的解释',\n                ms: q.explanation.textMs || ''\n            } : {\n                en: 'No explanation available',\n                zh: '没有可用的解释'\n            };\n            // Handle shuffling for multiple choice questions\n            let choices = q.choices;\n            const questionIdStr = q.id.toString();\n            // Check if this is a multiple choice question\n            if ((q.type === 'MULTIPLE_CHOICE' || q.type === 'MULTIPLE_CHOICE_IMAGE') && choices && choices.length > 0) {\n                // If we don't have shuffled choices for this question yet, create them\n                if (!shuffledChoicesMap[questionIdStr]) {\n                    console.log(`Shuffling choices for question ${questionIdStr}`);\n                    // Shuffle the choices and store the order\n                    const shuffledChoices = lodash_shuffle__WEBPACK_IMPORTED_MODULE_3___default()([\n                        ...choices\n                    ]);\n                    shuffledChoicesMap[questionIdStr] = shuffledChoices.map((c)=>c.id);\n                    needsMetadataUpdate = true;\n                    choices = shuffledChoices;\n                } else {\n                    console.log(`Using existing shuffled order for question ${questionIdStr}`);\n                    // Use the existing shuffled order\n                    const choiceOrder = shuffledChoicesMap[questionIdStr];\n                    // Sort the choices based on the stored order\n                    choices = choiceOrder.map((id)=>choices.find((c)=>c.id === id)).filter(Boolean);\n                }\n            }\n            // Transform the question structure to match frontend expectations\n            const transformedQuestion = {\n                ...q,\n                // Keep the original fields for debugging\n                promptEn: q.promptEn,\n                promptZh: q.promptZh,\n                originalLanguage: q.originalLanguage || 'EN',\n                prompt: {\n                    en: q.promptEn,\n                    zh: q.promptZh\n                },\n                // Use the actual answer and explanation\n                answer: answerData,\n                explanation: explanationData,\n                topic: q.subTopicEn || (q.unit ? q.unit.topicEn : ''),\n                // Use the shuffled choices if applicable\n                choices: choices\n            };\n            // Log the transformed question for debugging\n            console.log('Transformed question type:', transformedQuestion.type);\n            console.log('Transformed question has choices:', transformedQuestion.choices ? transformedQuestion.choices.length : 0);\n            console.log('Transformed question answer:', transformedQuestion.answer);\n            return transformedQuestion;\n        });\n        // Update the metadata if we shuffled any choices\n        if (needsMetadataUpdate) {\n            console.log('Updating quiz attempt metadata with shuffled choices');\n            await _lib_prisma__WEBPACK_IMPORTED_MODULE_0__[\"default\"].quizAttempt.update({\n                where: {\n                    id: quizAttempt.id\n                },\n                data: {\n                    metadata: {\n                        ...quizAttempt.metadata || {},\n                        shuffledChoices: shuffledChoicesMap\n                    }\n                }\n            });\n        }\n        // Include the child's language preferences in the response\n        const childLanguagePreferences = {\n            quizLanguage: quizAttempt.child?.quizLanguage || 'ZH',\n            menuLanguage: quizAttempt.child?.menuLanguage || 'EN',\n            showDualLanguage: quizAttempt.child?.showDualLanguage || false // Default to false if not set\n        };\n        res.status(200).json({\n            quizAttempt,\n            questions: transformedQuestions,\n            childLanguagePreferences\n        });\n    } catch (error) {\n        console.error('Error fetching quiz attempt:', error);\n        res.status(500).json({\n            message: 'Error fetching quiz attempt'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/quiz/[attemptId].ts\n");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "argon2":
/*!*************************!*\
  !*** external "argon2" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("argon2");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "lodash/shuffle":
/*!*********************************!*\
  !*** external "lodash/shuffle" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("lodash/shuffle");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/credentials":
/*!**************************************************!*\
  !*** external "next-auth/providers/credentials" ***!
  \**************************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/credentials");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fquiz%2F%5BattemptId%5D&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cquiz%5C%5BattemptId%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();