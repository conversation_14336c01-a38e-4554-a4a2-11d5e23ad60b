import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';
import StartQuiz from '../components/StartQuiz';
import * as api from '../lib/api';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
}));

// Mock API functions
jest.mock('../lib/api', () => ({
  getSubjects: jest.fn(),
  getTopics: jest.fn(),
  createQuizAttempt: jest.fn(),
}));

describe('StartQuiz Component', () => {
  // Setup common mocks
  const mockPush = jest.fn();
  const mockRouter = { push: mockPush };
  const mockSession = { data: { user: { id: '1' } }, status: 'authenticated' };

  const mockSubjects = [
    { id: 1, name: 'Mathematics' },
    { id: 2, name: 'Science' },
  ];

  const mockTopics = [
    { id: 1, unitNumber: 1, topicEn: 'Numbers', topicZh: '数字' },
    { id: 2, unitNumber: 2, topicEn: 'Algebra', topicZh: '代数' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSession as jest.Mock).mockReturnValue(mockSession);
    (api.getSubjects as jest.Mock).mockResolvedValue(mockSubjects);
    (api.getTopics as jest.Mock).mockResolvedValue(mockTopics);

    // Mock fetch for API calls
    global.fetch = jest.fn().mockImplementation((url) => {
      if (url.includes('/api/quiz-attempts')) {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ id: 123, questionIds: ['1', '2', '3'] }),
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve([]),
      });
    });
  });

  test('renders mode selection step initially', async () => {
    render(<StartQuiz />);

    // Wait for subjects to load
    await waitFor(() => {
      expect(api.getSubjects).toHaveBeenCalled();
    });

    // Check that mode selection is rendered
    expect(screen.getByText('Select Quiz Mode')).toBeInTheDocument();
    expect(screen.getByText('Mastery Quiz')).toBeInTheDocument();
    expect(screen.getByText('Test Quiz')).toBeInTheDocument();
    expect(screen.getByText('Quick Quiz')).toBeInTheDocument();
  });

  test('selecting Quick Quiz immediately creates a quiz and redirects', async () => {
    render(<StartQuiz />);

    // Wait for subjects to load
    await waitFor(() => {
      expect(api.getSubjects).toHaveBeenCalled();
    });

    // Click Quick Quiz
    fireEvent.click(screen.getByText('Quick Quiz'));

    // Check that fetch was called with correct parameters
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/quiz-attempts', expect.objectContaining({
        method: 'POST',
        body: expect.stringContaining('"mode":"quick"'),
      }));
    });

    // Check that router.push was called with the correct URL
    expect(mockPush).toHaveBeenCalledWith('/quiz/123');
  });

  test('selecting Mastery Quiz advances to subject selection', async () => {
    render(<StartQuiz />);

    // Wait for subjects to load
    await waitFor(() => {
      expect(api.getSubjects).toHaveBeenCalled();
    });

    // Click Mastery Quiz
    fireEvent.click(screen.getByText('Mastery Quiz'));

    // Check that subject selection is rendered
    expect(screen.getByText('Select Subject')).toBeInTheDocument();
    expect(screen.getByText('Mathematics')).toBeInTheDocument();
    expect(screen.getByText('Science')).toBeInTheDocument();
  });

  test('selecting a subject advances to topic selection', async () => {
    render(<StartQuiz />);

    // Wait for subjects to load
    await waitFor(() => {
      expect(api.getSubjects).toHaveBeenCalled();
    });

    // Click Mastery Quiz
    fireEvent.click(screen.getByText('Mastery Quiz'));

    // Click Mathematics
    fireEvent.click(screen.getByText('Mathematics'));

    // Check that topic selection is rendered
    await waitFor(() => {
      expect(screen.getByText('Select Topic')).toBeInTheDocument();
    });

    expect(screen.getByText('Auto-select topics (recommended)')).toBeInTheDocument();
    expect(screen.getByText('Select a specific topic')).toBeInTheDocument();
  });

  test('completing the wizard creates a quiz and redirects', async () => {
    render(<StartQuiz />);

    // Wait for subjects to load
    await waitFor(() => {
      expect(api.getSubjects).toHaveBeenCalled();
    });

    // Click Test Quiz
    fireEvent.click(screen.getByText('Test Quiz'));

    // Click Science
    fireEvent.click(screen.getByText('Science'));

    // Select manual topic selection
    fireEvent.click(screen.getByLabelText('Select a specific topic'));

    // Select a topic from dropdown
    fireEvent.change(screen.getByRole('combobox'), { target: { value: '1' } });

    // Click Continue
    fireEvent.click(screen.getByText('Continue'));

    // Check that confirmation step is rendered
    expect(screen.getByText('Confirm Quiz Settings')).toBeInTheDocument();

    // Click Start Quiz
    fireEvent.click(screen.getByText('Start Quiz'));

    // Check that fetch was called with correct parameters
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/quiz-attempts', expect.objectContaining({
        method: 'POST',
        body: expect.stringContaining('"mode":"test"'),
      }));
    });

    // Check that router.push was called with the correct URL
    expect(mockPush).toHaveBeenCalledWith('/quiz/123');
  });

  test('redirects to login if not authenticated', async () => {
    // Mock unauthenticated session
    (useSession as jest.Mock).mockReturnValue({ data: null, status: 'unauthenticated' });

    render(<StartQuiz />);

    // Check that router.push was called with /login
    expect(mockPush).toHaveBeenCalledWith('/login');
  });
});
