import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateScienceTopicsMs() {
  try {
    console.log('Starting to update Science topics with Malay translations...');
    
    const scienceSubject = await prisma.subject.findUnique({
      where: { name: 'Science' },
    });
    
    if (!scienceSubject) {
      console.error('Science subject not found');
      return;
    }
    
    const topicMsMap = [
      { unitNumber: 1, topicMs: 'Kemahiran Saintifik' },
      { unitNumber: 2, topicMs: 'Manusia' },
      { unitNumber: 3, topicMs: 'Haiwan' },
      { unitNumber: 4, topicMs: 'Tumbuh-Tumbuhan' },
      { unitNumber: 5, topicMs: 'Elektrik' },
      { unitNumber: 6, topicMs: 'Haba' },
      { unitNumber: 7, topicMs: 'Pengaratan' },
      { unitNumber: 8, topicMs: 'Jirim' },
      { unitNumber: 9, topicMs: 'Fasa <PERSON>n da<PERSON>' },
      { unitNumber: 10, topicMs: 'Mesin' },
    ];
    
    for (const { unitNumber, topicMs } of topicMsMap) {
      await prisma.unit.updateMany({
        where: {
          unitNumber,
          subjectId: scienceSubject.id,
        },
        data: {
          topicMs,
        },
      });
      console.log(`Updated Unit ${unitNumber} with Malay topic: ${topicMs}`);
    }
    
    console.log('Update completed successfully!');
  } catch (error) {
    console.error('Error updating Science topics:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateScienceTopicsMs()
  .catch(e => {
    console.error(e);
    process.exit(1);
  });