/**
 * Test script for JSON parsing logic
 * 
 * This script tests the JSON parsing logic from the questionWorker.ts file
 * with the sample response provided by the user.
 */

// Sample response from OpenRouter API
const sampleResponse = {
  "id": "gen-**********-xSskruZpX2S5gRzFejBa",
  "provider": "Google",
  "model": "google/gemini-2.0-flash-exp:free",
  "object": "chat.completion",
  "created": **********,
  "choices": [
    {
      "logprobs": null,
      "finish_reason": "stop",
      "native_finish_reason": "STOP",
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "json\n[\n  {\n    \"id\": \"a1b2c3d4-e5f6-7890-1234-567890abcdef\",\n    \"type\": \"MULTIPLE_CHOICE\",\n    \"promptEn\": \"Which of these animals is a mammal?\",\n    \"promptZh\": \"以下哪种动物是哺乳动物？\",\n    \"choices\": [\n      {\n        \"key\": \"A\",\n        \"textEn\": \"Snake\",\n        \"textZh\": \"蛇\"\n      },\n      {\n        \"key\": \"B\",\n        \"textEn\": \"Fish\",\n        \"textZh\": \"鱼\"\n      },\n      {\n        \"key\": \"C\",\n        \"textEn\": \"Bird\",\n        \"textZh\": \"鸟\"\n      },\n      {\n        \"key\": \"D\",\n        \"textEn\": \"Cat\",\n        \"textZh\": \" 猫\"\n      }\n    ],\n    \"answer\": {\n      \"key\": \"D\"\n    },\n    \"explanation\": {\n      \"textEn\": \"Mammals are warm-blooded animals that have fur or hair and feed their young with milk. Cats are mammals.\",\n      \"textZh\": \"哺乳动物是具有毛发并用乳汁喂养幼崽的温血动物。猫是哺乳动物。\"\n    }\n  }\n]"
      }
    }
  ]
};

// JSON parsing logic from questionWorker.ts
function parseJsonResponse(response: any) {
  try {
    let content = response.choices[0].message.content;
    
    // Log the raw content for debugging
    console.log('Raw content from model:', content.substring(0, 100) + '...');
    
    // Remove any "json" prefix that might be present
    if (content.trim().startsWith('json')) {
      console.log('Detected "json" prefix, removing it...');
      content = content.trim().substring(4).trim();
    }
    
    // Handle case where the model returns markdown code blocks
    if (content.includes('```json') || content.includes('```')) {
      console.log('Detected markdown code block in response, extracting JSON...');
      
      // Extract content between code blocks
      const jsonMatch = content.match(/```(?:json)?\s*([\s\S]*?)```/);
      if (jsonMatch && jsonMatch[1]) {
        content = jsonMatch[1].trim();
        console.log('Extracted JSON from code block');
      } else {
        console.log('Could not extract JSON from code block, trying to parse original content');
      }
    }
    
    // Clean up the content - remove any leading/trailing whitespace and newlines
    content = content.trim();
    
    // Handle the case where there are extra newlines in the JSON
    content = content.replace(/\n\s*\n/g, '\n');
    
    // Try to parse the JSON
    try {
      const questions = JSON.parse(content);
      console.log('Successfully parsed JSON:', questions);
      return questions;
    } catch (parseError) {
      console.error('JSON parse error:', parseError.message);
      console.log('Content that failed to parse (first 100 chars):', content.substring(0, 100));
      
      // Additional fallback: Try to extract anything that looks like a JSON array
      console.log('Initial JSON parsing failed, trying to extract JSON array pattern...');
      const arrayMatch = content.match(/\[\s*\{[\s\S]*\}\s*\]/);
      if (arrayMatch) {
        console.log('Found JSON array pattern, attempting to parse...');
        try {
          const questions = JSON.parse(arrayMatch[0]);
          console.log('Successfully parsed JSON from array pattern:', questions);
          return questions;
        } catch (innerError) {
          console.error('Failed to parse extracted array pattern:', innerError.message);
          
          // Last resort: Try to manually fix common JSON formatting issues
          console.log('Attempting to fix JSON formatting issues...');
          let fixedContent = content
            .replace(/\n/g, ' ')         // Replace newlines with spaces
            .replace(/\s+/g, ' ')        // Normalize whitespace
            .replace(/"\s+:/g, '":')     // Fix "  : issues
            .replace(/:\s+"/g, ':"')     // Fix :  " issues
            .replace(/,\s*}/g, '}')      // Fix trailing commas
            .replace(/,\s*]/g, ']');     // Fix trailing commas in arrays
            
          try {
            const questions = JSON.parse(fixedContent);
            console.log('Successfully parsed JSON after fixing formatting issues:', questions);
            return questions;
          } catch (finalError) {
            console.error('All parsing attempts failed:', finalError.message);
            throw parseError; // Throw the original error
          }
        }
      } else {
        // Re-throw the original error if we couldn't find a JSON array pattern
        throw parseError;
      }
    }
  } catch (error) {
    console.error('Error parsing response:', error);
    throw error;
  }
}

// Test the parsing logic
console.log('Testing JSON parsing logic with sample response...');
try {
  const questions = parseJsonResponse(sampleResponse);
  console.log('Parsing successful!');
  console.log(`Parsed ${Array.isArray(questions) ? questions.length : 0} questions`);
} catch (error) {
  console.error('Parsing failed:', error);
}
