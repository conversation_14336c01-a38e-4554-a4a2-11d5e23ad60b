import { PrismaClient } from '@prisma/client';
import { hashPassword, hashPin } from '../../../apps/web/lib/auth';

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error']
});

async function migratePasswords() {
  console.log('Starting password migration...');

  try {
    // Migrate accounts with plaintext passwords
    const accounts = await prisma.account.findMany({
      where: {
        password: { not: null },
        password_hash: null
      },
      include: {
        children: true
      }
    });

    console.log(`Found ${accounts.length} accounts to migrate`);

    for (const account of accounts) {
      if (!account.password) {
        console.log(`Skipping account ${account.id} - no password to migrate`);
        continue;
      }

      try {
        console.log(`Migrating account ${account.email}...`);

        // Generate hash and salt for account password
        const { hash: passwordHash, salt } = await hashPassword(account.password);
        console.log(`Generated hash and salt for account ${account.email}`);

        // Update account with hashed password
        await prisma.account.update({
          where: { id: account.id },
          data: {
            password_hash: passwordHash,
            salt: salt,
            password: null // Clear plaintext password
          }
        });
        console.log(`Updated account ${account.email} with hashed password`);

        // Update all children's PINs using the same salt
        for (const child of account.children) {
          if (!child.pin) {
            console.log(`Skipping child ${child.id} - no PIN to migrate`);
            continue;
          }

          console.log(`Migrating PIN for child ${child.username}...`);
          const { hash: pinHash } = await hashPin(child.pin, salt);
          await prisma.child.update({
            where: { id: child.id },
            data: {
              pin_hash: pinHash,
              salt: salt,
              pin: null // Clear plaintext PIN
            }
          });
          console.log(`Updated child ${child.username} with hashed PIN`);
        }

        console.log(`Successfully migrated account ${account.email} and ${account.children.length} children`);
      } catch (error) {
        console.error(`Failed to migrate account ${account.email}:`, error);
        throw error; // Re-throw to halt the migration
      }
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('Starting database seed...');

    console.log('Seeding Year table...');
    await prisma.year.createMany({
      data: [
        { yearNumber: 1 },
        { yearNumber: 2 },
        { yearNumber: 3 },
        { yearNumber: 4 },
        { yearNumber: 5 },
        { yearNumber: 6 },
      ],
      skipDuplicates: true,
    });
    console.log('Year table seeded.');

    console.log('Seeding Subject table...');
    await prisma.subject.createMany({
      data: [
        { name: 'Malay' },
        { name: 'Mathematics' },
        { name: 'History' },
        { name: 'Reka Bentuk dan Teknologi' },
        { name: 'Science' },
        { name: 'Chinese' },
        { name: 'English' },
      ],
      skipDuplicates: true,
    });
    console.log('TG_Subject table seeded.');

    console.log('Seeding Unit table for Year 5 Mathematics...');
    const year5 = await prisma.year.findUnique({
      where: { yearNumber: 5 },
    });
    const mathematicsSubject = await prisma.subject.findUnique({
      where: { name: 'Mathematics' },
    });

    if (year5 && mathematicsSubject) {
      const year5MathematicsSyllabus = {
          "year": 5,
          "subject": "Mathematics",
          "syllabus": [
              {
                  "unit": 1,
                  "topic": {
                      "zh": "整数与运算",
                      "en": "Whole Numbers and Operations",
                      "ms": "Nombor Bulat dan Operasi"
                  }
              },
              {
                  "unit": 2,
                  "topic": {
                      "zh": "分数、小数与百分比",
                      "en": "Fractions, Decimals, and Percentages",
                      "ms": "Pecahan, Perpuluhan dan Peratus"
                  }
              },
              {
                  "unit": 3,
                  "topic": {
                      "zh": "货币",
                      "en": "Money",
                      "ms": "Wang"
                  }
              },
              {
                  "unit": 4,
                  "topic": {
                      "zh": "时间与时刻",
                      "en": "Time and Clock",
                      "ms": "Masa dan Waktu"
                  }
              },
              {
                  "unit": 5,
                  "topic": {
                      "zh": "度量衡",
                      "en": "Measurement",
                      "ms": "Panjang, Jisim dan Isi Padu Cecair"
                  }
              },
              {
                  "unit": 6,
                  "topic": {
                      "zh": "空间",
                      "en": "Space",
                      "ms": "Ruang"
                  }
              },
              {
                  "unit": 7,
                  "topic": {
                      "zh": "坐标、比与比例",
                      "en": "Coordinates, Ratios, and Proportions",
                      "ms": "Koordinat, Nisbah dan Kadaraan"
                  }
              },
              {
                  "unit": 8,
                  "topic": {
                      "zh": "数据处理",
                      "en": "Data Handling",
                      "ms": "Pengurusan Data"
                  }
              }
          ]
      };

      for (const unitData of year5MathematicsSyllabus.syllabus) {
        await prisma.unit.upsert({
          where: {
            unitNumber_subjectId_yearId: {
              unitNumber: unitData.unit,
              subjectId: mathematicsSubject.id,
              yearId: year5.id,
            }
          },
          update: {
            topicMs: unitData.topic.ms // Add Malay topic in update
          },
          create: {
            unitNumber: unitData.unit,
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms, // Add Malay topic in create
            yearId: year5.id,
            subjectId: mathematicsSubject.id,
          },
        });
      }
      console.log('Unit table for Year 5 Mathematics seeded.');
    } else {
      console.warn('Could not find Year 5 or Mathematics subject to seed units.');
    }


    console.log('Seeding Unit table for Year 5 Science...');
    const year5Science = await prisma.year.findUnique({
      where: { yearNumber: 5 },
    });
    const scienceSubject = await prisma.subject.findUnique({
      where: { name: 'Science' },
    });

    if (year5Science && scienceSubject) {
      const year5ScienceSyllabus = {
        "year": 5,
        "subject": "Science",
        "syllabus": [
          {
            "unit": 1,
            "topic": {
              "zh": "科学技能",
              "en": "Scientific Skills",
              "ms": "Kemahiran Saintifik"
            }
          },
          {
            "unit": 2,
            "topic": {
              "zh": "人类",
              "en": "Humans",
              "ms": "Manusia"
            }
          },
          {
            "unit": 3,
            "topic": {
              "zh": "动物",
              "en": "Animals",
              "ms": "Haiwan"
            }
          },
          {
            "unit": 4,
            "topic": {
              "zh": "植物",
              "en": "Plants",
              "ms": "Tumbuh-Tumbuhan"
            }
          },
          {
            "unit": 5,
            "topic": {
              "zh": "电",
              "en": "Electricity",
              "ms": "Elektrik"
            }
          },
          {
            "unit": 6,
            "topic": {
              "zh": "热",
              "en": "Heat",
              "ms": "Haba"
            }
          },
          {
            "unit": 7,
            "topic": {
              "zh": "生锈",
              "en": "Rusting",
              "ms": "Pengaratan"
            }
          },
          {
            "unit": 8,
            "topic": {
              "zh": "物质",
              "en": "Matter",
              "ms": "Jirim"
            }
          },
          {
            "unit": 9,
            "topic": {
              "zh": "月相和星座",
              "en": "Moon Phases and Constellations",
              "ms": "Fasa Bulan dan Buruj"
            }
          },
          {
            "unit": 10,
            "topic": {
              "zh": "机械",
              "en": "Machines",
              "ms": "Mesin"
            }
          }
        ]
      };

      for (const unitData of year5ScienceSyllabus.syllabus) {
        await prisma.unit.upsert({
          where: {
            unitNumber_subjectId_yearId: {
              unitNumber: unitData.unit,
              subjectId: scienceSubject.id,
              yearId: year5Science.id,
            }
          },
          update: {
            topicMs: unitData.topic.ms // Add Malay topic in update
          },
          create: {
            unitNumber: unitData.unit,
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms, // Add Malay topic in create
            yearId: year5Science.id,
            subjectId: scienceSubject.id,
          },
        });
      }
      console.log('Unit table for Year 5 Science seeded.');
    } else {
      console.warn('Could not find Year 5 or Science subject to seed units.');
    }

    console.log('Seeding Unit table for Year 5 History...');
    const year5History = await prisma.year.findUnique({
      where: { yearNumber: 5 },
    });
    const historySubject = await prisma.subject.findUnique({
      where: { name: 'History' },
    });

    if (year5History && historySubject) {
      const year5HistorySyllabus = {
        "year": 5,
        "subject": "History",
        "syllabus": [
          {
            "unit": 1,
            "topic": {
              "zh": "君主立宪是国家的保护伞",
              "en": "The Monarchy: Umbrella of the Nation",
              "ms": "Institusi Raja Payung Negara"
            }
          },
          {
            "unit": 2,
            "topic": {
              "zh": "马来西亚的伊斯兰教",
              "en": "Islam in Malaysia",
              "ms": "Agama Islam di Malaysia"
            }
          },
          {
            "unit": 3,
            "topic": {
              "zh": "马来语是我们的遗产",
              "en": "Bahasa Melayu: Our Heritage",
              "ms": "Bahasa Melayu Warisan Kita"
            }
          },
          {
            "unit": 4,
            "topic": {
              "zh": "国家主权的挑战",
              "en": "Challenges to National Sovereignty",
              "ms": "Kedaulatan Negara Dicabar"
            }
          },
          {
            "unit": 5,
            "topic": {
              "zh": "地方领袖的兴起与抗争",
              "en": "Rise and Struggle Against Colonialists",
              "ms": "Bangkit Berjuang Penjajah Ditentang"
            }
          },
          {
            "unit": 6,
            "topic": {
              "zh": "国家独立的历史",
              "en": "The History of Independence",
              "ms": "Sejarah Kemerdekaan"
            }
          },
          {
            "unit": 7,
            "topic": {
              "zh": "国家元首是国家主权的核心",
              "en": "The Yang Di-Pertuan Agong: Core of National Sovereignty",
              "ms": "Yang Di-Pertuan Agong Teras Kedaulatan Negara"
            }
          },
          {
            "unit": 8,
            "topic": {
              "zh": "马来西亚的国徽",
              "en": "The National Emblem (Jata Negara)",
              "ms": "Jata Negara"
            }
          },
          {
            "unit": 9,
            "topic": {
              "zh": "马来西亚的国旗",
              "en": "The National Flag of Malaysia",
              "ms": "Bendera Kebangsaan Malaysia"
            }
          },
          {
            "unit": 10,
            "topic": {
              "zh": "马来西亚的国歌",
              "en": "The National Anthem of Malaysia",
              "ms": "Lagu Kebangsaan Malaysia"
            }
          },
          {
            "unit": 11,
            "topic": {
              "zh": "马来语是国语",
              "en": "The National Language (Bahasa Kebangsaan)",
              "ms": "Bahasa Kebangsaan"
            }
          },
          {
            "unit": 12,
            "topic": {
              "zh": "我国的国花大红花",
              "en": "The National Flower (Bunga Raya)",
              "ms": "Bunga Raya Bunga Kebangsaan"
            }
          }
        ]
      };

      for (const unitData of year5HistorySyllabus.syllabus) {
        await prisma.unit.upsert({
          where: {
            unitNumber_subjectId_yearId: {
              unitNumber: unitData.unit,
              subjectId: historySubject.id,
              yearId: year5History.id,
            }
          },
          update: {
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms
          },
          create: {
            unitNumber: unitData.unit,
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms,
            yearId: year5History.id,
            subjectId: historySubject.id,
          },
        });
      }
      console.log('Unit table for Year 5 History seeded.');
    } else {
      console.warn('Could not find Year 5 or History subject to seed units.');
    }

    console.log('Seeding Unit table for Year 5 Reka Bentuk dan Teknologi...');
    const year5RBT = await prisma.year.findUnique({
      where: { yearNumber: 5 },
    });
    const rbtSubject = await prisma.subject.findUnique({
      where: { name: 'Reka Bentuk dan Teknologi' },
    });

    if (year5RBT && rbtSubject) {
      const year5RBTSyllabus = {
        "year": 5,
        "subject": "Reka Bentuk dan Teknologi",
        "syllabus": [
          {
            "unit": 1,
            "topic": {
              "zh": "一针一线的温暖",
              "en": "Household Technology",
              "ms": "Teknologi Rumah Tangga"
            }
          },
          {
            "unit": 2,
            "topic": {
              "zh": "无限的可更新能源",
              "en": "Application of Engineering Design Technology",
              "ms": "Aplikasi Reka Bentuk Teknologi Kejuruteraan"
            }
          },
          {
            "unit": 3,
            "topic": {
              "zh": "我是小创客",
              "en": "Programming Design",
              "ms": "Reka Bentuk Pengaturcaraan"
            }
          },
          {
            "unit": 4,
            "topic": {
              "zh": "都市里的绿意",
              "en": "Agricultural Technology",
              "ms": "Teknologi Pertanian"
            }
          }
        ]
      };

      for (const unitData of year5RBTSyllabus.syllabus) {
        await prisma.unit.upsert({
          where: {
            unitNumber_subjectId_yearId: {
              unitNumber: unitData.unit,
              subjectId: rbtSubject.id,
              yearId: year5RBT.id,
            }
          },
          update: {
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms
          },
          create: {
            unitNumber: unitData.unit,
            topicZh: unitData.topic.zh,
            topicEn: unitData.topic.en,
            topicMs: unitData.topic.ms,
            yearId: year5RBT.id,
            subjectId: rbtSubject.id,
          },
        });
      }
      console.log('Unit table for Year 5 Reka Bentuk dan Teknologi seeded.');
    } else {
      console.warn('Could not find Year 5 or Reka Bentuk dan Teknologi subject to seed units.');
    }

    console.log('Seeding default settings...');
    await prisma.setting.upsert({
      where: { key: 'quizQuestionLimit' },
      update: {}, // No update needed if it exists, just ensure it's there
      create: {
        key: 'quizQuestionLimit',
        value: '5', // Default limit
      },
    });
    console.log('Default settings seeded.');

    console.log('Seeding QuizConfig table...');
    // Create default config for MASTERY mode
    await prisma.quizConfig.upsert({
      where: { mode: 'MASTERY' },
      update: {}, // No update needed if it exists
      create: {
        mode: 'MASTERY',
        numQuestions: 10,
        questionTypes: ['MULTIPLE_CHOICE', 'TRUE_FALSE', 'MATCHING', 'SEQUENCING'],
        allowTranslate: true,
        allowHints: true,
        allowAiTutor: true,
        reviewMissedQuestions: true,
      },
    });

    // Create default config for TEST mode
    await prisma.quizConfig.upsert({
      where: { mode: 'TEST' },
      update: {}, // No update needed if it exists
      create: {
        mode: 'TEST',
        numQuestions: 20,
        questionTypes: ['MULTIPLE_CHOICE', 'TRUE_FALSE', 'MATCHING', 'SEQUENCING', 'SHORT_ANSWER'],
        allowTranslate: false,
        allowHints: false,
        allowAiTutor: false,
        reviewMissedQuestions: true,
      },
    });
    console.log('QuizConfig table seeded.');


    await migratePasswords();
    console.log('Database seed completed successfully!');
  } catch (error) {
    console.error('Database seed failed:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Fatal error:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
