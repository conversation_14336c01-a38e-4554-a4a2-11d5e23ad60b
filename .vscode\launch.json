{
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Next: Dev (Launch)",
        "type": "pwa-node",
        "request": "launch",
        "runtimeExecutable": "npm",
        "runtimeArgs": ["run", "dev"],
        "port": 9229,
        "cwd": "${workspaceFolder}",
        "console": "integratedTerminal",
        "internalConsoleOptions": "neverOpen",
        // If you need to set NODE_OPTIONS for inspection:
        "env": { "NODE_OPTIONS": "--inspect-brk=9229" },
        "skipFiles": ["<node_internals>/**"]
      },
      {
        "name": "Next: Attach (Client)",
        "type": "pwa-chrome",
        "request": "attach",
        "port": 9229,
        "webRoot": "${workspaceFolder}",
        "skipFiles": ["node_modules/**/*.js"]
      }
    ]
  }
  