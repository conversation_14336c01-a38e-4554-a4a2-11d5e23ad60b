import { QuestionType, Language } from '@prisma/client';

// Define the expected request structure
interface GenerateQuestionsRequest {
  yearId: number;
  subjectId: number;
  unitId?: number;
  numQuestions?: number;
  questionTypes: QuestionType[];
  provider: string;
  model: string;
  language?: Language;
  tpDistribution?: number[];
}

// Mock fetch for API integration tests
const originalFetch = global.fetch;

describe('Generate Questions API Payload Validation', () => {
  // Test valid payload
  test('valid payload structure', () => {
    const validPayload: GenerateQuestionsRequest = {
      yearId: 1,
      subjectId: 2,
      unitId: 3,
      numQuestions: 5,
      questionTypes: [QuestionType.MULTIPLE_CHOICE, QuestionType.TRUE_FALSE],
      provider: 'openrouter',
      model: 'anthropic/claude-3-opus:beta',
      language: Language.ZH,
      tpDistribution: [1, 2, 3, 4, 5],
    };

    // Verify all required fields are present
    expect(validPayload).toHaveProperty('yearId');
    expect(validPayload).toHaveProperty('subjectId');
    expect(validPayload).toHaveProperty('questionTypes');
    expect(validPayload).toHaveProperty('model');

    // Verify questionTypes is an array and not empty
    expect(Array.isArray(validPayload.questionTypes)).toBe(true);
    expect(validPayload.questionTypes.length).toBeGreaterThan(0);

    // Verify numQuestions is within limits
    expect(validPayload.numQuestions).toBeLessThanOrEqual(100);
  });

  // Test missing required fields
  test('missing required fields should be detected', () => {
    const invalidPayload = {
      // Missing yearId
      subjectId: 2,
      questionTypes: [QuestionType.MULTIPLE_CHOICE],
      model: 'anthropic/claude-3-opus:beta',
    };

    // Check for missing yearId
    expect(invalidPayload).not.toHaveProperty('yearId');
  });

  // Test numQuestions exceeding maximum
  test('numQuestions exceeding maximum should be detected', () => {
    const invalidPayload: GenerateQuestionsRequest = {
      yearId: 1,
      subjectId: 2,
      questionTypes: [QuestionType.MULTIPLE_CHOICE],
      provider: 'openrouter',
      model: 'anthropic/claude-3-opus:beta',
      numQuestions: 101, // Exceeds maximum of 100
    };

    // Verify numQuestions exceeds limit
    expect(invalidPayload.numQuestions).toBeGreaterThan(100);
  });

  // Test empty questionTypes array
  test('empty questionTypes array should be detected', () => {
    const invalidPayload: GenerateQuestionsRequest = {
      yearId: 1,
      subjectId: 2,
      questionTypes: [], // Empty array
      provider: 'openrouter',
      model: 'anthropic/claude-3-opus:beta',
    };

    // Verify questionTypes is empty
    expect(invalidPayload.questionTypes.length).toBe(0);
  });

  // Test default values
  test('default values should be applied when optional fields are missing', () => {
    // Function to apply defaults (simulating API behavior)
    const applyDefaults = (payload: Partial<GenerateQuestionsRequest>): GenerateQuestionsRequest => {
      return {
        ...payload,
        numQuestions: payload.numQuestions ?? 10,
        provider: payload.provider ?? 'openrouter',
        language: payload.language ?? Language.ZH,
      } as GenerateQuestionsRequest;
    };

    const minimalPayload = {
      yearId: 1,
      subjectId: 2,
      questionTypes: [QuestionType.MULTIPLE_CHOICE],
      model: 'anthropic/claude-3-opus:beta',
    };

    const payloadWithDefaults = applyDefaults(minimalPayload);

    // Verify defaults are applied
    expect(payloadWithDefaults.numQuestions).toBe(10);
    expect(payloadWithDefaults.provider).toBe('openrouter');
    expect(payloadWithDefaults.language).toBe(Language.ZH);
  });

  // Test expected queue file structure
  test('queue file structure should be correct', () => {
    const batchId = 123;
    const provider = 'openrouter';
    const language = Language.ZH;
    const tpDistribution = [1, 2, 3, 4, 5];

    // Create expected queue file content
    const queueFileContent = JSON.stringify({
      batchId,
      provider,
      language,
      tpDistribution
    });

    // Parse the content back to verify structure
    const parsedContent = JSON.parse(queueFileContent);

    // Verify structure
    expect(parsedContent).toHaveProperty('batchId', batchId);
    expect(parsedContent).toHaveProperty('provider', provider);
    expect(parsedContent).toHaveProperty('language', language);
    expect(parsedContent).toHaveProperty('tpDistribution', tpDistribution);
  });
});

describe('Generate Questions API Integration', () => {
  beforeEach(() => {
    // Mock fetch before each test
    global.fetch = jest.fn();
  });

  afterEach(() => {
    // Restore original fetch after each test
    global.fetch = originalFetch;
  });

  test('API should return success response with batch ID', async () => {
    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({ ok: true, batchId: 123 }),
    });

    // Valid request payload
    const payload: GenerateQuestionsRequest = {
      yearId: 1,
      subjectId: 2,
      unitId: 3,
      numQuestions: 5,
      questionTypes: [QuestionType.MULTIPLE_CHOICE, QuestionType.TRUE_FALSE],
      provider: 'openrouter',
      model: 'anthropic/claude-3-opus:beta',
      language: Language.ZH,
      tpDistribution: [1, 2, 3, 4, 5],
    };

    // Call the API
    const response = await fetch('/api/admin/generate-questions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    // Verify fetch was called with correct parameters
    expect(global.fetch).toHaveBeenCalledWith(
      '/api/admin/generate-questions',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
        body: expect.any(String),
      })
    );

    // Verify the payload was correctly serialized
    const calledWith = (global.fetch as jest.Mock).mock.calls[0][1];
    const sentPayload = JSON.parse(calledWith.body);
    expect(sentPayload).toEqual(payload);

    // Verify response
    expect(response.ok).toBe(true);
    const data = await response.json();
    expect(data).toEqual({ ok: true, batchId: 123 });
  });

  test('API should handle error responses', async () => {
    // Mock error API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 400,
      json: async () => ({ message: 'Missing required fields' }),
    });

    // Invalid request payload (missing required fields)
    const payload = {
      // Missing yearId
      subjectId: 2,
      questionTypes: [QuestionType.MULTIPLE_CHOICE],
      model: 'anthropic/claude-3-opus:beta',
    };

    // Call the API
    const response = await fetch('/api/admin/generate-questions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    // Verify response
    expect(response.ok).toBe(false);
    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data).toEqual({ message: 'Missing required fields' });
  });

  test('API should handle server errors', async () => {
    // Mock server error response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      status: 500,
      json: async () => ({ message: 'Internal server error', error: 'Database error' }),
    });

    // Valid request payload
    const payload: GenerateQuestionsRequest = {
      yearId: 1,
      subjectId: 2,
      questionTypes: [QuestionType.MULTIPLE_CHOICE],
      provider: 'openrouter',
      model: 'anthropic/claude-3-opus:beta',
    };

    // Call the API
    const response = await fetch('/api/admin/generate-questions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    // Verify response
    expect(response.ok).toBe(false);
    expect(response.status).toBe(500);
    const data = await response.json();
    expect(data).toEqual({ message: 'Internal server error', error: 'Database error' });
  });
});
