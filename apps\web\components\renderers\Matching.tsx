import React, { useState, useEffect } from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';
import { Language } from '@prisma/client';

const Matching: React.FC<BaseRendererProps<'MATCHING'>> = ({
  promptEn,
  promptZh,
  promptMs,
  originalPrompt,
  originalLanguage,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  onMouseUp
}) => {
  const [matches, setMatches] = useState<Record<number, number>>({});
  const [selectedLeft, setSelectedLeft] = useState<number | null>(null);

  // Determine which language to use based on the question's original language
  const isChineseOriginal = originalLanguage === Language.ZH || originalPrompt === promptZh;
  const isMalayOriginal = originalLanguage === Language.MS || (originalPrompt !== promptZh && originalPrompt !== promptEn);
  const isEnglishOriginal = originalLanguage === Language.EN || (!isChineseOriginal && !isMalayOriginal);

  // Prepare the pairs for display with appropriate language
  const leftItems = spec?.pairs.map((pair, index) => ({
    id: index,
    text: isChineseOriginal && pair.leftZh ? pair.leftZh :
          isMalayOriginal && pair.leftMs ? pair.leftMs :
          pair.left
  })) || [];

  // Add original index to rightItems for validation
  let rightItems = spec?.pairs.map((pair, index) => ({
    id: index,
    text: isChineseOriginal && pair.rightZh ? pair.rightZh :
          isMalayOriginal && pair.rightMs ? pair.rightMs :
          pair.right || '',
    image: pair.rightImage,
    originalIndex: index
  })) || [];

  // Shuffle the right items if specified in the spec
  useEffect(() => {
    if (spec?.shuffleRight) {
      // Fisher-Yates shuffle
      const shuffled = [...rightItems];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      rightItems = shuffled;
    }
    // eslint-disable-next-line
  }, [spec]);

  // Shuffle the right items if specified in the spec
  useEffect(() => {
    if (spec?.shuffleRight) {
      // Simple Fisher-Yates shuffle
      const shuffled = [...rightItems];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      rightItems = shuffled;
    }
  }, [spec]);

  const handleLeftClick = (id: number) => {
    // Toggle selection - if clicking the same item, deselect it
    setSelectedLeft(prev => prev === id ? null : id);
  };

  const handleRightClick = (id: number) => {
    if (selectedLeft !== null) {
      setMatches({
        ...matches,
        [selectedLeft]: id
      });
      setSelectedLeft(null);
    }
  };

  const isLeftMatched = (id: number) => {
    return id in matches;
  };

  const isRightMatched = (id: number) => {
    return Object.values(matches).includes(id);
  };

  // Check if a match is correct (compare the selected right index to the correct index)
  const isMatchCorrect = (leftId: number, rightId: number) => {
    if (rightId == null) return false;
    // Find the original index of the selected right item
    const rightItem = rightItems.find((item) => item.id === rightId);
    if (!rightItem) return false;
    return rightItem.originalIndex === leftId;
  };

  // Check if a matched right item is correct for the current left selection
  const isSelectedMatchCorrect = (rightId: number) => {
    if (selectedLeft === null) return false;
    return isMatchCorrect(selectedLeft, rightId);
  };

  // Get the correct right ID for a given left ID
  const getCorrectRightId = (leftId: number) => {
    const leftItem = leftItems[leftId];
    if (!leftItem) return null;
    
    return rightItems.findIndex(rightItem => 
      rightItem.text === spec?.pairs[leftId]?.right ||
      rightItem.image === spec?.pairs[leftId]?.rightImage
    );
  };

  // Mini score: count correct and wrong
  const total = leftItems.length;
  let correct = 0, wrong = 0;
  Object.entries(matches).forEach(([left, right]) => {
    if (isMatchCorrect(Number(left), right as any)) correct++;
    else wrong++;
  });

  // Refs to track item positions for drawing lines
  const leftItemRefs = React.useRef<Array<HTMLButtonElement | null>>([]);
  const rightItemRefs = React.useRef<Array<HTMLButtonElement | null>>([]);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Update refs array when items change
  React.useEffect(() => {
    leftItemRefs.current = leftItemRefs.current.slice(0, leftItems.length);
    rightItemRefs.current = rightItemRefs.current.slice(0, rightItems.length);
  }, [leftItems.length, rightItems.length]);
  
  // Create stable ref callbacks
  const setLeftItemRef = React.useCallback((index: number) => (el: HTMLButtonElement | null) => {
    leftItemRefs.current[index] = el;
  }, []);
  
  const setRightItemRef = React.useCallback((index: number) => (el: HTMLButtonElement | null) => {
    rightItemRefs.current[index] = el;
  }, []);

  // Function to get coordinates for drawing lines
  const getLineCoordinates = () => {
    if (!containerRef.current) return [];
    
    const containerRect = containerRef.current.getBoundingClientRect();
    const coordinates: Array<{
      x1: number;
      y1: number;
      x2: number;
      y2: number;
      isCorrect: boolean;
    }> = [];

    Object.entries(matches).forEach(([leftId, rightId]) => {
      const leftIndex = parseInt(leftId, 10);
      const rightIndex = rightItems.findIndex(item => item.id === rightId);
      
      if (leftItemRefs.current[leftIndex] && rightItemRefs.current[rightIndex]) {
        const leftRect = leftItemRefs.current[leftIndex]!.getBoundingClientRect();
        const rightRect = rightItemRefs.current[rightIndex]!.getBoundingClientRect();
        
        coordinates.push({
          x1: leftRect.right - containerRect.left,
          y1: leftRect.top + leftRect.height / 2 - containerRect.top,
          x2: rightRect.left - containerRect.left,
          y2: rightRect.top + rightRect.height / 2 - containerRect.top,
          isCorrect: isMatchCorrect(leftIndex, rightId)
        });
      }
    });

    return coordinates;
  };

  const lineCoordinates = getLineCoordinates();

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div ref={containerRef} className="relative">
        {/* SVG for drawing lines */}
        <svg className="absolute top-0 left-0 w-full h-full pointer-events-none z-0">
          {lineCoordinates.map((coord, index) => (
            <line
              key={index}
              x1={coord.x1}
              y1={coord.y1}
              x2={coord.x2}
              y2={coord.y2}
              stroke={coord.isCorrect ? '#4ade80' : '#f87171'}
              strokeWidth="2"
              strokeDasharray={coord.isCorrect ? '0' : '5,5'}
              className="transition-all duration-300"
            />
          ))}
        </svg>
      <div className="flex justify-between items-start mb-6">
        <div className="text-lg font-medium text-black">
          {originalPrompt || (displayLanguage === 'en' ? promptEn : promptZh)}
        </div>
        {/* Mini score */}
        <div className="flex items-center space-x-2 text-base select-none">
          <span className="flex items-center">
            <svg className="w-5 h-5 text-green-500 mr-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" /></svg>
            {correct}
          </span>
          <span className="flex items-center">
            <svg className="w-5 h-5 text-red-500 mr-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
            {wrong}
          </span>
        </div>
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-8">
        {/* Left column */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-gray-600 mb-2">
            {displayLanguage === 'en' ? 'Match these' : 
             displayLanguage === 'ms' ? 'Padankan ini' : '匹配这些'}
          </h3>
          {leftItems.map((item) => (
            <button
              key={`left-${item.id}`}
              ref={setLeftItemRef(item.id)}
              onClick={() => handleLeftClick(item.id)}
              className={`w-full text-left rounded-lg px-4 py-3 border-2 text-gray-800 ${
                selectedLeft === item.id
                  ? 'border-blue-500 bg-blue-50'
                  : isLeftMatched(item.id)
                    ? isMatchCorrect(item.id, matches[item.id])
                      ? 'border-green-300 bg-green-50'
                      : 'border-red-300 bg-red-50'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
              } transition-colors`}
              disabled={isLeftMatched(item.id)}
            >
              <div className="flex justify-between items-center">
                <span className="text-gray-800">{item.text}</span>
                {isLeftMatched(item.id) && (
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    isMatchCorrect(item.id, matches[item.id])
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {isMatchCorrect(item.id, matches[item.id]) ? '✓' : '✗'}
                  </span>
                )}
              </div>
            </button>
          ))}
        </div>

        {/* Right column */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-gray-600 mb-2">
            {displayLanguage === 'en' ? 'With these' : 
             displayLanguage === 'ms' ? 'Dengan ini' : '与这些匹配'}
          </h3>
          {rightItems.map((item) => {
            const matchedLeft = Object.entries(matches).find(([leftId, rightId]) => Number(rightId) === item.id);
            const isMatched = Boolean(matchedLeft);
            const isCorrect = isMatched && isMatchCorrect(Number(matchedLeft?.[0]), item.id);
            return (
              <button
                key={`right-${item.id}`}
                ref={setRightItemRef(item.id)}
                onClick={() => handleRightClick(item.id)}
                className={`w-full text-left rounded-lg px-4 py-3 border-2 text-gray-800 ${
                  isMatched
                    ? isCorrect
                      ? 'border-green-300 bg-green-50'
                      : 'border-red-300 bg-red-50'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                } transition-colors`}
                disabled={isMatched}
              >
                <div className="flex items-center">
                  {item.image && (
                    <img
                      src={item.image}
                      alt=""
                      className="w-10 h-10 object-cover rounded-lg mr-3"
                    />
                  )}
                  <span className="text-gray-800">{item.text}</span>
                </div>
              </button>
            );
          })}
        </div>
      </div>
      </div>
    </RendererWrapper>
  );
};

export default Matching;
