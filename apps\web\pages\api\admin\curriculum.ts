import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const years = await prisma.year.findMany({
      include: {
        units: {
          include: {
            subject: true,
          },
        },
      },
    });

    // Restructure the data to match the frontend's expected format
    const structuredData = years.map(year => {
      const subjectMap = new Map();
      
      // Group units by subject
      year.units.forEach(unit => {
        if (!subjectMap.has(unit.subject.id)) {
          subjectMap.set(unit.subject.id, {
            id: unit.subject.id,
            name: unit.subject.name,
            units: []
          });
        }
        subjectMap.get(unit.subject.id).units.push({
          id: unit.id,
          unitNumber: unit.unitNumber,
          topicEn: unit.topicEn,
          topicZh: unit.topicZh,
        });
      });

      return {
        id: year.id,
        yearNumber: year.yearNumber,
        subjects: Array.from(subjectMap.values())
      };
    });

    res.status(200).json(structuredData);
  } catch (error) {
    console.error('Error fetching curriculum:', error);
    res.status(500).json({ message: 'Error fetching curriculum data' });
  }
}