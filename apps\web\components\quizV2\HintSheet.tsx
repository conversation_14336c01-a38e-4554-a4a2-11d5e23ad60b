import React from 'react';
import { useQuizV2 } from './QuizV2Provider';
import ReactMarkdown from 'react-markdown';

export default function HintSheet() {
  const { explanation, setExplanation } = useQuizV2();

  if (!explanation) return null;

  return (
    <div className="fixed inset-x-0 bottom-0 bg-white rounded-t-xl shadow-2xl p-4 max-h-2/5 overflow-y-auto text-black animate-slideUp z-40">
      <div className="flex justify-between items-center mb-2">
        <h4 className="font-bold">Hint</h4>
        <button
          onClick={() => setExplanation(null)}
          className="text-gray-500 hover:text-gray-700"
          aria-label="Close hint"
        >
          ✕
        </button>
      </div>
      <ReactMarkdown>{explanation}</ReactMarkdown>
      {/* "Got it" button removed as requested */}
    </div>
  );
}
