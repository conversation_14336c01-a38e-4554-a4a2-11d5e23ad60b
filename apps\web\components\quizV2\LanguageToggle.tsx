import React from 'react';
import { useQuizV2 } from './QuizV2Provider';

export default function LanguageToggle() {
  const { displayLanguage, toggleLanguage, allowTranslate } = useQuizV2();

  // If translation is not allowed, don't show the toggle
  if (!allowTranslate) {
    return null;
  }

  // Get the current language label
  const getCurrentLanguageLabel = () => {
    switch (displayLanguage) {
      case 'en': return 'EN';
      case 'zh': return '中文';
      case 'ms': return 'MS';
      default: return 'EN';
    }
  };

  return (
    <div className="flex items-center justify-end">
      <button
        className="px-3 py-1 rounded-full text-sm font-medium bg-blue-500 text-white"
        onClick={toggleLanguage}
        aria-label="Toggle language"
      >
        {getCurrentLanguageLabel()}
      </button>
    </div>
  );
}
