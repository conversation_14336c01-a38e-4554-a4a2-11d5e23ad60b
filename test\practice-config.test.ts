import { createMocks } from 'node-mocks-http';
import handler from '../pages/api/quiz/create-practice';
import prisma from '../lib/prisma';
import { getServerSession } from 'next-auth/next';

// Mock the next-auth session
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock the Prisma client
jest.mock('../lib/prisma', () => ({
  quizConfig: {
    findUnique: jest.fn(),
  },
  question: {
    findMany: jest.fn(),
  },
  quizAttempt: {
    create: jest.fn(),
  },
}));

// Mock lodash shuffle
jest.mock('lodash/shuffle', () => {
  return (arr: any[]) => arr; // Just return the array unchanged for predictable tests
});

describe('Create Practice API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Mock child session
  const mockChildSession = {
    user: {
      id: '1',
      name: 'Test Child',
      email: '<EMAIL>',
      role: 'CHILD',
    },
  };

  // Mock non-child session
  const mockNonChildSession = {
    user: {
      id: '2',
      name: 'Parent User',
      email: '<EMAIL>',
      role: 'PARENT',
    },
  };

  it('should return 401 if not authenticated', async () => {
    // Mock unauthenticated session
    (getServerSession as jest.Mock).mockResolvedValueOnce(null);

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        subjectId: 1,
        yearId: 1,
        unitId: 1,
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Unauthorized' });
  });

  it('should return 403 if not a child', async () => {
    // Mock non-child session
    (getServerSession as jest.Mock).mockResolvedValueOnce(mockNonChildSession);

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        subjectId: 1,
        yearId: 1,
        unitId: 1,
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(403);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Forbidden - Child access required' });
  });

  it('should create a practice quiz with config settings', async () => {
    // Mock child session
    (getServerSession as jest.Mock).mockResolvedValueOnce(mockChildSession);

    // Mock quiz config
    const mockConfig = {
      id: 1,
      mode: 'MASTERY',
      numQuestions: 5,
      questionTypes: ['MULTIPLE_CHOICE'],
      allowTranslate: false,
      allowHints: true,
      allowAiTutor: true,
    };
    (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce(mockConfig);

    // Mock questions
    const mockQuestions = [
      { id: 1, type: 'MULTIPLE_CHOICE' },
      { id: 2, type: 'MULTIPLE_CHOICE' },
      { id: 3, type: 'MULTIPLE_CHOICE' },
      { id: 4, type: 'MULTIPLE_CHOICE' },
      { id: 5, type: 'MULTIPLE_CHOICE' },
      { id: 6, type: 'MULTIPLE_CHOICE' },
      { id: 7, type: 'MULTIPLE_CHOICE' },
    ];
    (prisma.question.findMany as jest.Mock).mockResolvedValueOnce(mockQuestions);

    // Mock quiz attempt creation
    const mockQuizAttempt = {
      id: 123,
      childId: 1,
      subjectId: 1,
      unitId: 1,
      questionIds: ['1', '2', '3', '4', '5'],
      currentQuestionIndex: 0,
      quizType: 'MASTERY',
      status: 'ACTIVE',
      metadata: {
        configSnapshot: {
          numQuestions: 5,
          allowTranslate: false,
          allowHints: true,
          allowAiTutor: true,
        },
      },
    };
    (prisma.quizAttempt.create as jest.Mock).mockResolvedValueOnce(mockQuizAttempt);

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        subjectId: 1,
        yearId: 1,
        unitId: 1,
      },
    });

    await handler(req, res);

    // Check response
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ attemptId: 123 });

    // Check that the correct config was fetched
    expect(prisma.quizConfig.findUnique).toHaveBeenCalledWith({
      where: { mode: 'MASTERY' },
    });

    // Check that the correct questions were fetched
    expect(prisma.question.findMany).toHaveBeenCalledWith({
      where: {
        subjectId: 1,
        yearId: 1,
        unitId: 1,
        type: { in: ['MULTIPLE_CHOICE'] },
      },
      orderBy: { id: 'asc' },
    });

    // Check that the quiz attempt was created with the correct data
    expect(prisma.quizAttempt.create).toHaveBeenCalledWith({
      data: {
        childId: 1,
        subjectId: 1,
        unitId: 1,
        questionIds: expect.arrayContaining(['1', '2', '3', '4', '5']),
        currentQuestionIndex: 0,
        quizType: 'MASTERY',
        status: 'ACTIVE',
        metadata: {
          configSnapshot: {
            numQuestions: 5,
            allowTranslate: false,
            allowHints: true,
            allowAiTutor: true,
          },
        },
      },
    });
  });

  it('should return 400 if required fields are missing', async () => {
    // Mock child session
    (getServerSession as jest.Mock).mockResolvedValueOnce(mockChildSession);

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        // Missing subjectId
        yearId: 1,
        unitId: 1,
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Subject ID is required' });
  });

  it('should return 404 if no questions are found', async () => {
    // Mock child session
    (getServerSession as jest.Mock).mockResolvedValueOnce(mockChildSession);

    // Mock quiz config
    const mockConfig = {
      id: 1,
      mode: 'MASTERY',
      numQuestions: 5,
      questionTypes: ['MULTIPLE_CHOICE'],
      allowTranslate: false,
      allowHints: true,
      allowAiTutor: true,
    };
    (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce(mockConfig);

    // Mock empty questions array
    (prisma.question.findMany as jest.Mock).mockResolvedValueOnce([]);

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        subjectId: 1,
        yearId: 1,
        unitId: 1,
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({ message: 'No questions found for the specified criteria' });
  });
});
