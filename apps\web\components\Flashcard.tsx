import { useState, useEffect } from 'react';
import Head from 'next/head';

type Card = { hanzi: string; pinyin: string; english: string };

export default function Flashcard({ card }: { card: Card }) {
  const [flipped, setFlipped] = useState(false);
  const [fontLoaded, setFontLoaded] = useState(false);

  // Check if fonts are loaded
  useEffect(() => {
    // Use the document.fonts API if available
    if (typeof document !== 'undefined' && 'fonts' in document) {
      document.fonts.ready.then(() => {
        setFontLoaded(true);
      });
    } else {
      // Fallback for browsers that don't support document.fonts
      setTimeout(() => {
        setFontLoaded(true);
      }, 1000);
    }
  }, []);

  return (
    <>
      <Head>
        {/* Add inline font for this component specifically */}
        <style jsx global>{`
          @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;700&display=swap');
        `}</style>
      </Head>
      <div
        onClick={() => setFlipped(!flipped)}
        className="w-72 h-48 bg-white rounded-xl shadow-md flex items-center justify-center cursor-pointer select-none"
      >
        {!flipped ? (
          <div
            className="text-center w-full h-full flex items-center justify-center"
            style={{
              fontFamily: "'Noto Sans SC', 'Microsoft YaHei', 'SimSun', sans-serif",
              fontSize: '5rem',
              fontWeight: 'bold',
              color: '#000',
              opacity: fontLoaded ? 1 : 0, // Only show when font is loaded
              transition: 'opacity 0.3s ease'
            }}
          >
            {card.hanzi}
          </div>
        ) : (
          <div className="text-center w-full p-4">
            <div className="text-xl font-medium text-black mb-3">{card.pinyin}</div>
            <div className="text-lg text-gray-700">{card.english}</div>
          </div>
        )}
      </div>
    </>
  );
}
