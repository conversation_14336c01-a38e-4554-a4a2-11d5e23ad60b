"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/student-dashboard",{

/***/ "(pages-dir-browser)/./pages/student-dashboard.tsx":
/*!*************************************!*\
  !*** ./pages/student-dashboard.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_TpPill__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TpPill */ \"(pages-dir-browser)/./components/TpPill.tsx\");\n/* harmony import */ var _hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useMastery */ \"(pages-dir-browser)/./hooks/useMastery.ts\");\n/* harmony import */ var _DifficultyModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DifficultyModal */ \"(pages-dir-browser)/./pages/DifficultyModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Map subject names to icons and colors\nconst subjectIcons = {\n    'Math': '🔢',\n    'Science': '🧪',\n    'Chinese': '🀄',\n    'English': '📚',\n    'Malay': '🇲🇾',\n    'History': '📜',\n    'Geography': '🌍',\n    'Art': '🎨',\n    'Music': '🎵',\n    'Physical Education': '⚽'\n};\nconst subjectColors = {\n    'Math': 'bg-blue-500',\n    'Science': 'bg-green-500',\n    'Chinese': 'bg-red-500',\n    'English': 'bg-purple-500',\n    'Malay': 'bg-yellow-500',\n    'History': 'bg-amber-500',\n    'Geography': 'bg-emerald-500',\n    'Art': 'bg-pink-500',\n    'Music': 'bg-indigo-500',\n    'Physical Education': 'bg-orange-500'\n};\n// This is v2 of the dashboard.\nconst StudentDashboard = ()=>{\n    var _subjects_find, _subjects_find1;\n    _s();\n    var _s1 = $RefreshSig$();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showStreakModal, setShowStreakModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [units, setUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childData, setChildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDifficultyModal, setShowDifficultyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUnit, setSelectedUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle error messages from query parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            const { error: queryError } = router.query;\n            if (queryError === 'unauthorized-quiz-access') {\n                setError('You can only view your own quizzes. Please select a subject and start a new quiz.');\n                // Clear the error from URL after displaying\n                router.replace('/student-dashboard', undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        router.query\n    ]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        streak: 5,\n        xp: 230,\n        gems: 45,\n        hearts: 5\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            // Redirect to login if not authenticated\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch child data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (status === 'authenticated' && (session === null || session === void 0 ? void 0 : session.user)) {\n                setLoading(true);\n                // Fetch the child's data\n                fetch('/api/child-data').then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch child data');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setChildData(data);\n                        // Now fetch subjects based on the child's year\n                        return fetch(\"/api/subjects-by-year?yearNumber=\".concat(encodeURIComponent(data.year)));\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch subjects');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        // Add icon and color to each subject\n                        const enhancedSubjects = data.map({\n                            \"StudentDashboard.useEffect.enhancedSubjects\": (subject)=>({\n                                    ...subject,\n                                    icon: subjectIcons[subject.name] || '📚',\n                                    color: subjectColors[subject.name] || 'bg-gray-500',\n                                    completed: 0,\n                                    total: subject.unitCount || 0,\n                                    unlocked: true\n                                })\n                        }[\"StudentDashboard.useEffect.enhancedSubjects\"]);\n                        setSubjects(enhancedSubjects);\n                        // Try to get the saved subject selection from localStorage\n                        try {\n                            const savedSubjectId = localStorage.getItem('selectedSubjectId');\n                            if (savedSubjectId !== null) {\n                                const parsedId = parseInt(savedSubjectId, 10);\n                                // Check if the saved subject exists in the fetched subjects\n                                if (!isNaN(parsedId) && enhancedSubjects.some({\n                                    \"StudentDashboard.useEffect\": (subject)=>subject.id === parsedId\n                                }[\"StudentDashboard.useEffect\"])) {\n                                    setSelectedSubject(parsedId);\n                                } else {\n                                    // If saved subject doesn't exist in current subjects, default to first one\n                                    if (enhancedSubjects.length > 0) {\n                                        setSelectedSubject(enhancedSubjects[0].id);\n                                    }\n                                }\n                            } else {\n                                // No saved selection, default to first subject\n                                if (enhancedSubjects.length > 0) {\n                                    setSelectedSubject(enhancedSubjects[0].id);\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error accessing localStorage:', error);\n                            // Fall back to default behavior\n                            if (enhancedSubjects.length > 0) {\n                                setSelectedSubject(enhancedSubjects[0].id);\n                            }\n                        }\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching data:', error);\n                        setError('Failed to load data. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        session\n    ]);\n    // Save selected subject to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null) {\n                try {\n                    localStorage.setItem('selectedSubjectId', selectedSubject.toString());\n                } catch (error) {\n                    console.error('Error saving to localStorage:', error);\n                }\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject\n    ]);\n    // Fetch units when a subject is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null && childData) {\n                setLoading(true);\n                fetch(\"/api/units-by-subject-year?subjectId=\".concat(selectedSubject, \"&yearNumber=\").concat(encodeURIComponent(childData.year))).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch units');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setUnits({\n                            \"StudentDashboard.useEffect\": (prevUnits)=>({\n                                    ...prevUnits,\n                                    [selectedSubject]: data\n                                })\n                        }[\"StudentDashboard.useEffect\"]);\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching units:', error);\n                        setError('Failed to load units. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject,\n        childData\n    ]);\n    // Create a component to handle mastery data for a single unit\n    const UnitMasteryPill = (param)=>{\n        let { unitId, studentId } = param;\n        _s1();\n        const masteryData = (0,_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__.useMastery)(studentId, unitId);\n        var _masteryData_currentTp, _masteryData_confidence;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TpPill__WEBPACK_IMPORTED_MODULE_5__.TpPill, {\n            tp: (_masteryData_currentTp = masteryData === null || masteryData === void 0 ? void 0 : masteryData.currentTp) !== null && _masteryData_currentTp !== void 0 ? _masteryData_currentTp : 0,\n            confidence: (_masteryData_confidence = masteryData === null || masteryData === void 0 ? void 0 : masteryData.confidence) !== null && _masteryData_confidence !== void 0 ? _masteryData_confidence : 'low'\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(UnitMasteryPill, \"h03Z9Y+VegXFEVt8zvVx+LDM+0g=\", false, function() {\n        return [\n            _hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__.useMastery\n        ];\n    });\n    const handleStartQuiz = (unit)=>{\n        // Show the difficulty modal and store the selected unit\n        setSelectedUnit(unit);\n        setShowDifficultyModal(true);\n    };\n    const handleDifficultySelect = async (difficulty)=>{\n        // Close the modal\n        setShowDifficultyModal(false);\n        if (!selectedUnit || !childData) return;\n        try {\n            setLoading(true);\n            // Get the year ID from the API if not available in childData\n            let yearId = childData.yearId;\n            if (!yearId) {\n                try {\n                    // Extract year number from the year string (e.g., \"Year 5\" -> 5)\n                    const yearMatch = childData.year.match(/\\d+/);\n                    const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;\n                    if (yearNumber) {\n                        // Fetch the year ID based on the year number\n                        const yearResponse = await fetch(\"/api/years?yearNumber=\".concat(yearNumber));\n                        if (yearResponse.ok) {\n                            const yearData = await yearResponse.json();\n                            if (yearData && yearData.id) {\n                                yearId = yearData.id;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error fetching year ID:', error);\n                }\n            }\n            if (!yearId) {\n                setError('Could not determine year ID. Please try again or contact support.');\n                setLoading(false);\n                return;\n            }\n            // Create a practice quiz attempt using the new API\n            const response = await fetch('/api/quiz/create-practice', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    subjectId: selectedSubject,\n                    yearId: yearId,\n                    unitId: selectedUnit.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to create practice quiz');\n            }\n            const data = await response.json();\n            // Check if we should use v2 quiz\n            const quizVersion = \"v2        # Controls which quiz version to use (v1 or v2)\" || 0;\n            // Very visible debugging\n            alert(\"DEBUG: Quiz Version = \".concat(quizVersion, \", Env Var = \").concat(\"v2        # Controls which quiz version to use (v1 or v2)\"));\n            console.log(\"Quiz version from env:\", quizVersion);\n            console.log(\"All env vars:\", {\n                NEXT_PUBLIC_QUIZ_VERSION: \"v2        # Controls which quiz version to use (v1 or v2)\",\n                NEXT_PUBLIC_FEATURE_ADAPTIVE_V2: \"on\"\n            });\n            if (quizVersion === 'v2') {\n                console.log(\"Redirecting to V2 quiz:\", \"/quiz/v2/\".concat(data.attemptId));\n                router.push(\"/quiz/v2/\".concat(data.attemptId));\n            } else {\n                console.log(\"Redirecting to V1 quiz:\", \"/quiz?attemptId=\".concat(data.attemptId));\n                router.push(\"/quiz?attemptId=\".concat(data.attemptId));\n            }\n        } catch (error) {\n            console.error('Error creating practice quiz:', error);\n            setError('Failed to start practice quiz. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDifficultyModalClose = ()=>{\n        setShowDifficultyModal(false);\n        setSelectedUnit(null);\n    };\n    const handleShowStreak = ()=>{\n        setShowStreakModal(true);\n    };\n    const handleTestClick = ()=>{\n        // Navigate to the test page or show test options\n        router.push('/start-quiz');\n    };\n    // Show loading state while checking authentication\n    if (status === 'loading' || status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Student Dashboard | Studu\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Student dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-full min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 w-full bg-[#0F5FA6] text-white p-3 flex items-center justify-between shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold mr-2\",\n                                        children: \"Studu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm bg-[#0A8CBF] px-3 py-1 rounded-full\",\n                                        children: [\n                                            childData.name,\n                                            \" | \",\n                                            childData.year\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShowStreak,\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.streak\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.GemIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.gems\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HeartIcon, {\n                                                size: 20,\n                                                className: \"text-red-500 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.hearts\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 bg-gray-100 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Subjects\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, undefined) : error && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.reload(),\n                                                className: \"mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm\",\n                                                children: \"Retry\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, undefined) : subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No subjects found for your year. Please contact your teacher.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex overflow-x-auto pb-2 space-x-3\",\n                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedSubject(subject.id),\n                                                className: \"flex flex-col items-center justify-center p-4 rounded-lg shadow-md min-w-[100px] h-[100px] \".concat(selectedSubject === subject.id ? 'ring-4 ring-yellow-400' : '', \" \").concat(subject.unlocked ? subject.color : 'bg-gray-400'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mb-1\",\n                                                        children: subject.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: subject.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-white/80 mt-1\",\n                                                        children: [\n                                                            subject.completed,\n                                                            \"/\",\n                                                            subject.total\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, subject.id, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, undefined),\n                            selectedSubject !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Learning Path\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-4 shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mr-3\",\n                                                        children: (_subjects_find = subjects.find((s)=>s.id === selectedSubject)) === null || _subjects_find === void 0 ? void 0 : _subjects_find.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold text-lg\",\n                                                                children: (_subjects_find1 = subjects.find((s)=>s.id === selectedSubject)) === null || _subjects_find1 === void 0 ? void 0 : _subjects_find1.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Complete lessons to unlock new content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            loading && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, undefined) : error && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, undefined) : !units[selectedSubject] || units[selectedSubject].length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"No units found for this subject. Please contact your teacher.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-4\",\n                                                children: units[selectedSubject].map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative border-2 rounded-lg p-4 hover:shadow-md transition-shadow \".concat(unit.unlocked ? 'border-[#0A8CBF] bg-gradient-to-r from-[#04B2D9]/5 to-white' : 'border-gray-300 bg-gray-100'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-bold flex items-center gap-2\",\n                                                                            children: [\n                                                                                \"Unit \",\n                                                                                unit.unitNumber || index + 1,\n                                                                                \": \",\n                                                                                unit.name,\n                                                                                 true && childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnitMasteryPill, {\n                                                                                    unitId: unit.id,\n                                                                                    studentId: childData.id\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                    lineNumber: 485,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        unit.nameZh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                                            children: unit.nameZh\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 492,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                unit.unlocked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        var _subjects_find;\n                                                                        return handleStartQuiz({\n                                                                            id: unit.id,\n                                                                            name: unit.name,\n                                                                            subject: ((_subjects_find = subjects.find((s)=>s.id === selectedSubject)) === null || _subjects_find === void 0 ? void 0 : _subjects_find.name) || ''\n                                                                        });\n                                                                    },\n                                                                    className: \"px-4 py-2 rounded-lg font-bold bg-[#0F5FA6] text-white\",\n                                                                    children: \"Practice\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 499,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-300 p-2 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"20\",\n                                                                        height: \"20\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        className: \"text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                width: \"18\",\n                                                                                height: \"11\",\n                                                                                x: \"3\",\n                                                                                y: \"11\",\n                                                                                rx: \"2\",\n                                                                                ry: \"2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 523,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, unit.id, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"sticky bottom-0 w-full bg-white border-t border-gray-200 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-around items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-[#0F5FA6]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HomeIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.BookIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.TrophyIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 551,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.UserIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleTestClick,\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ClipboardCheckIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Test\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 9\n                    }, undefined),\n                    showStreakModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl max-w-md w-full p-6 shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                    size: 28,\n                                                    className: \"text-[#0F5FA6] mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Your Streak\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowStreakModal(false),\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 6 6 18\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"m6 6 12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl font-bold text-[#0F5FA6] mb-2\",\n                                            children: stats.streak\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                stats.streak === 1 ? 'day' : 'days',\n                                                \" in a row\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        {\n                                            days: 7,\n                                            label: '1 Week',\n                                            status: stats.streak >= 7 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 14,\n                                            label: '2 Weeks',\n                                            status: stats.streak >= 14 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 30,\n                                            label: '1 Month',\n                                            status: stats.streak >= 30 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 100,\n                                            label: '100 Days',\n                                            status: stats.streak >= 100 ? 'reached' : 'upcoming'\n                                        }\n                                    ].map((milestone)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center p-4 rounded-lg \".concat(milestone.status === 'reached' ? 'bg-[#04B2D9]/10' : 'bg-gray-100'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full flex items-center justify-center mr-3 \".concat(milestone.status === 'reached' ? 'bg-[#0F5FA6] text-white' : 'bg-gray-300'),\n                                                            children: milestone.status === 'reached' ? '✓' : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: milestone.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold\",\n                                                    children: [\n                                                        milestone.days,\n                                                        \" days\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 623,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, milestone.days, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 607,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 600,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-600 text-sm\",\n                                    children: \"Keep learning daily to build your streak!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 11\n                    }, undefined),\n                    showDifficultyModal && selectedUnit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DifficultyModal__WEBPACK_IMPORTED_MODULE_7__.DifficultyModal, {\n                        onSelect: handleDifficultySelect,\n                        onClose: handleDifficultyModalClose,\n                        unit: selectedUnit\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 636,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(StudentDashboard, \"gVnNjaXlOzbHb5j/UlPBR83zePo=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StudentDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StudentDashboard);\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/student-dashboard.tsx\n"));

/***/ })

});