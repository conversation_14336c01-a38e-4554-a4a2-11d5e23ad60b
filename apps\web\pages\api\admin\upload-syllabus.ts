import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};

interface SyllabusUnit {
  unit: number;
  topic: {
    zh: string;
    en: string;
  };
}

interface SyllabusData {
  year: number;
  subject: string;
  syllabus: SyllabusUnit[];
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const syllabusData: SyllabusData = req.body;

    // Validate the input data
    if (!syllabusData.year || !syllabusData.subject || !Array.isArray(syllabusData.syllabus)) {
      return res.status(400).json({ message: 'Invalid syllabus data format' });
    }

    // Create or get the subject
    const subject = await prisma.subject.upsert({
      where: { name: syllabusData.subject },
      update: {},
      create: { name: syllabusData.subject }
    });

    // Create or get the year
    const year = await prisma.year.upsert({
      where: { yearNumber: syllabusData.year },
      update: {},
      create: { yearNumber: syllabusData.year }
    });

    // Delete existing units for this year and subject
    await prisma.unit.deleteMany({
      where: {
        yearId: year.id,
        subjectId: subject.id,
      },
    });

    // Create new units
    const units = await Promise.all(
      syllabusData.syllabus.map(async (unitData) => {
        return prisma.unit.create({
          data: {
            unitNumber: unitData.unit,
            topicEn: unitData.topic.en,
            topicZh: unitData.topic.zh,
            subject: { connect: { id: subject.id } },
            year: { connect: { id: year.id } }
          }
        });
      })
    );

    res.status(200).json({ 
      message: 'Syllabus updated successfully',
      units: units 
    });
  } catch (error) {
    console.error('Error updating syllabus:', error);
    res.status(500).json({ message: 'Error updating syllabus' });
  }
}