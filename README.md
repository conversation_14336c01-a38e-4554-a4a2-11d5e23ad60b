# my-quiz-app

This is a quiz application that supports multiple question types.

## Answer & StudentAnswer Schema

The application supports various question types with different answer formats:

| Answer Type | Description | answerSpec Format | submittedJson Format |
|-------------|-------------|------------------|----------------------|
| SINGLE_CHOICE | Single-select multiple choice | Not used (uses `key` field) | Not used (uses `submittedKey` field) |
| MULTI_CHOICE | Multi-select multiple choice | `{ keys: ["A", "C", "D"] }` | `{ keys: ["A", "C", "D"] }` |
| SHORT_TEXT | Short text answer | Not used (uses `textEn/textZh/textMs` fields) | Not used (uses `submittedText` field) |
| TRUE_FALSE | True/False question | Not used (uses `key` field with "true"/"false") | Not used (uses `submittedKey` field) |
| FILL_IN_THE_BLANK | Fill in the blank | `{ acceptableAnswers: ["answer1", "answer2"] }` | Not used (uses `submittedText` field) |
| MATCHING | Matching pairs | `{ pairs: [{ left: "item1", right: "match1" }, ...] }` | `{ pairs: [{ left: "item1", right: "match1" }, ...] }` |
| SEQUENCING | Ordering items | `{ order: [1, 2, 3, 4, 5] }` | `{ order: [1, 2, 3, 4, 5] }` |
| LONG_TEXT_RUBRIC | Essay with rubric | `{ rubric: { criteria: [...], maxScore: 10 } }` | `{ text: "essay content" }` |

## Migration and Backfill

After updating the schema, run the following commands:

```bash
# Run migration
npx prisma migrate dev --name add-answer-spec
npx prisma generate

# Backfill existing data
npm run backfill:answer
```


# Database setup

Update the .env file to update the username and password


Run

docker-compose up -d

# Start only the DB
docker-compose up -d db

# Test connection
psql "postgresql://appuser:mysecretpassword!!@localhost:5432/education"

# Create a backup
docker-compose run --rm backup
# → File drops to ./backups/education-YYYY-MM-DD_HHMMSS.sql

# Tear down (keep data)
docker-compose down

# Tear down (wipe data)
docker-compose down -v

# Useful stuff
## Reset database. DATA LOSS. Used to change postgres username
docker-compose down
docker volume rm my-quiz-app_pgdata
docker-compose up -d db

# Run Web application

npm run dev

# Environment variables

## Adaptive TP-Mastery (beta)

| Variable | Scope | Default | Description |
|----------|-------|---------|-------------|
| `FEATURE_ADAPTIVE_V2` | Server | `off` | Enables real-time mastery computation & adaptive item selection |
| `NEXT_PUBLIC_FEATURE_ADAPTIVE_V2` | Client | `off` | Exposes the flag to the browser so the UI can display the TP pill |

Set both to `on` for beta cohorts. Toggle to `off` to disable the feature without redeploying DB migrations.

