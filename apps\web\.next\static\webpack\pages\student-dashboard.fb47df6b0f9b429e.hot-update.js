"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/student-dashboard",{

/***/ "(pages-dir-browser)/./pages/student-dashboard.tsx":
/*!*************************************!*\
  !*** ./pages/student-dashboard.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(pages-dir-browser)/../../node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!lucide-react */ \"(pages-dir-browser)/__barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_TpPill__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TpPill */ \"(pages-dir-browser)/./components/TpPill.tsx\");\n/* harmony import */ var _hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useMastery */ \"(pages-dir-browser)/./hooks/useMastery.ts\");\n/* harmony import */ var _DifficultyModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DifficultyModal */ \"(pages-dir-browser)/./pages/DifficultyModal.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// Map subject names to icons and colors\nconst subjectIcons = {\n    'Math': '🔢',\n    'Science': '🧪',\n    'Chinese': '🀄',\n    'English': '📚',\n    'Malay': '🇲🇾',\n    'History': '📜',\n    'Geography': '🌍',\n    'Art': '🎨',\n    'Music': '🎵',\n    'Physical Education': '⚽'\n};\nconst subjectColors = {\n    'Math': 'bg-blue-500',\n    'Science': 'bg-green-500',\n    'Chinese': 'bg-red-500',\n    'English': 'bg-purple-500',\n    'Malay': 'bg-yellow-500',\n    'History': 'bg-amber-500',\n    'Geography': 'bg-emerald-500',\n    'Art': 'bg-pink-500',\n    'Music': 'bg-indigo-500',\n    'Physical Education': 'bg-orange-500'\n};\n// This is v2 of the dashboard.\nconst StudentDashboard = ()=>{\n    var _subjects_find, _subjects_find1;\n    _s();\n    var _s1 = $RefreshSig$();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showStreakModal, setShowStreakModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [units, setUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childData, setChildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDifficultyModal, setShowDifficultyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUnit, setSelectedUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle error messages from query parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            const { error: queryError } = router.query;\n            if (queryError === 'unauthorized-quiz-access') {\n                setError('You can only view your own quizzes. Please select a subject and start a new quiz.');\n                // Clear the error from URL after displaying\n                router.replace('/student-dashboard', undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        router.query\n    ]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        streak: 5,\n        xp: 230,\n        gems: 45,\n        hearts: 5\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            // Redirect to login if not authenticated\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch child data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (status === 'authenticated' && (session === null || session === void 0 ? void 0 : session.user)) {\n                setLoading(true);\n                // Fetch the child's data\n                fetch('/api/child-data').then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch child data');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setChildData(data);\n                        // Now fetch subjects based on the child's year\n                        return fetch(\"/api/subjects-by-year?yearNumber=\".concat(encodeURIComponent(data.year)));\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch subjects');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        // Add icon and color to each subject\n                        const enhancedSubjects = data.map({\n                            \"StudentDashboard.useEffect.enhancedSubjects\": (subject)=>({\n                                    ...subject,\n                                    icon: subjectIcons[subject.name] || '📚',\n                                    color: subjectColors[subject.name] || 'bg-gray-500',\n                                    completed: 0,\n                                    total: subject.unitCount || 0,\n                                    unlocked: true\n                                })\n                        }[\"StudentDashboard.useEffect.enhancedSubjects\"]);\n                        setSubjects(enhancedSubjects);\n                        // Try to get the saved subject selection from localStorage\n                        try {\n                            const savedSubjectId = localStorage.getItem('selectedSubjectId');\n                            if (savedSubjectId !== null) {\n                                const parsedId = parseInt(savedSubjectId, 10);\n                                // Check if the saved subject exists in the fetched subjects\n                                if (!isNaN(parsedId) && enhancedSubjects.some({\n                                    \"StudentDashboard.useEffect\": (subject)=>subject.id === parsedId\n                                }[\"StudentDashboard.useEffect\"])) {\n                                    setSelectedSubject(parsedId);\n                                } else {\n                                    // If saved subject doesn't exist in current subjects, default to first one\n                                    if (enhancedSubjects.length > 0) {\n                                        setSelectedSubject(enhancedSubjects[0].id);\n                                    }\n                                }\n                            } else {\n                                // No saved selection, default to first subject\n                                if (enhancedSubjects.length > 0) {\n                                    setSelectedSubject(enhancedSubjects[0].id);\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error accessing localStorage:', error);\n                            // Fall back to default behavior\n                            if (enhancedSubjects.length > 0) {\n                                setSelectedSubject(enhancedSubjects[0].id);\n                            }\n                        }\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching data:', error);\n                        setError('Failed to load data. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        session\n    ]);\n    // Save selected subject to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null) {\n                try {\n                    localStorage.setItem('selectedSubjectId', selectedSubject.toString());\n                } catch (error) {\n                    console.error('Error saving to localStorage:', error);\n                }\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject\n    ]);\n    // Fetch units when a subject is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null && childData) {\n                setLoading(true);\n                fetch(\"/api/units-by-subject-year?subjectId=\".concat(selectedSubject, \"&yearNumber=\").concat(encodeURIComponent(childData.year))).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch units');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setUnits({\n                            \"StudentDashboard.useEffect\": (prevUnits)=>({\n                                    ...prevUnits,\n                                    [selectedSubject]: data\n                                })\n                        }[\"StudentDashboard.useEffect\"]);\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching units:', error);\n                        setError('Failed to load units. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject,\n        childData\n    ]);\n    // Create a component to handle mastery data for a single unit\n    const UnitMasteryPill = (param)=>{\n        let { unitId, studentId } = param;\n        _s1();\n        const masteryData = (0,_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__.useMastery)(studentId, unitId);\n        var _masteryData_currentTp, _masteryData_confidence;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TpPill__WEBPACK_IMPORTED_MODULE_5__.TpPill, {\n            tp: (_masteryData_currentTp = masteryData === null || masteryData === void 0 ? void 0 : masteryData.currentTp) !== null && _masteryData_currentTp !== void 0 ? _masteryData_currentTp : 0,\n            confidence: (_masteryData_confidence = masteryData === null || masteryData === void 0 ? void 0 : masteryData.confidence) !== null && _masteryData_confidence !== void 0 ? _masteryData_confidence : 'low'\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(UnitMasteryPill, \"h03Z9Y+VegXFEVt8zvVx+LDM+0g=\", false, function() {\n        return [\n            _hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__.useMastery\n        ];\n    });\n    const handleStartQuiz = (unit)=>{\n        // Show the difficulty modal and store the selected unit\n        setSelectedUnit(unit);\n        setShowDifficultyModal(true);\n    };\n    const handleDifficultySelect = async (difficulty)=>{\n        // Close the modal\n        setShowDifficultyModal(false);\n        if (!selectedUnit || !childData) return;\n        try {\n            setLoading(true);\n            // Get the year ID from the API if not available in childData\n            let yearId = childData.yearId;\n            if (!yearId) {\n                try {\n                    // Extract year number from the year string (e.g., \"Year 5\" -> 5)\n                    const yearMatch = childData.year.match(/\\d+/);\n                    const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;\n                    if (yearNumber) {\n                        // Fetch the year ID based on the year number\n                        const yearResponse = await fetch(\"/api/years?yearNumber=\".concat(yearNumber));\n                        if (yearResponse.ok) {\n                            const yearData = await yearResponse.json();\n                            if (yearData && yearData.id) {\n                                yearId = yearData.id;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error fetching year ID:', error);\n                }\n            }\n            if (!yearId) {\n                setError('Could not determine year ID. Please try again or contact support.');\n                setLoading(false);\n                return;\n            }\n            // Create a practice quiz attempt using the new API\n            const response = await fetch('/api/quiz/create-practice', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    subjectId: selectedSubject,\n                    yearId: yearId,\n                    unitId: selectedUnit.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to create practice quiz');\n            }\n            const data = await response.json();\n            // Check if we should use v2 quiz\n            const quizVersion = \"v2        # Controls which quiz version to use (v1 or v2)\" || 0;\n            console.log(\"🎯 QUIZ VERSION DEBUG:\");\n            console.log(\"Quiz version from env:\", quizVersion);\n            console.log(\"Environment variable:\", \"v2        # Controls which quiz version to use (v1 or v2)\");\n            console.log(\"Condition check (quizVersion === 'v2'):\", quizVersion === 'v2');\n            if (quizVersion === 'v2') {\n                console.log(\"✅ Redirecting to V2 quiz:\", \"/quiz/v2/\".concat(data.attemptId));\n                router.push(\"/quiz/v2/\".concat(data.attemptId));\n            } else {\n                console.log(\"❌ Redirecting to V1 quiz:\", \"/quiz?attemptId=\".concat(data.attemptId));\n                router.push(\"/quiz?attemptId=\".concat(data.attemptId));\n            }\n        } catch (error) {\n            console.error('Error creating practice quiz:', error);\n            setError('Failed to start practice quiz. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDifficultyModalClose = ()=>{\n        setShowDifficultyModal(false);\n        setSelectedUnit(null);\n    };\n    const handleShowStreak = ()=>{\n        setShowStreakModal(true);\n    };\n    const handleTestClick = ()=>{\n        // Navigate to the test page or show test options\n        router.push('/start-quiz');\n    };\n    // Show loading state while checking authentication\n    if (status === 'loading' || status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Student Dashboard | Studu\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Student dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-full min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 w-full bg-[#0F5FA6] text-white p-3 flex items-center justify-between shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold mr-2\",\n                                        children: \"Studu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm bg-[#0A8CBF] px-3 py-1 rounded-full\",\n                                        children: [\n                                            childData.name,\n                                            \" | \",\n                                            childData.year\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShowStreak,\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.streak\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.GemIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.gems\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HeartIcon, {\n                                                size: 20,\n                                                className: \"text-red-500 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.hearts\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 bg-gray-100 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Subjects\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined) : error && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.reload(),\n                                                className: \"mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm\",\n                                                children: \"Retry\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, undefined) : subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No subjects found for your year. Please contact your teacher.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex overflow-x-auto pb-2 space-x-3\",\n                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedSubject(subject.id),\n                                                className: \"flex flex-col items-center justify-center p-4 rounded-lg shadow-md min-w-[100px] h-[100px] \".concat(selectedSubject === subject.id ? 'ring-4 ring-yellow-400' : '', \" \").concat(subject.unlocked ? subject.color : 'bg-gray-400'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mb-1\",\n                                                        children: subject.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: subject.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-white/80 mt-1\",\n                                                        children: [\n                                                            subject.completed,\n                                                            \"/\",\n                                                            subject.total\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, subject.id, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined),\n                            selectedSubject !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Learning Path\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-4 shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mr-3\",\n                                                        children: (_subjects_find = subjects.find((s)=>s.id === selectedSubject)) === null || _subjects_find === void 0 ? void 0 : _subjects_find.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold text-lg\",\n                                                                children: (_subjects_find1 = subjects.find((s)=>s.id === selectedSubject)) === null || _subjects_find1 === void 0 ? void 0 : _subjects_find1.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Complete lessons to unlock new content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            loading && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, undefined) : error && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, undefined) : !units[selectedSubject] || units[selectedSubject].length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"No units found for this subject. Please contact your teacher.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-4\",\n                                                children: units[selectedSubject].map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative border-2 rounded-lg p-4 hover:shadow-md transition-shadow \".concat(unit.unlocked ? 'border-[#0A8CBF] bg-gradient-to-r from-[#04B2D9]/5 to-white' : 'border-gray-300 bg-gray-100'),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-bold flex items-center gap-2\",\n                                                                            children: [\n                                                                                \"Unit \",\n                                                                                unit.unitNumber || index + 1,\n                                                                                \": \",\n                                                                                unit.name,\n                                                                                 true && childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnitMasteryPill, {\n                                                                                    unitId: unit.id,\n                                                                                    studentId: childData.id\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                    lineNumber: 482,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        unit.nameZh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                                            children: unit.nameZh\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                unit.unlocked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>{\n                                                                        var _subjects_find;\n                                                                        return handleStartQuiz({\n                                                                            id: unit.id,\n                                                                            name: unit.name,\n                                                                            subject: ((_subjects_find = subjects.find((s)=>s.id === selectedSubject)) === null || _subjects_find === void 0 ? void 0 : _subjects_find.name) || ''\n                                                                        });\n                                                                    },\n                                                                    className: \"px-4 py-2 rounded-lg font-bold bg-[#0F5FA6] text-white\",\n                                                                    children: \"Practice\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-300 p-2 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"20\",\n                                                                        height: \"20\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        className: \"text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                width: \"18\",\n                                                                                height: \"11\",\n                                                                                x: \"3\",\n                                                                                y: \"11\",\n                                                                                rx: \"2\",\n                                                                                ry: \"2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 521,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, unit.id, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"sticky bottom-0 w-full bg-white border-t border-gray-200 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-around items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-[#0F5FA6]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HomeIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.BookIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.TrophyIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.UserIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleTestClick,\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ClipboardCheckIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Test\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, undefined),\n                    showStreakModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl max-w-md w-full p-6 shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                    size: 28,\n                                                    className: \"text-[#0F5FA6] mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Your Streak\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowStreakModal(false),\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 6 6 18\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"m6 6 12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl font-bold text-[#0F5FA6] mb-2\",\n                                            children: stats.streak\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                stats.streak === 1 ? 'day' : 'days',\n                                                \" in a row\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        {\n                                            days: 7,\n                                            label: '1 Week',\n                                            status: stats.streak >= 7 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 14,\n                                            label: '2 Weeks',\n                                            status: stats.streak >= 14 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 30,\n                                            label: '1 Month',\n                                            status: stats.streak >= 30 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 100,\n                                            label: '100 Days',\n                                            status: stats.streak >= 100 ? 'reached' : 'upcoming'\n                                        }\n                                    ].map((milestone)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center p-4 rounded-lg \".concat(milestone.status === 'reached' ? 'bg-[#04B2D9]/10' : 'bg-gray-100'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-full flex items-center justify-center mr-3 \".concat(milestone.status === 'reached' ? 'bg-[#0F5FA6] text-white' : 'bg-gray-300'),\n                                                            children: milestone.status === 'reached' ? '✓' : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: milestone.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold\",\n                                                    children: [\n                                                        milestone.days,\n                                                        \" days\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, milestone.days, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-600 text-sm\",\n                                    children: \"Keep learning daily to build your streak!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 11\n                    }, undefined),\n                    showDifficultyModal && selectedUnit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DifficultyModal__WEBPACK_IMPORTED_MODULE_7__.DifficultyModal, {\n                        onSelect: handleDifficultySelect,\n                        onClose: handleDifficultyModalClose,\n                        unit: selectedUnit\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(StudentDashboard, \"gVnNjaXlOzbHb5j/UlPBR83zePo=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = StudentDashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StudentDashboard);\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/student-dashboard.tsx\n"));

/***/ })

});