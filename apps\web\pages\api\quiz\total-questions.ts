import { PrismaClient } from '@prisma/client';
import { NextApiRequest, NextApiResponse } from 'next';

const prisma = new PrismaClient();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { attemptId } = req.query;

  if (!attemptId || typeof attemptId !== 'string') {
    return res.status(400).json({ message: 'Missing or invalid attemptId' });
  }

  try {
    const quizAttempt = await prisma.quizAttempt.findUnique({
      where: {
        id: parseInt(attemptId, 10),
      },
      select: {
        questionIds: true,
      },
    });

    if (!quizAttempt) {
      return res.status(404).json({ message: 'Quiz attempt not found' });
    }

    const totalQuestions = quizAttempt.questionIds ? quizAttempt.questionIds.length : 0;

    return res.status(200).json({ totalQuestions });
  } catch (error) {
    console.error('Error fetching total questions:', error);
    return res.status(500).json({ message: 'Internal Server Error' });
  } finally {
    await prisma.$disconnect();
  }
}
