import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getServerSession } from "next-auth/next"; // Use recommended getServerSession
import { authOptions } from '../auth/[...nextauth]'; // Import authOptions

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Use getServerSession for server-side session retrieval
  const session = await getServerSession(req, res, authOptions);

  // Ensure the user is authenticated and is a parent
  if (!session) {
    console.log('403 Forbidden: Session is missing.');
    return res.status(403).json({ message: 'Forbidden' });
  }

  if (session.user?.role !== 'PARENT') {
    console.log(`403 Forbidden: User role is "${session.user?.role}" instead of "PARENT".`);
    return res.status(403).json({ message: 'Forbidden' });
  }

  const parentId = session.user.id; // Assuming session.user.id is the parent's database ID

  try {
    // Find the children associated with this parent
    const children = await prisma.child.findMany({
      where: {
        accountId: Number(parentId),
      },
      select: {
        id: true,
        name: true,
      },
    });

    const childIds = children.map(child => child.id);

    // Find incomplete quiz attempts for these children
    const incompleteQuizzes = await prisma.quizAttempt.findMany({
      where: {
        childId: {
          in: childIds,
        },
        endTime: null, // Quiz is incomplete if endTime is null
      },
      select: {
        id: true,
        startTime: true,
        currentQuestionIndex: true,
        // Removed questionIds and quizType as they don't exist on the model
        child: {
          select: {
            name: true,
          },
        },
        subject: {
          select: {
            name: true,
          },
        },
        unit: {
          select: {
            unitNumber: true,
          },
        },
      },
      orderBy: {
        startTime: 'desc', // Order by most recent attempts first
      },
    });

    // Format the output to include elapsed time and attempted questions count
    const formattedIncompleteQuizzes = incompleteQuizzes.map(quiz => ({
      id: quiz.id,
      childName: quiz.child.name,
      subject: quiz.subject.name,
      unit: quiz.unit?.unitNumber,
      // Removed quizType
      attemptedQuestions: quiz.currentQuestionIndex ?? 0, // Default to 0 if null
      // Removed totalQuestions as questionIds is not available
      startTime: quiz.startTime,
      elapsedTimeSeconds: Math.floor((Date.now() - new Date(quiz.startTime).getTime()) / 1000),
    }));


    res.status(200).json({ incompleteQuizzes: formattedIncompleteQuizzes });

  } catch (error) {
    console.error('Error fetching incomplete quizzes for parent:', error);
    res.status(500).json({ message: 'Error fetching incomplete quizzes' });
  }
}
