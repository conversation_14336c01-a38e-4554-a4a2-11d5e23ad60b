import React, { useState } from 'react';

interface AiTutorNotificationProps {
  message: string;
  onClose: () => void;
  onExpand: () => void;
  displayLanguage: 'en' | 'zh' | 'ms';
}

/**
 * A notification component that appears when the AI tutor sends a message
 * while the panel is collapsed. It appears to the left of the AI tutor button.
 */
export default function AiTutorNotification({
  message,
  onClose,
  onExpand,
  displayLanguage
}: AiTutorNotificationProps) {
  // Format the message for display
  const getFormattedMessage = () => {
    // For translation messages, show the complete translation message with formatting
    if (message.includes('Translation of "') ||
        message.includes('翻译 "') ||
        message.includes('<PERSON><PERSON>jemahan untuk "')) {

      // Format the translation message with styling
      try {
        // Extract the parts of the translation message
        let formattedMessage = message;

        // Find the position of the first quote
        const firstQuotePos = message.indexOf('"');
        if (firstQuotePos !== -1) {
          // Find the position of the second quote
          const secondQuotePos = message.indexOf('"', firstQuotePos + 1);
          if (secondQuotePos !== -1) {
            // Find the position of the colon after the second quote
            const colonPos = message.indexOf(':', secondQuotePos);
            if (colonPos !== -1) {
              // Split the message into parts
              const prefix = message.substring(0, firstQuotePos);
              const originalText = message.substring(firstQuotePos + 1, secondQuotePos);
              const middle = message.substring(secondQuotePos, colonPos + 1);
              const translatedText = message.substring(colonPos + 1);

              // Return formatted message with HTML (will be rendered as-is)
              return (
                <span>
                  <span className="text-gray-600">{prefix}"</span>
                  <span className="font-medium text-gray-800">{originalText}</span>
                  <span className="text-gray-600">{middle}</span>
                  <span className="font-medium text-blue-700">{translatedText}</span>
                </span>
              );
            }
          }
        }

        // If parsing fails, return the original message
        return message.length > 150
          ? message.substring(0, 150) + '...'
          : message;
      } catch (error) {
        // If any error occurs, return the original message
        console.error('Error formatting translation message:', error);
        return message.length > 150
          ? message.substring(0, 150) + '...'
          : message;
      }
    }

    // For thinking messages, show a special message
    if (message === 'Thinking...' ||
        message === 'Berfikir...' ||
        message === '思考中...') {
      return displayLanguage === 'en'
        ? 'AI Tutor is thinking...'
        : displayLanguage === 'ms'
          ? 'Tutor AI sedang berfikir...'
          : 'AI导师正在思考...';
    }

    // For welcome messages, format them nicely
    if (message.includes('Hello! I\'m your AI tutor') ||
        message.includes('Hai! Saya adalah tutor AI') ||
        message.includes('你好！我是你的AI导师')) {
      return displayLanguage === 'en'
        ? 'Hello! How can I help you with this question?'
        : displayLanguage === 'ms'
          ? 'Hai! Bagaimana saya boleh membantu anda dengan soalan ini?'
          : '你好！我能如何帮助你解答这个问题？';
    }

    // For other messages, clean up HTML and format properly
    let formattedMessage = message
      // Remove HTML tags
      .replace(/<[^>]*>/g, '')
      // Replace line breaks with spaces
      .replace(/\n/g, ' ')
      // Replace multiple spaces with a single space
      .replace(/\s+/g, ' ')
      // Trim whitespace
      .trim();

    // Truncate if needed
    return formattedMessage.length > 120
      ? formattedMessage.substring(0, 120) + '...'
      : formattedMessage;
  };

  // Determine the notification title based on message content
  const getNotificationTitle = () => {
    if (message.includes('Translation of "') ||
        message.includes('翻译 "') ||
        message.includes('Terjemahan untuk "')) {
      return displayLanguage === 'en' ? 'Translation Added' :
             displayLanguage === 'ms' ? 'Terjemahan Ditambah' : '翻译已添加';
    }

    // For welcome messages
    if (message.includes('Hello! I\'m your AI tutor') ||
        message.includes('Hai! Saya adalah tutor AI') ||
        message.includes('你好！我是你的AI导师')) {
      return displayLanguage === 'en' ? 'AI Tutor Ready' :
             displayLanguage === 'ms' ? 'Tutor AI Sedia' : 'AI导师准备就绪';
    }

    // Default message
    return displayLanguage === 'en' ? 'New Message' :
           displayLanguage === 'ms' ? 'Mesej Baru' : '新消息';
  };

  return (
    <div
      className="fixed right-[70px] bottom-4 bg-white rounded-lg shadow-lg border border-blue-200 z-50 cursor-pointer animate-fadeIn"
      style={{ maxWidth: '400px', minWidth: '250px', maxHeight: '180px', overflow: 'hidden' }}
    >
      {/* Arrow pointing to AI tutor button */}
      <div className="absolute right-[-10px] top-1/2 transform -translate-y-1/2 z-10">
        <div className="w-0 h-0 border-t-[8px] border-t-transparent border-l-[10px] border-l-white border-b-[8px] border-b-transparent"></div>
      </div>
      {/* Shadow for the arrow */}
      <div className="absolute right-[-11px] top-1/2 transform -translate-y-1/2 z-0">
        <div className="w-0 h-0 border-t-[9px] border-t-transparent border-l-[11px] border-l-blue-200 border-b-[9px] border-b-transparent"></div>
      </div>

      <div
        className="flex items-start p-4"
        onClick={onExpand}
      >
        <button
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
          className="text-gray-400 hover:text-gray-600 mr-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <div className="flex-1">
          <div className="flex items-center mb-1">
            <span className="text-xs font-medium text-blue-600 bg-blue-50 px-1.5 py-0.5 rounded-sm">
              {displayLanguage === 'en' ? 'AI Tutor' :
               displayLanguage === 'ms' ? 'Tutor AI' : 'AI导师'}
            </span>
          </div>
          <div className="text-sm text-gray-700 font-normal relative" style={{ maxHeight: '100px', overflow: 'hidden' }}>
            {/* Show the actual message content */}
            {getFormattedMessage()}
            {/* Gradient fade at the bottom to indicate more content */}
            <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
