# Centralized Logging System

This document describes the centralized logging and error handling system implemented in the quiz application.

## Overview

The application uses a centralized logging system with the following components:

- **Backend Logging**: Pino logger with environment-controlled levels
- **Frontend Logging**: Custom logger with optional Sentry integration
- **Global Error Handling**: Fastify global error handler
- **Worker Scripts**: Structured logging with context

## Backend Logging (`packages/core/logger.ts`)

### Features

- **Environment-controlled log levels** via `LOG_LEVEL` environment variable
- **Pretty printing in development** with timestamps and colors
- **Structured JSON logging in production**
- **Child loggers** with additional context

### Usage

```typescript
import { logger, createChildLogger } from '@quiz/core/logger';

// Basic logging
logger.info('Application started');
logger.error(error, 'Database connection failed');

// Child logger with context
const workerLogger = createChildLogger({ component: 'questionWorker' });
workerLogger.info('Processing batch', { batchId: 123 });
```

### Log Levels

- `trace` (10): Very detailed debug information
- `debug` (20): Debug information
- `info` (30): General information
- `warn` (40): Warning messages
- `error` (50): Error messages
- `fatal` (60): Fatal errors that cause application to exit

## Frontend Logging (`apps/web/lib/logger.ts`)

### Features

- **Console logging in development**
- **Optional Sentry integration** for production error tracking
- **User context setting** for error reporting
- **Breadcrumb tracking** for debugging

### Usage

```typescript
import { logInfo, logError, frontendLogger } from '../lib/logger';

// Simple logging
logInfo('User logged in');
logError('API request failed', { endpoint: '/api/quiz' });

// Advanced usage
frontendLogger.setUserContext({ id: '123', email: '<EMAIL>' });
frontendLogger.addBreadcrumb('User clicked start quiz', 'user-action');
```

## Environment Configuration

### Development (`.env.development`)

```bash
LOG_LEVEL=debug
NODE_ENV=development
```

### Production (`.env.production`)

```bash
LOG_LEVEL=warn
NODE_ENV=production
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn-here  # Optional
```

## API Server Error Handling

The Fastify server includes a global error handler that:

- **Logs all errors** with structured information
- **Returns sanitized responses** in production
- **Includes stack traces** in development
- **Uses appropriate HTTP status codes**

## Worker Scripts

Worker scripts use child loggers with component context:

```typescript
const workerLogger = createChildLogger({ component: 'questionWorker' });
workerLogger.info('Processing batch', { batchId, provider, language });
```

## Sentry Integration (Optional)

To enable Sentry error tracking:

1. Set `NEXT_PUBLIC_SENTRY_DSN` environment variable
2. Install Sentry packages: `npm install @sentry/nextjs`
3. Update the frontend logger to use actual Sentry calls

## Log Output Examples

### Development (Pretty)
```
[13:03:28] INFO: Starting question generation worker...
    component: "questionWorker"
[13:03:28] INFO: Processing batch 123
    component: "questionWorker"
    batchId: 123
    provider: "openrouter"
```

### Production (JSON)
```json
{"level":30,"time":1748091769765,"component":"questionWorker","msg":"Processing batch 123","batchId":123,"provider":"openrouter"}
```

## Best Practices

1. **Use appropriate log levels** - debug for detailed info, info for general events, warn for issues, error for failures
2. **Include context** - use child loggers or context objects to provide relevant information
3. **Don't log sensitive data** - avoid logging passwords, tokens, or personal information
4. **Use structured logging** - prefer objects over string concatenation
5. **Handle errors gracefully** - always log errors with sufficient context for debugging

## Migration from console.log

Replace console.log/error calls with structured logging:

```typescript
// Before
console.log('Processing batch', batchId);
console.error('Error:', error);

// After
logger.info('Processing batch', { batchId });
logger.error(error, 'Batch processing failed', { batchId });
```
