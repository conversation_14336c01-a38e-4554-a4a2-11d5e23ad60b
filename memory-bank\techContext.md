# Tech Context

This document details the technologies, development setup, and dependencies used in the My Quiz App project.

## Technologies Used
- **Frontend:** Next.js, React, TypeScript, Tailwind CSS
- **Backend:** Node.js, Next.js API Routes
- **Database:** PostgreSQL
- **ORM:** Prisma
- **Authentication:** (Likely NextAuth.js or similar, needs confirmation)
- **AI Integration:** (Specific libraries/APIs used for translator/tutor, needs confirmation)

## Development Setup
- Node.js and npm/yarn installed.
- Docker for database setup (based on `docker-compose.yaml`).
- Environment variables for database connection and other configurations (`.env`).

## Technical Constraints
- Performance considerations for real-time quiz updates and analytics data fetching.
- Security requirements for user data, authentication, and analytics data access.

## Dependencies
- Review `package.json` for a comprehensive list of dependencies.
- New dependencies may be required for charting or data visualization libraries for the analytics dashboards.

## Tool Usage Patterns
- Use of Prisma CLI for database migrations and generating the Prisma Client.
- Use of Jest for testing (`jest.config.js`, `test/`).
- Development and testing of new API routes for analytics (including quiz attempt and answer logging).
- Development and testing of frontend components for displaying analytics data.
