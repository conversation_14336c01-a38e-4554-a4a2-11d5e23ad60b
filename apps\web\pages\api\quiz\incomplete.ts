import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getSession } from 'next-auth/react';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getSession({ req });

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { childId } = req.query;

  if (!childId) {
    return res.status(400).json({ message: 'Missing childId' });
  }

  try {
    const incompleteQuiz = await prisma.quizAttempt.findFirst({ // Corrected model name
      where: {
        childId: Number(childId),
        status: 'ACTIVE', // Only fetch quizzes with ACTIVE status
        endTime: null, // Quiz is incomplete if endTime is null
      },
      select: {
        id: true,
        // questionIds: true, // Removed non-existent field
        currentQuestionIndex: true,
        // quizType: true, // Removed non-existent field
        startTime: true,
        subject: {
          select: {
            name: true,
          },
        },
        unit: {
          select: {
            unitNumber: true,
          },
        },
      },
    });

    if (incompleteQuiz) {
      res.status(200).json({ incompleteQuiz });
    } else {
      res.status(200).json({ incompleteQuiz: null });
    }

  } catch (error) {
    console.error('Error fetching incomplete quiz:', error);
    res.status(500).json({ message: 'Error fetching incomplete quiz' });
  }
}
