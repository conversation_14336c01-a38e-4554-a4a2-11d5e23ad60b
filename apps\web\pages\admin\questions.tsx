import React, { useEffect, useState, useRef } from 'react';
import QuestionModal from '../../components/admin/QuestionModal';

interface Question {
  id: number;
  questionId: string;
  type: string;
  promptEn: string;
  promptZh: string;
  topic?: string;
  choices?: Choice[];
  answer?: Answer;
  explanation?: ExplanationText;
  subjectId: number; // Keep ID for internal use if needed
  yearId: number; // Keep ID for internal use if needed
  unitId: number; // Keep ID for internal use if needed
  status: 'DRAFT' | 'LIVE' | 'ARCHIVED'; // Add status field
  tpLevel?: number; // Tahap Penguasaan level (1-6)
  subject: { // Include related Subject data
    id: number;
    name: string;
  };
  year: { // Include related Year data
    id: number;
    yearNumber: number;
  };
}

interface Choice {
  id: number;
  key: string;
  textEn: string;
  textZh: string;
}

interface Answer {
  id: number;
  key?: string;
  textEn?: string;
  textZh?: string;
}

interface ExplanationText {
  id: number;
  textEn: string;
  textZh: string;
}


const AdminQuestionsPage: React.FC = () => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isAddQuestionModalOpen, setIsAddQuestionModalOpen] = useState(false);
  const [newQuestion, setNewQuestion] = useState({
    type: 'MULTIPLE_CHOICE', // Default type
    year: '',
    subject: '',
    unit: '',
    prompt: { en: '', zh: '' },
    choices: [{ key: 'A', en: '', zh: '' }],
    answer: { key: '', en: '', zh: '' },
    explanation: { en: '', zh: '' },
    topic: '',
  });

  // Filter states
  const [filterYear, setFilterYear] = useState('');
  const [filterSubject, setFilterSubject] = useState('');
  const [filterUnit, setFilterUnit] = useState('');
  const [filterQuestionType, setFilterQuestionType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterTpLevel, setFilterTpLevel] = useState('');

  // Filter options states
  const [years, setYears] = useState<{ id: number; yearNumber: number }[]>([]);
  const [subjects, setSubjects] = useState<{ id: number; name: string; yearId: number }[]>([]);
  const [units, setUnits] = useState<{ id: number; unitNumber: number; subjectId: number }[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadStatus, setUploadStatus] = useState<{ type: 'success' | 'error', message: string } | null>(null);
  const [previewId, setPreviewId] = useState<number | null>(null);

  // Fetch years, subjects, and units for filters
  useEffect(() => {
    const fetchSyllabusData = async () => {
      try {
        console.log('AdminQuestionsPage: Fetching syllabus data...');

        // Add a timeout to the fetch to prevent hanging indefinitely
        const fetchWithTimeout = async (url: string, options = {}, timeout = 10000) => {
          const controller = new AbortController();
          const { signal } = controller;

          const timeoutId = setTimeout(() => controller.abort(), timeout);

          try {
            const response = await fetch(url, { ...options, signal });
            clearTimeout(timeoutId);
            return response;
          } catch (error) {
            clearTimeout(timeoutId);
            throw error;
          }
        };

        const response = await fetchWithTimeout('/api/syllabus');

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Syllabus API returned error status:', response.status, errorText);
          throw new Error(`Failed to fetch syllabus data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Syllabus data received:', {
          yearCount: data.years?.length || 0,
          subjectCount: data.subjects?.length || 0,
          unitCount: data.units?.length || 0,
        });

        // Set state with fallbacks to empty arrays
        setYears(data.years || []);
        setSubjects(data.subjects || []);
        setUnits(data.units || []);

        // Log success
        console.log('AdminQuestionsPage: Successfully loaded syllabus data');
      } catch (err) {
        console.error('Error fetching syllabus data:', err);
        // Show error in UI but don't block the component from rendering
        setError(err instanceof Error ? err.message : 'Failed to load syllabus data');
      }
    };

    fetchSyllabusData();
  }, []);


  useEffect(() => {
    const fetchQuestions = async () => {
      setLoading(true); // Set loading to true before fetching
      setError(''); // Clear previous errors
      try {
        const queryParams = new URLSearchParams();
        if (filterYear) {
          queryParams.append('yearId', filterYear);
        }
        if (filterSubject) {
          queryParams.append('subjectId', filterSubject);
        }
        if (filterUnit) {
          queryParams.append('unitId', filterUnit);
        }
        if (filterQuestionType) {
          queryParams.append('type', filterQuestionType);
        }
        if (filterStatus) {
          queryParams.append('status', filterStatus);
        }
        if (filterTpLevel) {
          queryParams.append('tpLevel', filterTpLevel);
        }

        const response = await fetch(`/api/admin/questions?${queryParams.toString()}`);
        if (!response.ok) {
          throw new Error('Failed to fetch questions');
        }
        const data = await response.json();
        setQuestions(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchQuestions();
  }, [filterYear, filterSubject, filterUnit, filterQuestionType, filterStatus, filterTpLevel]);

  const handleAddQuestionClick = () => {
    setIsAddQuestionModalOpen(true);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const fileContent = await file.text();
      const questionsData = JSON.parse(fileContent);

      // Assuming the JSON file contains an array of questions
      if (!Array.isArray(questionsData)) {
        setUploadStatus({ type: 'error', message: 'Invalid JSON format. Expected an array of questions.' });
        return;
      }

      // Send the array of questions to the backend
      const response = await fetch('/api/admin/add-questions-batch', { // TODO: Create this new API endpoint
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(questionsData),
      });

      const result = await response.json();

      if (!response.ok) {
        setUploadStatus({
          type: 'error',
          message: result.message || 'Failed to upload questions.',
        });
      } else {
        setUploadStatus({ type: 'success', message: 'Questions uploaded successfully.' });
        // Refresh the question list
        const fetchQuestions = async () => {
          try {
            const queryParams = new URLSearchParams();
            if (filterYear) {
              queryParams.append('yearId', filterYear);
            }
            if (filterSubject) {
              queryParams.append('subjectId', filterSubject);
            }
            if (filterUnit) {
              queryParams.append('unitId', filterUnit);
            }
            if (filterQuestionType) {
              queryParams.append('type', filterQuestionType);
            }
            if (filterStatus) {
              queryParams.append('status', filterStatus);
            }
            if (filterTpLevel) {
              queryParams.append('tpLevel', filterTpLevel);
            }

            const response = await fetch(`/api/admin/questions?${queryParams.toString()}`);
            if (!response.ok) {
              throw new Error('Failed to fetch questions');
            }
            const data = await response.json();
            setQuestions(data);
          } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
          }
        };
        fetchQuestions();
      }
    } catch (err) {
      setUploadStatus({
        type: 'error',
        message: err instanceof Error ? err.message : 'An error occurred during file upload.',
      });
    } finally {
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleCloseModal = () => {
    setIsAddQuestionModalOpen(false);
    // Reset the new question state
    setNewQuestion({
      type: 'MULTIPLE_CHOICE',
      year: '',
      subject: '',
      unit: '',
      prompt: { en: '', zh: '' },
      choices: [{ key: 'A', en: '', zh: '' }],
      answer: { key: '', en: '', zh: '' },
      explanation: { en: '', zh: '' },
      topic: '',
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>, field: string, nestedField?: string, index?: number) => {
    if (nestedField) {
      if (index !== undefined) {
        // Handling changes in choices array
        const updatedChoices = [...newQuestion.choices];
        updatedChoices[index] = { ...updatedChoices[index], [nestedField]: e.target.value };
        setNewQuestion((prev) => ({
          ...prev,
          choices: updatedChoices,
        }));
      } else {
        // Handling changes in nested objects like prompt, answer, explanation
        setNewQuestion((prev) => ({
          ...prev,
          [field]: {
            ...((prev as any)[field]), // Cast to any to access nested property
            [nestedField]: e.target.value,
          },
        }));
      }
    } else {
      // Handling changes in top-level fields
      setNewQuestion((prev) => ({
        ...prev,
        [field]: e.target.value,
      }));
    }
  };

  const handleAddChoice = () => {
    const newKey = String.fromCharCode(65 + newQuestion.choices.length); // Generate next letter key (A, B, C...)
    setNewQuestion((prev) => ({
      ...prev,
      choices: [...prev.choices, { key: newKey, en: '', zh: '' }],
    }));
  };

  const handleRemoveChoice = (index: number) => {
    setNewQuestion((prev) => ({
      ...prev,
      choices: prev.choices.filter((_, i) => i !== index),
    }));
  };


  const handleSaveQuestion = async () => {
    try {
      const response = await fetch('/api/admin/add-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newQuestion),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('Failed to add question:', result.message);
        alert(`Failed to add question: ${result.message}`);
      } else {
        console.log('Question added:', result);
        handleCloseModal();
        // Re-fetch questions to update the list
        const fetchQuestions = async () => {
          try {
            const queryParams = new URLSearchParams();
            if (filterYear) {
              queryParams.append('yearId', filterYear);
            }
            if (filterSubject) {
              queryParams.append('subjectId', filterSubject);
            }
            if (filterUnit) {
              queryParams.append('unitId', filterUnit);
            }
            if (filterQuestionType) {
              queryParams.append('type', filterQuestionType);
            }
            if (filterStatus) {
              queryParams.append('status', filterStatus);
            }
            if (filterTpLevel) {
              queryParams.append('tpLevel', filterTpLevel);
            }

            const response = await fetch(`/api/admin/questions?${queryParams.toString()}`);
            if (!response.ok) {
              throw new Error('Failed to fetch questions');
            }
            const data = await response.json();
            setQuestions(data);
          } catch (err) {
            setError(err instanceof Error ? err.message : 'An error occurred');
          }
        };
        fetchQuestions();
      }
    } catch (error) {
      console.error('Error saving question:', error);
      alert('An error occurred while saving the question.');
    }
  };


  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-md">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-lg text-gray-700">Loading questions...</p>
        </div>
      </div>
    );
  }

  // Show error as a banner instead of replacing the entire UI
  // This allows users to still use the page even if there was an error

  return (
    <div className="container mx-auto py-8">
      {/* Error Banner */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <div className="text-red-600 mr-3">⚠️</div>
            <div>
              <h3 className="font-medium text-red-800">Error</h3>
              <p className="text-red-700 mt-1">{error}</p>
              <button
                onClick={() => setError('')}
                className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Available Questions</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleAddQuestionClick}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold"
          >
            + Add Question
          </button>
          <input
            type="file"
            accept=".json"
            onChange={handleFileUpload}
            ref={fileInputRef}
            className="hidden"
            id="question-upload"
          />
          <label
            htmlFor="question-upload"
            className="cursor-pointer bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center"
          >
            <span className="mr-2">⬆️</span>
            Upload JSON
          </label>
        </div>
      </div>

      {/* Filter Section */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold mb-3">Filter Questions</h2>
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Year</label>
            <select
              className="mt-1 block w-full border rounded px-3 py-2"
              value={filterYear}
              onChange={(e) => setFilterYear(e.target.value)}
            >
              <option value="">All Years</option>
              {years.length > 0 ? (
                years.map((year) => (
                  <option key={year.id} value={year.id}>{year.yearNumber}</option>
                ))
              ) : (
                <option disabled>No years available</option>
              )}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Subject</label>
            <select
              className="mt-1 block w-full border rounded px-3 py-2"
              value={filterSubject}
              onChange={(e) => {
                setFilterSubject(e.target.value);
                setFilterUnit(''); // Reset unit when subject changes
              }}
            >
              <option value="">All Subjects</option>
              {subjects.length > 0 ? (
                subjects
                  .filter(subject => filterYear === '' || (subject as any).yearId === parseInt(filterYear))
                  .map((subject) => (
                    <option key={subject.id} value={subject.id}>{subject.name}</option>
                  ))
              ) : (
                <option disabled>No subjects available</option>
              )}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Unit</label>
            <select
              className="mt-1 block w-full border rounded px-3 py-2"
              value={filterUnit}
              onChange={(e) => setFilterUnit(e.target.value)}
              disabled={!filterSubject} // Disable if no subject is selected
            >
              <option value="">All Units</option>
              {units.length > 0 ? (
                units
                  .filter(unit => filterSubject === '' || unit.subjectId === parseInt(filterSubject))
                  .map((unit) => (
                    <option key={unit.id} value={unit.id}>{unit.unitNumber}</option>
                  ))
              ) : (
                <option disabled>No units available</option>
              )}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Question Type</label>
            <select
              className="mt-1 block w-full border rounded px-3 py-2"
              value={filterQuestionType}
              onChange={(e) => setFilterQuestionType(e.target.value)}
            >
              <option value="">All Types</option>
              <option value="MULTIPLE_CHOICE">Multiple Choice</option>
              <option value="MULTIPLE_CHOICE_IMAGE">Multiple Choice with Image</option>
              <option value="PICTURE_PROMPT">Picture Prompt</option>
              <option value="FILL_IN_THE_BLANK">Fill in the Blank</option>
              <option value="TRUE_FALSE">True/False</option>
              <option value="SHORT_ANSWER">Short Answer</option>
              <option value="LONG_ANSWER">Long Answer</option>
              <option value="MATCHING">Matching</option>
              <option value="SEQUENCING">Sequencing</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Status</label>
            <select
              className="mt-1 block w-full border rounded px-3 py-2"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <option value="">All Statuses</option>
              <option value="DRAFT">Draft</option>
              <option value="LIVE">Live</option>
              <option value="ARCHIVED">Archived</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">TP Level</label>
            <select
              className="mt-1 block w-full border rounded px-3 py-2"
              value={filterTpLevel}
              onChange={(e) => setFilterTpLevel(e.target.value)}
            >
              <option value="">All Levels</option>
              <option value="1">Level 1 (Recall)</option>
              <option value="2">Level 2 (Understand)</option>
              <option value="3">Level 3 (Apply)</option>
              <option value="4">Level 4 (Analyze)</option>
              <option value="5">Level 5 (Evaluate)</option>
              <option value="6">Level 6 (Create)</option>
            </select>
          </div>
        </div>
      </div>


      {uploadStatus && (
        <div className={`mb-6 p-4 rounded-md ${
          uploadStatus.type === 'success'
            ? 'bg-green-50 text-green-800 border border-green-200'
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          {uploadStatus.message}
        </div>
      )}

      {questions.length === 0 ? (
        <div className="bg-white p-8 rounded-lg shadow-sm text-center">
          <div className="text-gray-400 text-5xl mb-4">📚</div>
          <h3 className="text-xl font-medium text-gray-700 mb-2">No Questions Available</h3>
          <p className="text-gray-500 mb-4">
            {error ? 'There was an error loading questions.' : 'Try adjusting your filters or add new questions.'}
          </p>
          <button
            onClick={handleAddQuestionClick}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold"
          >
            + Add Your First Question
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Questions ({questions.length})</h2>
          </div>
          <ul className="space-y-4">
            {questions.map((question) => (
              <li
                key={question.id}
                className="border border-gray-100 p-4 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => setPreviewId(question.id)}
              >
                <div className="flex justify-between">
                  <h3 className="text-lg font-semibold">{question.questionId} - {question.type}</h3>
                  <div className="flex space-x-2">
                    <span className={`text-sm px-2 py-1 rounded-full ${
                      question.status === 'DRAFT'
                        ? 'bg-yellow-100 text-yellow-800'
                        : question.status === 'LIVE'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}>
                      {question.status}
                    </span>
                    <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {question.subject?.name || 'Unknown Subject'}
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div>
                    <p className="text-sm text-gray-500">
                      Year: {question.year?.yearNumber || 'Unknown'},
                      Unit: {question.unitId || 'N/A'}
                    </p>
                    <p className="text-sm text-gray-500">Topic: {question.topic || 'N/A'}</p>
                    <p className="text-sm text-gray-500 mb-2">
                      TP Level: <span className="font-medium">{question.tpLevel || 'N/A'}</span>
                    </p>
                    <div className="bg-gray-50 p-2 rounded mb-2">
                      <p className="text-gray-800 text-sm">
                        <span className="font-medium">EN:</span> {question.promptEn}
                      </p>
                    </div>
                    <div className="bg-gray-50 p-2 rounded">
                      <p className="text-gray-800 text-sm">
                        <span className="font-medium">ZH:</span> {question.promptZh}
                      </p>
                    </div>
                  </div>
                  <div>
                    {question.choices && question.choices.length > 0 && (
                      <div className="mb-3">
                        <h4 className="font-medium text-sm text-gray-700 mb-1">Choices:</h4>
                        <ul className="space-y-1">
                          {question.choices.map((choice) => (
                            <li key={choice.id} className="text-sm">
                              <span className="font-medium">{choice.key}:</span> {choice.textEn} ({choice.textZh})
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {question.answer && (
                      <div className="mb-3">
                        <h4 className="font-medium text-sm text-gray-700 mb-1">Answer:</h4>
                        <p className="text-sm">
                          {question.answer?.key && <span className="font-medium">Key: {question.answer.key}</span>}
                          {question.answer?.textEn && <span className="block">EN: {question.answer.textEn}</span>}
                          {question.answer?.textZh && <span className="block">ZH: {question.answer.textZh}</span>}
                        </p>
                      </div>
                    )}
                    {question.explanation && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 mb-1">Explanation:</h4>
                        <p className="text-sm">EN: {question.explanation.textEn}</p>
                        <p className="text-sm">ZH: {question.explanation.textZh}</p>
                      </div>
                    )}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Question Preview Modal */}
      {previewId && (
        <QuestionModal id={previewId} onClose={() => setPreviewId(null)} />
      )}

      {/* Add Question Modal */}
      {isAddQuestionModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto p-6">
            <h2 className="text-xl font-bold mb-4">Add New Question</h2>

            <div className="space-y-4">
              {/* Type, Year, Subject, Unit */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Type</label>
                  <select
                    className="mt-1 block w-full border rounded px-3 py-2"
                    value={newQuestion.type}
                    onChange={(e) => handleInputChange(e, 'type')}
                  >
                    <option value="MULTIPLE_CHOICE">Multiple Choice</option>
                    <option value="MULTIPLE_CHOICE_IMAGE">Multiple Choice with Image</option>
                    <option value="PICTURE_PROMPT">Picture Prompt</option>
                    <option value="FILL_IN_THE_BLANK">Fill in the Blank</option>
                    <option value="TRUE_FALSE">True/False</option>
                    <option value="SHORT_ANSWER">Short Answer</option>
                    <option value="LONG_ANSWER">Long Answer</option>
                    <option value="MATCHING">Matching</option>
                    <option value="SEQUENCING">Sequencing</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Year</label>
                  <input
                    type="number"
                    className="mt-1 block w-full border rounded px-3 py-2"
                    value={newQuestion.year}
                    onChange={(e) => handleInputChange(e, 'year')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Subject</label>
                  <input
                    type="text"
                    className="mt-1 block w-full border rounded px-3 py-2"
                    value={newQuestion.subject}
                    onChange={(e) => handleInputChange(e, 'subject')}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Unit</label>
                  <input
                    type="number"
                    className="mt-1 block w-full border rounded px-3 py-2"
                    value={newQuestion.unit}
                    onChange={(e) => handleInputChange(e, 'unit')}
                  />
                </div>
              </div>

              {/* Prompt */}
              <div>
                <label className="block text-sm font-medium text-gray-700">Prompt (English)</label>
                <textarea
                  className="mt-1 block w-full border rounded px-3 py-2"
                  rows={2}
                  value={newQuestion.prompt.en}
                  onChange={(e) => handleInputChange(e, 'prompt', 'en')}
                ></textarea>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Prompt (Chinese)</label>
                <textarea
                  className="mt-1 block w-full border rounded px-3 py-2"
                  rows={2}
                  value={newQuestion.prompt.zh}
                  onChange={(e) => handleInputChange(e, 'prompt', 'zh')}
                ></textarea>
              </div>

              {/* Choices (for Multiple Choice) */}
              {newQuestion.type === 'MULTIPLE_CHOICE' && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Choices</h3>
                  {newQuestion.choices.map((choice, index) => (
                    <div key={index} className="grid grid-cols-4 gap-4 mb-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Key</label>
                        <input
                          type="text"
                          className="mt-1 block w-full border rounded px-3 py-2"
                          value={choice.key}
                          onChange={(e) => handleInputChange(e, 'choices', 'key', index)}
                        />
                      </div>
                      <div className="col-span-1">
                        <label className="block text-sm font-medium text-gray-700">English Text</label>
                        <input
                          type="text"
                          className="mt-1 block w-full border rounded px-3 py-2"
                          value={choice.en}
                          onChange={(e) => handleInputChange(e, 'choices', 'en', index)}
                        />
                      </div>
                       <div className="col-span-1">
                        <label className="block text-sm font-medium text-gray-700">Chinese Text</label>
                        <input
                          type="text"
                          className="mt-1 block w-full border rounded px-3 py-2"
                          value={choice.zh}
                          onChange={(e) => handleInputChange(e, 'choices', 'zh', index)}
                        />
                      </div>
                      <div className="flex items-end">
                        {index > 0 && (
                          <button
                            onClick={() => handleRemoveChoice(index)}
                            className="bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                  <button
                    onClick={handleAddChoice}
                    className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 text-sm"
                  >
                    + Add Choice
                  </button>
                </div>
              )}

              {/* Answer */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Answer</h3>
                 {newQuestion.type === 'MULTIPLE_CHOICE' ? (
                   <div>
                     <label className="block text-sm font-medium text-gray-700">Correct Choice Key (e.g., A)</label>
                     <input
                       type="text"
                       className="mt-1 block w-full border rounded px-3 py-2"
                       value={newQuestion.answer.key}
                       onChange={(e) => handleInputChange(e, 'answer', 'key')}
                     />
                   </div>
                 ) : (
                   <div className="grid grid-cols-2 gap-4">
                     <div>
                       <label className="block text-sm font-medium text-gray-700">Answer Text (English)</label>
                       <textarea
                         className="mt-1 block w-full border rounded px-3 py-2"
                         rows={2}
                         value={newQuestion.answer.en}
                         onChange={(e) => handleInputChange(e, 'answer', 'en')}
                       ></textarea>
                     </div>
                     <div>
                       <label className="block text-sm font-medium text-gray-700">Answer Text (Chinese)</label>
                       <textarea
                         className="mt-1 block w-full border rounded px-3 py-2"
                         rows={2}
                         value={newQuestion.answer.zh}
                         onChange={(e) => handleInputChange(e, 'answer', 'zh')}
                       ></textarea>
                     </div>
                   </div>
                 )}
              </div>

              {/* Explanation */}
              <div>
                <h3 className="text-lg font-semibold mb-2">Explanation</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Explanation Text (English)</label>
                    <textarea
                      className="mt-1 block w-full border rounded px-3 py-2"
                      rows={2}
                      value={newQuestion.explanation.en}
                      onChange={(e) => handleInputChange(e, 'explanation', 'en')}
                    ></textarea>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Explanation Text (Chinese)</label>
                    <textarea
                      className="mt-1 block w-full border rounded px-3 py-2"
                      rows={2}
                      value={newQuestion.explanation.zh}
                      onChange={(e) => handleInputChange(e, 'explanation', 'zh')}
                    ></textarea>
                  </div>
                </div>
              </div>

              {/* Topic */}
              <div>
                <label className="block text-sm font-medium text-gray-700">Topic</label>
                <input
                  type="text"
                  className="mt-1 block w-full border rounded px-3 py-2"
                  value={newQuestion.topic}
                  onChange={(e) => handleInputChange(e, 'topic')}
                />
              </div>

            </div>

            <div className="flex justify-end space-x-4 mt-6">
              <button
                onClick={handleCloseModal}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 font-semibold"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveQuestion}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold"
              >
                Save Question
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminQuestionsPage;
