# Analytics API Endpoints

This document provides documentation for the analytics-related API endpoints in the My Quiz App application.

## Translation Analytics

### Get Translation Analytics

Retrieves translation analytics data for a child.

- **URL**: `/api/analytics/translations`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None (but typically used by PARENT or ADMIN)

#### Request Parameters

| Parameter  | Type   | Required | Description                                                |
|------------|--------|----------|------------------------------------------------------------|
| childId    | string | No       | Filter analytics for a specific child                      |
| timePeriod | string | No       | Filter by time period (e.g., "week", "month", "year")      |

#### Response

```json
{
  "translationCount": 42,
  "commonWords": [
    {
      "word": "你好",
      "count": 5
    },
    {
      "word": "谢谢",
      "count": 3
    }
  ]
}
```

## Knowledge Analytics

### Get Knowledge Analytics

Retrieves knowledge analytics data for a child.

- **URL**: `/api/analytics/knowledge`
- **Method**: `GET`
- **Authentication Required**: No
- **Role Required**: None (but typically used by PARENT or ADMIN)

#### Request Parameters

| Parameter  | Type   | Required | Description                                                |
|------------|--------|----------|------------------------------------------------------------|
| childId    | string | No       | Filter analytics for a specific child                      |
| timePeriod | string | No       | Filter by time period (e.g., "week", "month", "year")      |
| subjectId  | string | No       | Filter by subject ID                                       |
| unitId     | string | No       | Filter by unit ID                                          |

#### Response

```json
{
  "attemptCount": 15,
  "subjectAttemptCounts": [
    {
      "subjectId": 1,
      "subjectName": "Mathematics",
      "count": 10
    },
    {
      "subjectId": 2,
      "subjectName": "Science",
      "count": 5
    }
  ],
  "unitAttemptCounts": [
    {
      "unitId": 1,
      "unitName": "Addition",
      "count": 5
    },
    {
      "unitId": 2,
      "unitName": "Subtraction",
      "count": 5
    }
  ],
  "subjectSuccessRates": [
    {
      "subjectId": 1,
      "subjectName": "Mathematics",
      "successRate": 0.8
    },
    {
      "subjectId": 2,
      "subjectName": "Science",
      "successRate": 0.6
    }
  ],
  "unitSuccessRates": [
    {
      "unitId": 1,
      "unitName": "Addition",
      "successRate": 0.9
    },
    {
      "unitId": 2,
      "unitName": "Subtraction",
      "successRate": 0.7
    }
  ],
  "tpLevelSuccessRates": [
    {
      "tpLevel": 1,
      "successRate": 0.95
    },
    {
      "tpLevel": 2,
      "successRate": 0.85
    }
  ],
  "studentTpLevel": {
    "subjectId": 1,
    "unitId": 1,
    "tpLevel": 3,
    "confidence": 0.75
  }
}
```
