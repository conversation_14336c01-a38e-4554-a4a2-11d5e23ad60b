import { renderHook, act } from '@testing-library/react-hooks';
import { QuizV2Provider, useQuizV2 } from '../components/quizV2/QuizV2Provider';
import { Question } from '../types/quiz';
import useAnswerSubmit from '../hooks/useAnswerSubmit';

// Mock the useAnswerSubmit hook
jest.mock('../hooks/useAnswerSubmit', () => ({
  __esModule: true,
  default: jest.fn(),
}));

// Mock the next/router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

describe('QuizV2 Review Queue', () => {
  // Sample questions for testing
  const mockQuestions: Question[] = [
    {
      id: 1,
      questionId: 'q1',
      type: 'MULTIPLE_CHOICE',
      promptEn: 'What is 2+2?',
      promptZh: '2+2等于几?',
      originalLanguage: 'ZH',
      choices: [
        { key: 'A', textEn: '3', textZh: '3' },
        { key: 'B', textEn: '4', textZh: '4' },
        { key: 'C', textEn: '5', textZh: '5' },
      ],
      answer: 'B',
      explanation: { en: 'The sum of 2 and 2 is 4', zh: '2加2等于4' },
      topic: 'Math',
      subject: { name: 'Mathematics' },
      unit: { unitNumber: 1, topicEn: 'Numbers', topicZh: '数字' },
    },
    {
      id: 2,
      questionId: 'q2',
      type: 'MULTIPLE_CHOICE',
      promptEn: 'What is the capital of France?',
      promptZh: '法国的首都是什么?',
      originalLanguage: 'ZH',
      choices: [
        { key: 'A', textEn: 'London', textZh: '伦敦' },
        { key: 'B', textEn: 'Paris', textZh: '巴黎' },
        { key: 'C', textEn: 'Berlin', textZh: '柏林' },
      ],
      answer: 'B',
      explanation: { en: 'Paris is the capital of France', zh: '巴黎是法国的首都' },
      topic: 'Geography',
      subject: { name: 'Social Studies' },
      unit: { unitNumber: 2, topicEn: 'Countries', topicZh: '国家' },
    },
    {
      id: 3,
      questionId: 'q3',
      type: 'MULTIPLE_CHOICE',
      promptEn: 'What is H2O?',
      promptZh: 'H2O是什么?',
      originalLanguage: 'ZH',
      choices: [
        { key: 'A', textEn: 'Water', textZh: '水' },
        { key: 'B', textEn: 'Air', textZh: '空气' },
        { key: 'C', textEn: 'Fire', textZh: '火' },
      ],
      answer: 'A',
      explanation: { en: 'H2O is the chemical formula for water', zh: 'H2O是水的化学式' },
      topic: 'Science',
      subject: { name: 'Science' },
      unit: { unitNumber: 3, topicEn: 'Elements', topicZh: '元素' },
    },
  ];

  // Mock quiz attempt
  const mockAttempt = {
    quizAttempt: {
      id: 123,
      currentQuestionIndex: 0,
      quizType: 'MASTERY',
      metadata: {
        configSnapshot: {
          allowTranslate: true,
          allowHints: true,
          allowAiTutor: true,
          reviewMissedQuestions: true,
        },
      },
    },
    questions: mockQuestions,
    isCompleted: false,
  };

  // Mock submit function
  const mockSubmit = jest.fn();
  const mockFetchAiTutorExplanation = jest.fn();

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup useAnswerSubmit mock
    (useAnswerSubmit as jest.Mock).mockReturnValue({
      submit: mockSubmit,
      submitting: false,
      fetchAiTutorExplanation: mockFetchAiTutorExplanation,
    });
  });

  it('should add incorrect answers to the review queue', async () => {
    // Mock the submit function to return incorrect for first question
    mockSubmit.mockResolvedValueOnce({
      correct: false,
      last: false,
    });

    // Render the hook with the provider
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QuizV2Provider attempt={mockAttempt}>{children}</QuizV2Provider>
    );

    const { result, waitForNextUpdate } = renderHook(() => useQuizV2(), { wrapper });

    // Check initial state
    expect(result.current.phase).toBe('PRIMARY');
    expect(result.current.reviewQueue).toHaveLength(0);
    expect(result.current.firstTryCorrect).toEqual({});

    // Answer the first question incorrectly
    await act(async () => {
      await result.current.checkAnswer(mockQuestions[0], 'A');
      await waitForNextUpdate();
    });

    // Check that the question was added to the review queue
    expect(result.current.reviewQueue).toHaveLength(1);
    expect(result.current.reviewQueue[0].id).toBe(1);
    expect(result.current.firstTryCorrect[1]).toBe(false);
  });

  it('should transition to REVIEW phase after completing PRIMARY phase with incorrect answers', async () => {
    // Mock the submit function to return correct for last question
    mockSubmit
      .mockResolvedValueOnce({ correct: true, last: false }) // First question correct
      .mockResolvedValueOnce({ correct: false, last: false }) // Second question incorrect
      .mockResolvedValueOnce({ correct: true, last: true }); // Third question correct (last)

    // Render the hook with the provider
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QuizV2Provider attempt={mockAttempt}>{children}</QuizV2Provider>
    );

    const { result, waitForNextUpdate } = renderHook(() => useQuizV2(), { wrapper });

    // Answer first question correctly
    await act(async () => {
      await result.current.checkAnswer(mockQuestions[0], 'B');
      await waitForNextUpdate();
    });

    // Move to next question
    act(() => {
      result.current.moveToNextQuestion();
    });

    // Answer second question incorrectly
    await act(async () => {
      await result.current.checkAnswer(mockQuestions[1], 'A');
      await waitForNextUpdate();
    });

    // Check that the question was added to the review queue
    expect(result.current.reviewQueue).toHaveLength(1);
    expect(result.current.reviewQueue[0].id).toBe(2);

    // Move to next question
    act(() => {
      result.current.moveToNextQuestion();
    });

    // Answer third question correctly
    await act(async () => {
      await result.current.checkAnswer(mockQuestions[2], 'A');
      await waitForNextUpdate();
    });

    // Move to next question (should transition to REVIEW phase)
    act(() => {
      result.current.moveToNextQuestion();
    });

    // Check that we transitioned to REVIEW phase
    expect(result.current.phase).toBe('REVIEW');
    expect(result.current.currentIndex).toBe(0);
  });

  it('should allow moving to the next question after answering incorrectly', async () => {
    // Mock the submit function to return incorrect for first question
    mockSubmit.mockResolvedValueOnce({
      correct: false,
      last: false,
    });

    // Render the hook with the provider
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QuizV2Provider attempt={mockAttempt}>{children}</QuizV2Provider>
    );

    const { result, waitForNextUpdate } = renderHook(() => useQuizV2(), { wrapper });

    // Answer the first question incorrectly
    await act(async () => {
      await result.current.checkAnswer(mockQuestions[0], 'A');
      await waitForNextUpdate();
    });

    // Check that the question was added to the review queue
    expect(result.current.reviewQueue).toHaveLength(1);
    expect(result.current.firstTryCorrect[1]).toBe(false);

    // Current index should still be 0
    expect(result.current.currentIndex).toBe(0);

    // Move to next question despite incorrect answer
    act(() => {
      result.current.moveToNextQuestion();
    });

    // Current index should now be 1
    expect(result.current.currentIndex).toBe(1);
  });
});
