/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/student-dashboard";
exports.ids = ["pages/student-dashboard"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\student-dashboard.tsx */ \"(pages-dir-node)/./pages/student-dashboard.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/student-dashboard\",\n        pathname: \"/student-dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_student_dashboard_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./components/TpPill.tsx":
/*!*******************************!*\
  !*** ./components/TpPill.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TpPill: () => (/* binding */ TpPill)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst TpPill = ({ tp, confidence })=>{\n    const colour = confidence === 'secure' ? 'bg-green-500' : confidence === 'emerging' ? 'bg-amber-500' : 'bg-gray-400';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: `${colour} text-white text-[10px] sm:text-xs px-2 py-[2px] rounded-full whitespace-nowrap`,\n        title: tp ? `Mastery TP${tp} • ${confidence}` : 'Mastery not established yet',\n        children: [\n            \"TP\\xa0\",\n            tp || '—'\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\TpPill.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL2NvbXBvbmVudHMvVHBQaWxsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBS08sTUFBTUEsU0FBUyxDQUFDLEVBQUVDLEVBQUUsRUFBRUMsVUFBVSxFQUFTO0lBQzlDLE1BQU1DLFNBQ0pELGVBQWUsV0FDWCxpQkFDQUEsZUFBZSxhQUNmLGlCQUNBO0lBRU4scUJBQ0UsOERBQUNFO1FBQ0NDLFdBQVcsR0FBR0YsT0FBTywrRUFBK0UsQ0FBQztRQUNyR0csT0FDRUwsS0FDSSxDQUFDLFVBQVUsRUFBRUEsR0FBRyxHQUFHLEVBQUVDLFlBQVksR0FDakM7O1lBRVA7WUFDVUQsTUFBTTs7Ozs7OztBQUdyQixFQUFFIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXEdpdEh1YlxcbXktcXVpei1hcHBcXGFwcHNcXHdlYlxcY29tcG9uZW50c1xcVHBQaWxsLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbnRlcmZhY2UgUHJvcHMge1xyXG4gIHRwOiBudW1iZXI7ICAgICAgICAgICAgICAgICAgICAvLyAwIHdoZW4gdW5rbm93blxyXG4gIGNvbmZpZGVuY2U6ICdzZWN1cmUnIHwgJ2VtZXJnaW5nJyB8ICdsb3cnO1xyXG59XHJcblxyXG5leHBvcnQgY29uc3QgVHBQaWxsID0gKHsgdHAsIGNvbmZpZGVuY2UgfTogUHJvcHMpID0+IHtcclxuICBjb25zdCBjb2xvdXIgPVxyXG4gICAgY29uZmlkZW5jZSA9PT0gJ3NlY3VyZSdcclxuICAgICAgPyAnYmctZ3JlZW4tNTAwJ1xyXG4gICAgICA6IGNvbmZpZGVuY2UgPT09ICdlbWVyZ2luZydcclxuICAgICAgPyAnYmctYW1iZXItNTAwJ1xyXG4gICAgICA6ICdiZy1ncmF5LTQwMCc7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8c3BhblxyXG4gICAgICBjbGFzc05hbWU9e2Ake2NvbG91cn0gdGV4dC13aGl0ZSB0ZXh0LVsxMHB4XSBzbTp0ZXh0LXhzIHB4LTIgcHktWzJweF0gcm91bmRlZC1mdWxsIHdoaXRlc3BhY2Utbm93cmFwYH1cclxuICAgICAgdGl0bGU9e1xyXG4gICAgICAgIHRwXHJcbiAgICAgICAgICA/IGBNYXN0ZXJ5IFRQJHt0cH0g4oCiICR7Y29uZmlkZW5jZX1gXHJcbiAgICAgICAgICA6ICdNYXN0ZXJ5IG5vdCBlc3RhYmxpc2hlZCB5ZXQnXHJcbiAgICAgIH1cclxuICAgID5cclxuICAgICAgVFAmbmJzcDt7dHAgfHwgJ+KAlCd9XHJcbiAgICA8L3NwYW4+XHJcbiAgKTtcclxufTtcclxuIl0sIm5hbWVzIjpbIlRwUGlsbCIsInRwIiwiY29uZmlkZW5jZSIsImNvbG91ciIsInNwYW4iLCJjbGFzc05hbWUiLCJ0aXRsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/TpPill.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useMastery.ts":
/*!*****************************!*\
  !*** ./hooks/useMastery.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMastery: () => (/* binding */ useMastery)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nfunction useMastery(studentId, unitId) {\n    const enabled = \"on\" === 'on';\n    // Use a cache key that includes both studentId and unitId\n    const cacheKey = enabled ? `mastery-${studentId}-${unitId}` : null;\n    const { data } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(cacheKey, {\n        \"useMastery.useSWR\": ()=>{\n            const url = `/api/mastery/unit/${unitId}`;\n            return fetch(url, {\n                headers: {\n                    'x-student-id': String(studentId)\n                }\n            }).then({\n                \"useMastery.useSWR\": (r)=>r.json()\n            }[\"useMastery.useSWR\"]);\n        }\n    }[\"useMastery.useSWR\"], {\n        revalidateOnFocus: false,\n        dedupingInterval: 10000\n    });\n    return data;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useMastery.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/DifficultyModal.tsx":
/*!***********************************!*\
  !*** ./pages/DifficultyModal.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DifficultyModal: () => (/* binding */ DifficultyModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=XIcon!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=XIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n\n\n\nconst DifficultyModal = ({ onSelect, onClose, unit })=>{\n    const difficulties = [\n        {\n            label: 'Very Easy',\n            value: 'very-easy',\n            tpLevels: [\n                1\n            ],\n            description: 'TP1 level questions'\n        },\n        {\n            label: 'Easy',\n            value: 'easy',\n            tpLevels: [\n                2\n            ],\n            description: 'TP2 level questions'\n        },\n        {\n            label: 'Medium',\n            value: 'medium',\n            tpLevels: [\n                3,\n                4\n            ],\n            description: 'TP3-TP4 level questions'\n        },\n        {\n            label: 'Expert',\n            value: 'expert',\n            tpLevels: [\n                5,\n                6\n            ],\n            description: 'TP5-TP6 level questions'\n        },\n        {\n            label: 'Mix It',\n            value: 'mix',\n            tpLevels: [\n                1,\n                2,\n                3,\n                4,\n                5,\n                6\n            ],\n            description: 'Questions from all TP levels'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl max-w-md w-full p-6 shadow-xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-[#0D0D0D]\",\n                                    children: \"Select Difficulty\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        unit.subject,\n                                        \" - \",\n                                        unit.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_2__.XIcon, {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-4 mb-6\",\n                    children: difficulties.map((difficulty)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onSelect(difficulty),\n                            className: \"p-4 rounded-lg font-bold text-left transition-all   hover:bg-[#04B2D9] hover:text-white   bg-white border-2 border-[#04B2D9] text-[#04B2D9]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: difficulty.label\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs font-normal mt-1\",\n                                        children: difficulty.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, undefined)\n                        }, difficulty.value, false, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-sm text-gray-600\",\n                    children: \"Choose a difficulty level to begin practice\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\DifficultyModal.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/DifficultyModal.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n// pages/_app.tsx\n\n\n\nfunction MyApp({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSxpQkFBaUI7O0FBQ2E7QUFFbUI7QUFFakQsU0FBU0MsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUdELFdBQVcsRUFBWTtJQUMxRSxxQkFDRSw4REFBQ0gsNERBQWVBO1FBQUNJLFNBQVNBO2tCQUN4Qiw0RUFBQ0Y7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QjtBQUVBLGlFQUFlRixLQUFLQSxFQUFBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXEdpdEh1YlxcbXktcXVpei1hcHBcXGFwcHNcXHdlYlxccGFnZXNcXF9hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhZ2VzL19hcHAudHN4XHJcbmltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcclxuXHJcbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHM6IHsgc2Vzc2lvbiwgLi4ucGFnZVByb3BzIH0gfTogQXBwUHJvcHMpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cclxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxyXG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XHJcbiAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBNeUFwcFxyXG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJzZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n            @font-face {\n              font-family: 'Chinese Fallback';\n              src: local('SimSun'), local('Microsoft YaHei'), local('STHeiti');\n              unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF;\n            }\n          `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/student-dashboard.tsx":
/*!*************************************!*\
  !*** ./pages/student-dashboard.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/../../node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_TpPill__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/TpPill */ \"(pages-dir-node)/./components/TpPill.tsx\");\n/* harmony import */ var _hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useMastery */ \"(pages-dir-node)/./hooks/useMastery.ts\");\n/* harmony import */ var _DifficultyModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./DifficultyModal */ \"(pages-dir-node)/./pages/DifficultyModal.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__]);\n_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n// Map subject names to icons and colors\nconst subjectIcons = {\n    'Math': '🔢',\n    'Science': '🧪',\n    'Chinese': '🀄',\n    'English': '📚',\n    'Malay': '🇲🇾',\n    'History': '📜',\n    'Geography': '🌍',\n    'Art': '🎨',\n    'Music': '🎵',\n    'Physical Education': '⚽'\n};\nconst subjectColors = {\n    'Math': 'bg-blue-500',\n    'Science': 'bg-green-500',\n    'Chinese': 'bg-red-500',\n    'English': 'bg-purple-500',\n    'Malay': 'bg-yellow-500',\n    'History': 'bg-amber-500',\n    'Geography': 'bg-emerald-500',\n    'Art': 'bg-pink-500',\n    'Music': 'bg-indigo-500',\n    'Physical Education': 'bg-orange-500'\n};\n// This is v2 of the dashboard.\nconst StudentDashboard = ()=>{\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [showStreakModal, setShowStreakModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subjects, setSubjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [units, setUnits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [childData, setChildData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDifficultyModal, setShowDifficultyModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUnit, setSelectedUnit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Handle error messages from query parameters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            const { error: queryError } = router.query;\n            if (queryError === 'unauthorized-quiz-access') {\n                setError('You can only view your own quizzes. Please select a subject and start a new quiz.');\n                // Clear the error from URL after displaying\n                router.replace('/student-dashboard', undefined, {\n                    shallow: true\n                });\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        router.query\n    ]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        streak: 5,\n        xp: 230,\n        gems: 45,\n        hearts: 5\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            // Redirect to login if not authenticated\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        router\n    ]);\n    // Fetch child data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (status === 'authenticated' && session?.user) {\n                setLoading(true);\n                // Fetch the child's data\n                fetch('/api/child-data').then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch child data');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setChildData(data);\n                        // Now fetch subjects based on the child's year\n                        return fetch(`/api/subjects-by-year?yearNumber=${encodeURIComponent(data.year)}`);\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch subjects');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        // Add icon and color to each subject\n                        const enhancedSubjects = data.map({\n                            \"StudentDashboard.useEffect.enhancedSubjects\": (subject)=>({\n                                    ...subject,\n                                    icon: subjectIcons[subject.name] || '📚',\n                                    color: subjectColors[subject.name] || 'bg-gray-500',\n                                    completed: 0,\n                                    total: subject.unitCount || 0,\n                                    unlocked: true\n                                })\n                        }[\"StudentDashboard.useEffect.enhancedSubjects\"]);\n                        setSubjects(enhancedSubjects);\n                        // Try to get the saved subject selection from localStorage\n                        try {\n                            const savedSubjectId = localStorage.getItem('selectedSubjectId');\n                            if (savedSubjectId !== null) {\n                                const parsedId = parseInt(savedSubjectId, 10);\n                                // Check if the saved subject exists in the fetched subjects\n                                if (!isNaN(parsedId) && enhancedSubjects.some({\n                                    \"StudentDashboard.useEffect\": (subject)=>subject.id === parsedId\n                                }[\"StudentDashboard.useEffect\"])) {\n                                    setSelectedSubject(parsedId);\n                                } else {\n                                    // If saved subject doesn't exist in current subjects, default to first one\n                                    if (enhancedSubjects.length > 0) {\n                                        setSelectedSubject(enhancedSubjects[0].id);\n                                    }\n                                }\n                            } else {\n                                // No saved selection, default to first subject\n                                if (enhancedSubjects.length > 0) {\n                                    setSelectedSubject(enhancedSubjects[0].id);\n                                }\n                            }\n                        } catch (error) {\n                            console.error('Error accessing localStorage:', error);\n                            // Fall back to default behavior\n                            if (enhancedSubjects.length > 0) {\n                                setSelectedSubject(enhancedSubjects[0].id);\n                            }\n                        }\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching data:', error);\n                        setError('Failed to load data. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        status,\n        session\n    ]);\n    // Save selected subject to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null) {\n                try {\n                    localStorage.setItem('selectedSubjectId', selectedSubject.toString());\n                } catch (error) {\n                    console.error('Error saving to localStorage:', error);\n                }\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject\n    ]);\n    // Fetch units when a subject is selected\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDashboard.useEffect\": ()=>{\n            if (selectedSubject !== null && childData) {\n                setLoading(true);\n                fetch(`/api/units-by-subject-year?subjectId=${selectedSubject}&yearNumber=${encodeURIComponent(childData.year)}`).then({\n                    \"StudentDashboard.useEffect\": (res)=>{\n                        if (!res.ok) {\n                            throw new Error('Failed to fetch units');\n                        }\n                        return res.json();\n                    }\n                }[\"StudentDashboard.useEffect\"]).then({\n                    \"StudentDashboard.useEffect\": (data)=>{\n                        setUnits({\n                            \"StudentDashboard.useEffect\": (prevUnits)=>({\n                                    ...prevUnits,\n                                    [selectedSubject]: data\n                                })\n                        }[\"StudentDashboard.useEffect\"]);\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]).catch({\n                    \"StudentDashboard.useEffect\": (error)=>{\n                        console.error('Error fetching units:', error);\n                        setError('Failed to load units. Please try again later.');\n                        setLoading(false);\n                    }\n                }[\"StudentDashboard.useEffect\"]);\n            }\n        }\n    }[\"StudentDashboard.useEffect\"], [\n        selectedSubject,\n        childData\n    ]);\n    // Create a component to handle mastery data for a single unit\n    const UnitMasteryPill = ({ unitId, studentId })=>{\n        const masteryData = (0,_hooks_useMastery__WEBPACK_IMPORTED_MODULE_6__.useMastery)(studentId, unitId);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TpPill__WEBPACK_IMPORTED_MODULE_5__.TpPill, {\n            tp: masteryData?.currentTp ?? 0,\n            confidence: masteryData?.confidence ?? 'low'\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined);\n    };\n    const handleStartQuiz = (unit)=>{\n        // Show the difficulty modal and store the selected unit\n        setSelectedUnit(unit);\n        setShowDifficultyModal(true);\n    };\n    const handleDifficultySelect = async (difficulty)=>{\n        // Close the modal\n        setShowDifficultyModal(false);\n        if (!selectedUnit || !childData) return;\n        try {\n            setLoading(true);\n            // Get the year ID from the API if not available in childData\n            let yearId = childData.yearId;\n            if (!yearId) {\n                try {\n                    // Extract year number from the year string (e.g., \"Year 5\" -> 5)\n                    const yearMatch = childData.year.match(/\\d+/);\n                    const yearNumber = yearMatch ? parseInt(yearMatch[0], 10) : null;\n                    if (yearNumber) {\n                        // Fetch the year ID based on the year number\n                        const yearResponse = await fetch(`/api/years?yearNumber=${yearNumber}`);\n                        if (yearResponse.ok) {\n                            const yearData = await yearResponse.json();\n                            if (yearData && yearData.id) {\n                                yearId = yearData.id;\n                            }\n                        }\n                    }\n                } catch (error) {\n                    console.error('Error fetching year ID:', error);\n                }\n            }\n            if (!yearId) {\n                setError('Could not determine year ID. Please try again or contact support.');\n                setLoading(false);\n                return;\n            }\n            // Create a practice quiz attempt using the new API\n            const response = await fetch('/api/quiz/create-practice', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    subjectId: selectedSubject,\n                    yearId: yearId,\n                    unitId: selectedUnit.id\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to create practice quiz');\n            }\n            const data = await response.json();\n            // Check if we should use v2 quiz\n            const quizVersion = \"v2        # Controls which quiz version to use (v1 or v2)\" || 0;\n            console.log(\"🎯 QUIZ VERSION DEBUG:\");\n            console.log(\"Quiz version from env:\", quizVersion);\n            console.log(\"Environment variable:\", \"v2        # Controls which quiz version to use (v1 or v2)\");\n            console.log(\"Condition check (quizVersion === 'v2'):\", quizVersion === 'v2');\n            if (quizVersion === 'v2') {\n                console.log(\"✅ Redirecting to V2 quiz:\", `/quiz/v2/${data.attemptId}`);\n                router.push(`/quiz/v2/${data.attemptId}`);\n            } else {\n                console.log(\"❌ Redirecting to V1 quiz:\", `/quiz?attemptId=${data.attemptId}`);\n                router.push(`/quiz?attemptId=${data.attemptId}`);\n            }\n        } catch (error) {\n            console.error('Error creating practice quiz:', error);\n            setError('Failed to start practice quiz. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDifficultyModalClose = ()=>{\n        setShowDifficultyModal(false);\n        setSelectedUnit(null);\n    };\n    const handleShowStreak = ()=>{\n        setShowStreakModal(true);\n    };\n    const handleTestClick = ()=>{\n        // Navigate to the test page or show test options\n        router.push('/start-quiz');\n    };\n    // Show loading state while checking authentication\n    if (status === 'loading' || status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 346,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n            lineNumber: 345,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Student Dashboard | Studu\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Student dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-full min-h-screen bg-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 w-full bg-[#0F5FA6] text-white p-3 flex items-center justify-between shadow-md\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xl font-bold mr-2\",\n                                        children: \"Studu\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm bg-[#0A8CBF] px-3 py-1 rounded-full\",\n                                        children: [\n                                            childData.name,\n                                            \" | \",\n                                            childData.year\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleShowStreak,\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.streak\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.GemIcon, {\n                                                size: 20,\n                                                className: \"text-[#05DBF2] mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.gems\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center bg-[#0A8CBF] rounded-full px-3 py-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HeartIcon, {\n                                                size: 20,\n                                                className: \"text-red-500 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold\",\n                                                children: stats.hearts\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 p-4 bg-gray-100 overflow-y-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Subjects\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined) : error && subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.reload(),\n                                                className: \"mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm\",\n                                                children: \"Retry\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, undefined) : subjects.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No subjects found for your year. Please contact your teacher.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex overflow-x-auto pb-2 space-x-3\",\n                                        children: subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setSelectedSubject(subject.id),\n                                                className: `flex flex-col items-center justify-center p-4 rounded-lg shadow-md min-w-[100px] h-[100px] ${selectedSubject === subject.id ? 'ring-4 ring-yellow-400' : ''} ${subject.unlocked ? subject.color : 'bg-gray-400'}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mb-1\",\n                                                        children: subject.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: subject.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-white/80 mt-1\",\n                                                        children: [\n                                                            subject.completed,\n                                                            \"/\",\n                                                            subject.total\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, subject.id, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, undefined),\n                            selectedSubject !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-bold mb-3\",\n                                        children: \"Learning Path\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-xl p-4 shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-3xl mr-3\",\n                                                        children: subjects.find((s)=>s.id === selectedSubject)?.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-bold text-lg\",\n                                                                children: subjects.find((s)=>s.id === selectedSubject)?.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Complete lessons to unlock new content\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            loading && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 19\n                                            }, undefined) : error && (!units[selectedSubject] || units[selectedSubject].length === 0) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, undefined) : !units[selectedSubject] || units[selectedSubject].length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"No units found for this subject. Please contact your teacher.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-4\",\n                                                children: units[selectedSubject].map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `relative border-2 rounded-lg p-4 hover:shadow-md transition-shadow ${unit.unlocked ? 'border-[#0A8CBF] bg-gradient-to-r from-[#04B2D9]/5 to-white' : 'border-gray-300 bg-gray-100'}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-bold flex items-center gap-2\",\n                                                                            children: [\n                                                                                \"Unit \",\n                                                                                unit.unitNumber || index + 1,\n                                                                                \": \",\n                                                                                unit.name,\n                                                                                 true && childData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnitMasteryPill, {\n                                                                                    unitId: unit.id,\n                                                                                    studentId: childData.id\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                    lineNumber: 482,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        unit.nameZh && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                            className: \"text-sm text-gray-600 mb-1\",\n                                                                            children: unit.nameZh\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                            lineNumber: 489,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                unit.unlocked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleStartQuiz({\n                                                                            id: unit.id,\n                                                                            name: unit.name,\n                                                                            subject: subjects.find((s)=>s.id === selectedSubject)?.name || ''\n                                                                        }),\n                                                                    className: \"px-4 py-2 rounded-lg font-bold bg-[#0F5FA6] text-white\",\n                                                                    children: \"Practice\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 29\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-300 p-2 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"20\",\n                                                                        height: \"20\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        className: \"text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                                                                                width: \"18\",\n                                                                                height: \"11\",\n                                                                                x: \"3\",\n                                                                                y: \"11\",\n                                                                                rx: \"2\",\n                                                                                ry: \"2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                d: \"M7 11V7a5 5 0 0 1 10 0v4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                                lineNumber: 521,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, unit.id, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"sticky bottom-0 w-full bg-white border-t border-gray-200 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-around items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-[#0F5FA6]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.HomeIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.BookIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Lessons\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.TrophyIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.UserIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleTestClick,\n                                    className: \"flex flex-col items-center p-2 text-gray-500 hover:text-[#0A8CBF]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.ClipboardCheckIcon, {\n                                            size: 24\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs mt-1\",\n                                            children: \"Test\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, undefined),\n                    showStreakModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl max-w-md w-full p-6 shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookIcon_ClipboardCheckIcon_FlameIcon_GemIcon_HeartIcon_HomeIcon_TrophyIcon_UserIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__.FlameIcon, {\n                                                    size: 28,\n                                                    className: \"text-[#0F5FA6] mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 571,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Your Streak\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowStreakModal(false),\n                                            className: \"p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M18 6 6 18\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"m6 6 12 12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-6xl font-bold text-[#0F5FA6] mb-2\",\n                                            children: stats.streak\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: [\n                                                stats.streak === 1 ? 'day' : 'days',\n                                                \" in a row\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-6\",\n                                    children: [\n                                        {\n                                            days: 7,\n                                            label: '1 Week',\n                                            status: stats.streak >= 7 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 14,\n                                            label: '2 Weeks',\n                                            status: stats.streak >= 14 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 30,\n                                            label: '1 Month',\n                                            status: stats.streak >= 30 ? 'reached' : 'upcoming'\n                                        },\n                                        {\n                                            days: 100,\n                                            label: '100 Days',\n                                            status: stats.streak >= 100 ? 'reached' : 'upcoming'\n                                        }\n                                    ].map((milestone)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `flex justify-between items-center p-4 rounded-lg ${milestone.status === 'reached' ? 'bg-[#04B2D9]/10' : 'bg-gray-100'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-8 h-8 rounded-full flex items-center justify-center mr-3 ${milestone.status === 'reached' ? 'bg-[#0F5FA6] text-white' : 'bg-gray-300'}`,\n                                                            children: milestone.status === 'reached' ? '✓' : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: milestone.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold\",\n                                                    children: [\n                                                        milestone.days,\n                                                        \" days\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                                    lineNumber: 620,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, milestone.days, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-gray-600 text-sm\",\n                                    children: \"Keep learning daily to build your streak!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                            lineNumber: 568,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 11\n                    }, undefined),\n                    showDifficultyModal && selectedUnit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DifficultyModal__WEBPACK_IMPORTED_MODULE_7__.DifficultyModal, {\n                        onSelect: handleDifficultySelect,\n                        onClose: handleDifficultyModalClose,\n                        unit: selectedUnit\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\student-dashboard.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StudentDashboard);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/student-dashboard.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*********************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BookIcon,ClipboardCheckIcon,FlameIcon,GemIcon,HeartIcon,HomeIcon,TrophyIcon,UserIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*********************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BookIcon: () => (/* reexport safe */ _icons_book_js__WEBPACK_IMPORTED_MODULE_0__["default"]),
/* harmony export */   ClipboardCheckIcon: () => (/* reexport safe */ _icons_clipboard_check_js__WEBPACK_IMPORTED_MODULE_1__["default"]),
/* harmony export */   FlameIcon: () => (/* reexport safe */ _icons_flame_js__WEBPACK_IMPORTED_MODULE_2__["default"]),
/* harmony export */   GemIcon: () => (/* reexport safe */ _icons_gem_js__WEBPACK_IMPORTED_MODULE_3__["default"]),
/* harmony export */   HeartIcon: () => (/* reexport safe */ _icons_heart_js__WEBPACK_IMPORTED_MODULE_4__["default"]),
/* harmony export */   HomeIcon: () => (/* reexport safe */ _icons_house_js__WEBPACK_IMPORTED_MODULE_5__["default"]),
/* harmony export */   TrophyIcon: () => (/* reexport safe */ _icons_trophy_js__WEBPACK_IMPORTED_MODULE_6__["default"]),
/* harmony export */   UserIcon: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_7__["default"])
/* harmony export */ });
/* harmony import */ var _icons_book_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/book.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/book.js");
/* harmony import */ var _icons_clipboard_check_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/clipboard-check.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/clipboard-check.js");
/* harmony import */ var _icons_flame_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/flame.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/flame.js");
/* harmony import */ var _icons_gem_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/gem.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/gem.js");
/* harmony import */ var _icons_heart_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/heart.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/heart.js");
/* harmony import */ var _icons_house_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/house.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/house.js");
/* harmony import */ var _icons_trophy_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/trophy.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/trophy.js");
/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/user.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/user.js");










/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=XIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!**************************************************************************************************!*\
  !*** __barrel_optimize__?names=XIcon!=!../../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   XIcon: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/x.js */ "(pages-dir-node)/../../node_modules/lucide-react/dist/esm/icons/x.js");



/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = import("swr");;

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fstudent-dashboard&preferredRegion=&absolutePagePath=.%2Fpages%5Cstudent-dashboard.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();