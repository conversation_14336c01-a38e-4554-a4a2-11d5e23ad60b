import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import prisma from '../../../lib/prisma';
import fs from 'fs/promises';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check authentication and admin role
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (session.user?.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Forbidden - Admin access required' });
    }

    // Extract query parameters
    const { yearId, subjectId, unitId, kind } = req.query;

    // Build the where clause based on provided filters
    const where: any = {};

    if (yearId && !isNaN(Number(yearId))) {
      where.yearId = Number(yearId);
    }

    if (subjectId && !isNaN(Number(subjectId))) {
      where.subjectId = Number(subjectId);
    }

    if (unitId && !isNaN(Number(unitId))) {
      where.unitId = Number(unitId);
    }

    if (kind && typeof kind === 'string') {
      // Handle comma-separated kinds
      const kinds = kind.split(',');
      where.contentType = {
        in: kinds,
      };
    }

    // Fetch notes with related data
    const notes = await prisma.note.findMany({
      where,
      include: {
        year: true,
        subject: true,
        unit: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // For markdown notes, fetch the content
    const notesWithContent = await Promise.all(
      notes.map(async (note) => {
        if (note.contentType === 'MARKDOWN' || note.contentType === 'SAMPLE') {
          try {
            console.log(`Fetching content for note ${note.id} from URL: ${note.fileUrl}`);

            let content;

            // Check if the URL is a relative path or absolute URL
            const isRelativePath = !note.fileUrl.startsWith('http://') && !note.fileUrl.startsWith('https://');

            if (isRelativePath) {
              // For relative paths, try to read the file directly from the uploads directory
              try {
                console.log(`Trying to read file from uploads directory: ${note.fileUrl}`);

                // Construct the full path to the file
                const fullPath = path.join(process.cwd(), note.fileUrl.startsWith('/') ? note.fileUrl.substring(1) : note.fileUrl);
                console.log(`Full path: ${fullPath}`);

                // Read the file
                content = await fs.readFile(fullPath, 'utf-8');
                console.log(`Successfully read file from disk: ${fullPath}`);
              } catch (fileError) {
                console.error(`Failed to read file directly: ${fileError}`);

                // Try to fetch from the API endpoint
                try {
                  // Construct the full URL using the host from the request
                  const host = req.headers.host || 'localhost:3000';
                  const protocol = host.includes('localhost') ? 'http' : 'https';
                  const apiUrl = `${protocol}://${host}${note.fileUrl}`;

                  console.log(`Trying to fetch from API: ${apiUrl}`);
                  const response = await fetch(apiUrl);

                  if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                  }

                  content = await response.text();
                  console.log(`Successfully fetched from API, content length: ${content.length}`);
                } catch (apiError) {
                  console.error(`Failed to fetch from API: ${apiError}`);
                  return {
                    ...note,
                    content: `Failed to load content: ${fileError}. API fetch error: ${apiError}`
                  };
                }
              }
            } else {
              // For absolute URLs, try to fetch directly
              try {
                console.log(`Fetching from absolute URL: ${note.fileUrl}`);
                const response = await fetch(note.fileUrl);

                if (!response.ok) {
                  throw new Error(`HTTP ${response.status}`);
                }

                content = await response.text();
                console.log(`Successfully fetched from URL, content length: ${content.length}`);
              } catch (fetchError) {
                console.error(`Failed to fetch from URL: ${fetchError}`);
                return {
                  ...note,
                  content: `Failed to load content: ${fetchError}`
                };
              }
            }
            console.log(`Successfully fetched content for note ${note.id}, content length: ${content.length}`);

            // Add the content to the note
            return { ...note, content };
          } catch (error) {
            console.error(`Error fetching content for note ${note.id}:`, error);
            return {
              ...note,
              content: `Error loading content: ${error instanceof Error ? error.message : String(error)}`
            };
          }
        }
        return note;
      })
    );

    return res.status(200).json({ notes: notesWithContent });
  } catch (error) {
    console.error('Error fetching notes:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
