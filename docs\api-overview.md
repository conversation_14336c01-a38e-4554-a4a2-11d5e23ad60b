# API Overview

This document provides an overview of the API endpoints available in the My Quiz App application.

## Authentication

Most API endpoints require authentication. The application uses NextAuth.js for authentication, which provides JWT-based session management.

### Authentication Methods

- **Parent Login**: Email and password
- **Child Login**: Username and PIN
- **Admin Login**: Email and password with admin role

### Session Management

- Sessions are managed using JWT tokens
- Session duration: 30 days
- Custom login page: `/login`

## API Structure

The API is organized into the following categories:

1. **General Endpoints**: Basic data retrieval for years, subjects, topics, etc.
2. **Quiz Endpoints**: Creating, retrieving, and managing quizzes
3. **Analytics Endpoints**: Retrieving analytics data for translations and knowledge
4. **Admin Endpoints**: Admin-only functionality for managing the application
5. **Authentication Endpoints**: Login, logout, and session management

## Response Format

All API endpoints return JSON responses with the following general structure:

### Success Response

```json
{
  "data": { ... },  // The actual response data
  "message": "Optional success message"
}
```

### Error Response

```json
{
  "message": "Error message describing what went wrong",
  "error": "Optional detailed error information"
}
```

## Common HTTP Status Codes

- **200 OK**: Request succeeded
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Authenticated but not authorized
- **404 Not Found**: Resource not found
- **405 Method Not Allowed**: HTTP method not supported
- **500 Internal Server Error**: Server-side error

## Role-Based Access Control

The application implements role-based access control with the following roles:

- **CHILD**: Can access student-specific endpoints
- **PARENT**: Can access parent-specific endpoints
- **ADMIN**: Can access all endpoints, including admin-specific ones

## API Documentation

Detailed documentation for each API category can be found in the following files:

- [General API Endpoints](api-general.md)
- [Quiz API Endpoints](api-quiz.md)
- [Analytics API Endpoints](api-analytics.md)
- [Admin API Endpoints](api-admin.md)
- [Authentication API Endpoints](api-auth.md)
