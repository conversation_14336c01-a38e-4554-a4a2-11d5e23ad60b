// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client" // Specify output path
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Subject {
  id                Int               @id @default(autoincrement())
  name              String            @unique
  units             Unit[]
  questions         Question[]
  QuizAttempt       QuizAttempt[]
  notes             Note[]
  generationBatches GenerationBatch[]

  @@map("TG_Subject")
}

model Year {
  id                Int               @id @default(autoincrement())
  yearNumber        Int               @unique
  units             Unit[]
  questions         Question[]
  notes             Note[]
  generationBatches GenerationBatch[]

  @@map("TG_Year")
}

model Unit {
  id                Int               @id @default(autoincrement())
  unitNumber        Int
  topicEn           String
  topicZh           String
  topicMs           String? // Added Malay topic field (optional)
  subject           Subject           @relation(fields: [subjectId], references: [id])
  subjectId         Int
  year              Year              @relation(fields: [yearId], references: [id])
  yearId            Int
  questions         Question[]
  QuizAttempt       QuizAttempt[]
  notes             Note[]
  generationBatches GenerationBatch[]

  @@unique([unitNumber, subjectId, yearId])
  @@map("TG_Unit")
}

model Question {
  id                Int               @id @default(autoincrement())
  questionId        String            @unique // e.g. "q1", "q2" etc
  type              QuestionType
  promptEn          String
  promptZh          String
  promptMs          String?
  promptMedia       Media?            @relation(name: "QuestionPromptMedia", fields: [promptMediaId], references: [id])
  promptMediaId     Int?
  spec              Json?
  keywords          Json? // Store highlightable keywords for each language
  tpLevel           Int // Tahap Penguasaan level (1-6)
  originalLanguage  Language          @default(ZH)
  translationState  TranslationStatus @default(PARTIAL)
  status            QuestionStatus    @default(DRAFT)
  explanation       ExplanationText?
  subject           Subject           @relation(fields: [subjectId], references: [id])
  subjectId         Int
  year              Year              @relation(fields: [yearId], references: [id])
  yearId            Int
  unit              Unit              @relation(fields: [unitId], references: [id])
  unitId            Int
  subTopicEn        String
  subTopicZh        String?
  subTopicMs        String?
  choices           Choice[] // For multiple choice questions
  answer            Answer? // One-to-one relation with answer
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  TranslationLog    TranslationLog[]
  StudentAnswer     StudentAnswer[]
  QuestionFlag      QuestionFlag[]
  generationBatchId Int? // optional FK
  generationBatch   GenerationBatch?  @relation(fields: [generationBatchId], references: [id])

  @@map("TG_Question")
}

model Media {
  id        Int        @id @default(autoincrement())
  url       String
  altEn     String?
  altZh     String?
  altMs     String? // Added Malay alt text field (optional)
  createdAt DateTime   @default(now())
  questions Question[] @relation("QuestionPromptMedia")
  choices   Choice[]

  @@map("TG_Media")
}

model Choice {
  id         Int      @id @default(autoincrement())
  key        String // e.g. "A", "B", "C", "D"
  textEn     String
  textZh     String
  textMs     String? // Added Malay text field (optional)
  media      Media?   @relation(fields: [mediaId], references: [id])
  mediaId    Int?
  question   Question @relation(fields: [questionId], references: [id])
  questionId Int

  @@map("TG_Choice")
}

enum AnswerType {
  SINGLE_CHOICE
  MULTI_CHOICE
  SHORT_TEXT
  TRUE_FALSE
  FILL_IN_THE_BLANK
  MATCHING
  SEQUENCING
  LONG_TEXT_RUBRIC
}

model Answer {
  id         Int        @id @default(autoincrement())
  question   Question   @relation(fields: [questionId], references: [id])
  questionId Int        @unique
  type       AnswerType @default(SINGLE_CHOICE)
  // For multiple choice questions
  key        String? // e.g. "A", "B", "C", "D"
  // For short answer questions
  textEn     String?
  textZh     String?
  textMs     String?
  // For complex answer structures
  answerSpec Json? // NEW – complex structures
  createdAt  DateTime   @default(now())

  @@map("TG_Answer")
}

model ExplanationText {
  id         Int      @id @default(autoincrement())
  question   Question @relation(fields: [questionId], references: [id])
  questionId Int      @unique
  textEn     String
  textZh     String
  textMs     String?

  @@map("TG_ExplanationText")
}

model Account {
  id                Int               @id @default(autoincrement())
  email             String            @unique
  password          String? // Made optional for migration
  password_hash     String?
  salt              String?
  role              Role
  name              String
  status            Status            @default(ACTIVE)
  license           License?
  children          Child[]
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  generationBatches GenerationBatch[]

  @@map("TG_Account")
}

model Child {
  id               Int              @id @default(autoincrement())
  name             String
  year             String
  username         String           @unique
  pin              String? // Made optional for migration
  pin_hash         String?
  salt             String?
  quizLanguage     Language         @default(ZH) // Language for quiz questions and answers
  menuLanguage     Language         @default(EN) // Language for UI elements
  showDualLanguage Boolean          @default(false) // Whether to show questions in dual language
  account          Account          @relation(fields: [accountId], references: [id])
  accountId        Int
  TranslationLog   TranslationLog[]
  QuizAttempt      QuizAttempt[]
  StudentAnswer    StudentAnswer[]
  StudentMastery   StudentMastery[]
  QuestionFlag     QuestionFlag[]

  @@map("TG_Child")
}

model License {
  id        Int         @id @default(autoincrement())
  type      LicenseType
  duration  Int?
  account   Account     @relation(fields: [accountId], references: [id])
  accountId Int         @unique

  @@map("TG_License")
}

model TranslationLog {
  id             Int      @id @default(autoincrement())
  timestamp      DateTime @default(now())
  translatedText String
  child          Child    @relation(fields: [childId], references: [id])
  childId        Int
  question       Question @relation(fields: [questionId], references: [id])
  questionId     Int

  @@map("TG_TranslationLog")
}

model QuizAttempt {
  id                   Int             @id @default(autoincrement())
  questionIds          String[] // List of question IDs for this attempt
  startTime            DateTime        @default(now())
  endTime              DateTime?
  score                Float? // Optional: Can be calculated based on StudentAnswers
  child                Child           @relation(fields: [childId], references: [id])
  childId              Int
  subject              Subject         @relation(fields: [subjectId], references: [id])
  subjectId            Int
  unit                 Unit?           @relation(fields: [unitId], references: [id]) // Made optional based on schema
  unitId               Int? // Made optional based on schema
  currentQuestionIndex Int? // Add field to store the index of the current question
  status               QuizStatus      @default(ACTIVE) // Add status field
  quizType             QuizType        @default(TEST) // Add quiz type field
  metadata             Json? // Store additional metadata like TP level distribution
  studentAnswers       StudentAnswer[]
  questionFlags        QuestionFlag[]

  @@map("TG_QuizAttempt")
}

model StudentAnswer {
  id              Int         @id @default(autoincrement())
  submittedAt     DateTime    @default(now())
  submittedKey    String? // single choice
  submittedText   String? // short text
  submittedJson   Json? // NEW – complex submissions
  isCorrect       Boolean
  /// true if the student answered correctly on their first try
  firstTryCorrect Boolean     @default(false)
  /// "mastery" (default) or "test"
  quizType        String      @default("mastery")
  child           Child       @relation(fields: [childId], references: [id])
  childId         Int
  question        Question    @relation(fields: [questionId], references: [id])
  questionId      Int
  quizAttempt     QuizAttempt @relation(fields: [quizAttemptId], references: [id])
  quizAttemptId   Int

  @@map("TG_StudentAnswer")
}

model StudentMastery {
  /// scope = "unit" or "subject"
  scope      String   @db.VarChar(10)
  /// FK → Unit.id or Subject.id depending on scope
  scopeId    Int
  student    Child    @relation(fields: [studentId], references: [id])
  studentId  Int
  currentTp  Int /// 1-6
  confidence String   @db.VarChar(10) // secure | emerging | low
  computedAt DateTime @default(now())

  @@id([studentId, scope, scopeId])
  @@map("TG_StudentMastery")
}

enum QuizStatus {
  ACTIVE
  COMPLETED
  CANCELED
}

enum QuizType {
  MASTERY
  TEST
  QUICK
}

enum QuizMode {
  MASTERY
  TEST
}

enum Role {
  PARENT
  CHILD
  TEACHER
  ADMIN
}

enum Status {
  ACTIVE
  PENDING
  INACTIVE
}

enum LicenseType {
  FREE_TRIAL
  STANDARD_PLAN
}

enum QuestionType {
  MULTIPLE_CHOICE
  MULTIPLE_CHOICE_IMAGE
  PICTURE_PROMPT
  FILL_IN_THE_BLANK
  TRUE_FALSE
  SHORT_ANSWER
  LONG_ANSWER
  MATCHING
  SEQUENCING
}

enum QuestionStatus {
  DRAFT
  LIVE
  ARCHIVED
}

enum BatchStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum Language {
  EN // English
  ZH // Chinese
  MS // Malay
}

enum TranslationStatus {
  NONE
  PARTIAL
  COMPLETE
}

model Setting {
  id        Int      @id @default(autoincrement())
  key       String   @unique // e.g., "quizQuestionLimit"
  value     String // Store value as string, parse in application logic
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("TG_Setting") // Optional: Map to a table name like TG_Setting
}

model Note {
  id                Int               @id @default(autoincrement())
  filename          String
  fileUrl           String
  mimeType          String
  fileSize          Int
  contentType       NoteType          @default(OTHER)
  year              Year              @relation(fields: [yearId], references: [id])
  yearId            Int
  subject           Subject           @relation(fields: [subjectId], references: [id])
  subjectId         Int
  unit              Unit              @relation(fields: [unitId], references: [id])
  unitId            Int
  createdAt         DateTime          @default(now())
  generationBatches GenerationBatch[] @relation("BatchNotes")

  @@map("TG_Notes")
}

model GenerationBatch {
  id               Int            @id @default(autoincrement())
  admin            Account        @relation(fields: [adminId], references: [id])
  adminId          Int
  year             Year           @relation(fields: [yearId], references: [id])
  yearId           Int
  subject          Subject        @relation(fields: [subjectId], references: [id])
  subjectId        Int
  unit             Unit?          @relation(fields: [unitId], references: [id])
  unitId           Int?
  questionTypes    QuestionType[]
  numQuestions     Int
  provider         String         @default("openrouter") // AI provider (openrouter or gemini)
  modelUsed        String
  language         Language       @default(ZH) // Default to Chinese
  status           BatchStatus    @default(PENDING)
  jobId            String?
  createdAt        DateTime       @default(now())
  completedAt      DateTime?
  promptTokens     Int? // Input tokens from provider
  completionTokens Int? // Output tokens from provider
  totalTokens      Int? // Total tokens from provider
  metadata         Json? // Store additional metadata like tpDistribution
  notes            Note[]         @relation("BatchNotes")
  questions        Question[]

  @@map("TG_GenerationBatch")
}

model QuizConfig {
  id                    Int      @id @default(autoincrement())
  mode                  QuizMode
  numQuestions          Int      @default(10)
  questionTypes         String[] @default([])
  allowTranslate        Boolean  @default(true)
  allowHints            Boolean  @default(true)
  allowAiTutor          Boolean  @default(true)
  reviewMissedQuestions Boolean  @default(true)
  updatedAt             DateTime @updatedAt

  @@unique([mode])
  @@map("TG_QuizConfig")
}

enum NoteType {
  PDF
  IMAGE
  TEXT
  MARKDOWN
  SAMPLE
  OTHER
}

model HintLog {
  id         Int      @id @default(autoincrement())
  questionId Int
  childId    Int
  hint       String
  timestamp  DateTime @default(now())

  @@map("TG_HintLog")
}

model AiTutorLog {
  id           Int      @id @default(autoincrement())
  questionId   Int
  childId      Int
  userQuestion String
  aiResponse   String
  language     String   @db.VarChar(2)
  timestamp    DateTime @default(now())

  @@map("TG_AiTutorLog")
}

enum FlagStatus {
  PENDING
  REVIEWED
  APPROVED
  REJECTED
}

model QuestionFlag {
  id            Int         @id @default(autoincrement())
  question      Question    @relation(fields: [questionId], references: [id])
  questionId    Int
  child         Child       @relation(fields: [childId], references: [id])
  childId       Int
  quizAttempt   QuizAttempt @relation(fields: [quizAttemptId], references: [id])
  quizAttemptId Int
  submittedKey  String? // The answer the student selected
  submittedText String? // For text-based answers
  submittedJson Json? // For complex answers
  status        FlagStatus  @default(PENDING)
  reviewNotes   String? // Admin notes about the flag
  createdAt     DateTime    @default(now())
  reviewedAt    DateTime? // When the flag was reviewed

  @@map("TG_QuestionFlag")
}
