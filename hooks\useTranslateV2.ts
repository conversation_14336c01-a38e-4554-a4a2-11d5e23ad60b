import { useState, useCallback, useEffect } from 'react';
import { Question } from '../types/quiz';

interface ContextMenu {
  x: number;
  y: number;
  text: string;
}

interface TranslateMenuData {
  position: {
    top: number;
    left: number;
  };
  text: string | null;
  onTranslate: () => Promise<void>;
  displayLanguage: 'en' | 'zh' | 'ms';
}

interface UseTranslateReturn {
  contextMenu: ContextMenu | null;
  translatedText: string | null;
  onMouseUp: (e: React.MouseEvent) => void;
  onTranslate: () => Promise<void>;
  clearContextMenu: () => void;
  menuData: TranslateMenuData | null;
}

/**
 * Hook to manage text selection and translation functionality for QuizV2
 * @param currentQuestion The current question being displayed
 * @param displayLanguage The current display language ('en' | 'zh' | 'ms')
 * @param enabled Whether translation is enabled
 * @returns Functions and state for translation functionality
 */
export function useTranslateV2(
  currentQuestion: Question | undefined,
  displayLanguage: 'en' | 'zh' | 'ms',
  enabled: boolean = true
): UseTranslateReturn {
  const [contextMenu, setContextMenu] = useState<ContextMenu | null>(null);
  const [translatedText, setTranslatedText] = useState<string | null>(null);

  // Handle mouse up for text selection
  const onMouseUp = useCallback((e: React.MouseEvent) => {
    // If translation is disabled, do nothing
    if (!enabled) return;

    const selectedText = window.getSelection()?.toString().trim();
    if (selectedText) {
      setContextMenu({ x: e.pageX, y: e.pageY, text: selectedText });
      setTranslatedText(null); // Clear previous translation
    }
  }, [enabled]);

  // Listen for the custom event from QuizV2Provider
  useEffect(() => {
    if (!enabled) return;

    const handleTextSelected = (e: CustomEvent) => {
      const { text, x, y } = e.detail;
      if (text) {
        setContextMenu({ x, y, text });
        setTranslatedText(null); // Clear previous translation
      }
    };

    window.addEventListener('quizV2TextSelected', handleTextSelected as EventListener);

    return () => {
      window.removeEventListener('quizV2TextSelected', handleTextSelected as EventListener);
    };
  }, [enabled]);

  // Clear context menu
  const clearContextMenu = useCallback(() => {
    setContextMenu(null);
  }, []);

  // Handle translation
  const onTranslate = useCallback(async () => {
    if (contextMenu && currentQuestion && enabled) {
      setTranslatedText('Translating...'); // Show loading indicator
      try {
        const response = await fetch('/api/ai-translator', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            highlightedText: contextMenu.text,
            question: currentQuestion.promptZh || ''
          }),
        });

        const data = await response.json();
        if (data.translatedText) {
          setTranslatedText(data.translatedText); // Display translated text

          // Dispatch a custom event with the translation result
          console.log('🚀 Creating translationComplete event in useTranslateV2 with:', {
            translatedText: data.translatedText,
            originalText: contextMenu.text
          });

          const translationCompleteEvent = new CustomEvent('translationComplete', {
            detail: {
              translatedText: data.translatedText,
              originalText: contextMenu.text
            }
          });

          console.log('🚀 Dispatching translationComplete event from useTranslateV2');
          window.dispatchEvent(translationCompleteEvent);
          console.log('🚀 translationComplete event dispatched from useTranslateV2');

          // Log the translation
          try {
            await fetch('/api/log-translation', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                questionId: currentQuestion.id,
                translatedText: contextMenu.text,
              }),
            });
          } catch (logError) {
            console.error('Error logging translation:', logError);
          }
        } else {
          setTranslatedText('Translation failed.'); // Show error message
        }
      } catch (error) {
        console.error('Translation error:', error);
        setTranslatedText('Translation failed.'); // Show error message
      }
    }
  }, [contextMenu, currentQuestion, enabled]);

  // Create menu data for the translation menu
  const menuData = contextMenu && enabled ? {
    position: {
      top: contextMenu.y,
      left: contextMenu.x
    },
    text: translatedText,
    onTranslate,
    displayLanguage
  } : null;

  // If translation is disabled, return no-op functions
  if (!enabled) {
    return {
      contextMenu: null,
      translatedText: null,
      onMouseUp: () => {},
      onTranslate: async () => {},
      clearContextMenu: () => {},
      menuData: null
    };
  }

  return {
    contextMenu,
    translatedText,
    onMouseUp,
    onTranslate,
    clearContextMenu,
    menuData
  };
}

export default useTranslateV2;
