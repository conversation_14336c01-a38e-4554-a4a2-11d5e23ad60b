import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { hashPassword, verifyPassword } from '../lib/auth';
import axios from 'axios';

const prisma = new PrismaClient();
const API_URL = 'http://localhost:3000/api';

describe('Authentication System', () => {
  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.child.deleteMany({ where: { username: 'testchild' } });
    await prisma.account.deleteMany({ where: { email: '<EMAIL>' } });
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  it('should create account with hashed password and verify login', async () => {
    // Create test account
    const testPassword = 'testpass123';
    const { hash: passwordHash, salt } = await hashPassword(testPassword);
    
    const account = await prisma.account.create({
      data: {
        name: 'Test Parent',
        email: '<EMAIL>',
        password_hash: passwordHash,
        salt: salt,
        role: 'PARENT',
        status: 'ACTIVE',
        children: {
          create: {
            name: 'Test Child',
            year: 'Year 1',
            username: 'testchild',
            pin_hash: (await hashPassword('123456')).hash,
            salt: (await hashPassword('123456')).salt
          }
        }
      },
      include: {
        children: true
      }
    });

    expect(account).toBeTruthy();
    expect(account.password_hash).toBeTruthy();
    expect(account.salt).toBeTruthy();
    expect(account.children[0].pin_hash).toBeTruthy();
    expect(account.children[0].salt).toBeTruthy();

    // Verify password hashing works
    const isValid = await verifyPassword(testPassword, passwordHash, salt);
    expect(isValid).toBe(true);

    try {
      // Test parent login
      const loginResponse = await axios.post(`${API_URL}/auth/login`, {
        email: '<EMAIL>',
        password: testPassword
      });

      expect(loginResponse.status).toBe(200);
      expect(loginResponse.data).toHaveProperty('id');
      expect(loginResponse.data).toHaveProperty('role', 'PARENT');
      expect(loginResponse.data).not.toHaveProperty('password');
      expect(loginResponse.data).not.toHaveProperty('password_hash');
      expect(loginResponse.data).not.toHaveProperty('salt');

      // Test child login
      const childLoginResponse = await axios.post(`${API_URL}/auth/login`, {
        username: 'testchild',
        pin: '123456'
      });

      expect(childLoginResponse.status).toBe(200);
      expect(childLoginResponse.data).toHaveProperty('id');
      expect(childLoginResponse.data).toHaveProperty('role', 'CHILD');
      expect(childLoginResponse.data).not.toHaveProperty('pin');
      expect(childLoginResponse.data).not.toHaveProperty('pin_hash');
      expect(childLoginResponse.data).not.toHaveProperty('salt');

      // Test invalid credentials
      await expect(axios.post(`${API_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      })).rejects.toThrow();

      await expect(axios.post(`${API_URL}/auth/login`, {
        username: 'testchild',
        pin: '000000'
      })).rejects.toThrow();

    } catch (error) {
      throw error;
    }
  });
});