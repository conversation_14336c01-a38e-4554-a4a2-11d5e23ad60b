import { createMocks } from 'node-mocks-http';
import handler from './quiz-config';
import prisma from '../../lib/prisma';
import { getServerSession } from 'next-auth/next';

// Mock the next-auth session
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock the Prisma client
jest.mock('../../lib/prisma', () => ({
  quizConfig: {
    findUnique: jest.fn(),
  },
}));

describe('Public Quiz Config API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Mock authenticated session
  const mockSession = {
    user: {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'PARENT',
    },
  };

  describe('GET method', () => {
    it('should return 401 if not authenticated', async () => {
      // Mock unauthenticated session
      (getServerSession as jest.Mock).mockResolvedValueOnce(null);

      const { req, res } = createMocks({
        method: 'GET',
        query: { mode: 'MASTERY' },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Unauthorized' });
    });

    it('should return 400 if mode is not provided', async () => {
      // Mock authenticated session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockSession);

      const { req, res } = createMocks({
        method: 'GET',
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Quiz mode is required' });
    });

    it('should return a specific quiz config when mode is specified', async () => {
      // Mock authenticated session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockSession);

      // Mock Prisma response
      const mockConfig = {
        id: 1,
        mode: 'MASTERY',
        numQuestions: 10,
        questionTypes: ['MULTIPLE_CHOICE', 'TRUE_FALSE'],
        allowTranslate: true,
        allowHints: true,
        allowAiTutor: true,
      };
      (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce(mockConfig);

      const { req, res } = createMocks({
        method: 'GET',
        query: { mode: 'MASTERY' },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(mockConfig);
    });

    it('should return 404 if the specified mode is not found', async () => {
      // Mock authenticated session
      (getServerSession as jest.Mock).mockResolvedValueOnce(mockSession);

      // Mock Prisma response (not found)
      (prisma.quizConfig.findUnique as jest.Mock).mockResolvedValueOnce(null);

      const { req, res } = createMocks({
        method: 'GET',
        query: { mode: 'NONEXISTENT' },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData())).toEqual({ message: 'Quiz config for mode NONEXISTENT not found' });
    });
  });

  it('should return 405 for unsupported methods', async () => {
    // Mock authenticated session
    (getServerSession as jest.Mock).mockResolvedValueOnce(mockSession);

    const { req, res } = createMocks({
      method: 'POST',
      query: { mode: 'MASTERY' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Method not allowed' });
  });
});
