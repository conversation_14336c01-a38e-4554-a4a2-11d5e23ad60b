import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Check for OpenRouter API key
  const openRouterApiKey = process.env.OPENROUTER_API_KEY;
  if (!openRouterApiKey) {
    return res.status(500).json({ message: 'OpenRouter API key not configured' });
  }

  // Determine if this is a user question or an incorrect answer explanation
  const { question, language, userQuestion } = req.body;

  if (!question || !language) {
    return res.status(400).json({ message: 'Missing required parameters: question and language are required' });
  }

  let prompt = '';

  // Handle user questions to AI tutor
  if (userQuestion) {
    // Get the question prompt based on the language
    const questionPrompt = language === 'zh'
      ? (question.promptZh || question.prompt?.zh || '')
      : (question.promptEn || question.prompt?.en || '');

    // Prepare prompt for user questions
    prompt = `
    The student is asking a question about this quiz question: "${questionPrompt}".
    The student's question is: "${userQuestion}".
    The language preference is ${language === 'zh' ? 'Chinese' : 'English'}.

    Answer the student's question helpfully and concisely. If the question is about the answer, provide hints but don't reveal the full answer.
    If your response contains keywords for the question or answer, bold and put the wording (follow the question language) in between brackets next to it. eg. protect itself [保护自己].
    Keep explanations clear, concise and age-appropriate for elementary school students.`;
  }
  // Handle incorrect answer explanations
  else {
    const { year, subject, correctAnswer, incorrectAnswer, topicEn } = req.body;

    if (!year || !subject || !correctAnswer || !incorrectAnswer || !topicEn) {
      return res.status(400).json({
        message: 'Missing required parameters for incorrect answer explanation'
      });
    }

    // Determine the question prompt based on the language
    const questionPrompt = language === 'zh'
      ? (question.promptZh || question.prompt?.zh || '')
      : (question.promptEn || question.prompt?.en || '');

    // Prepare prompt for incorrect answer explanations
    prompt = `
    The student answered incorrectly with "${incorrectAnswer}" but the correct answer is "${correctAnswer}".
    The question was: "${questionPrompt}".
    The subject of the question is "${subject}" and the topic is "${topicEn}".
    Breakdown the question for the student and share a hint on what may be the correct answer but do not reveal the answer.
    Your response will be in markdown.
    If your response contains keywords for the question or answer, bold and put the wording (follow the question language) in between brackets next to it. eg. protect itself [保护自己].
    Keep explanations clear, concise and age-appropriate for year ${year} SJKC students.`;
  }
  console.log('Prompt:', prompt);

  // Set the appropriate system prompt based on the request type
  const systemPrompt = userQuestion
    ? 'You are a friendly AI tutor helping students with their questions. Provide clear, concise explanations that are appropriate for elementary school students. Be helpful but avoid giving away answers directly.'
    : 'You are a tutor helping students who are answering a quiz. You are strict, concise but helpful in wanting the student to understand the subject';

  // Query OpenRouter API
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openRouterApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      //model: 'qwen/qwen3-30b-a3b:free', // Default model, can be configured
      model: 'google/gemini-2.0-flash-exp:free',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt }
      ],
      temperature: 0.7,
      max_tokens: 512
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    return res.status(500).json({
      message: 'LLM query failed',
      error: errorData.error?.message || 'Unknown error'
    });
  }

  const data = await response.json();
  const explanation = data.choices?.[0]?.message?.content || 'Failed to get explanation';
  const hints = 'No hints available'; // OpenRouter response doesn't include hints by default

  // Log the AI tutor interaction
  try {
    await prisma.aiTutorLog.create({
      data: {
        questionId: question.id || 0,
        childId: 1, // Default for testing
        userQuestion: userQuestion || '',
        aiResponse: explanation,
        language,
      },
    });
  } catch (logError) {
    console.error('Error logging AI tutor interaction:', logError);
    // Continue even if logging fails
  }

  res.status(200).json({ explanation, hints });

}
