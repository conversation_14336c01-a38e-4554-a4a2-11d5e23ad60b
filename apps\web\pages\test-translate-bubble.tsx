import React, { useState } from 'react';
import Head from 'next/head';
import { useTranslateBubble } from '../hooks/useTranslateBubble';
import { QuizProvider } from '../components/quiz/QuizContext';
import AiTutorPanel from '../components/quiz/AiTutorPanel';

// Sample question for testing
const sampleQuestion = {
  id: 1,
  promptEn: 'What is photosynthesis?',
  promptZh: '什么是光合作用？',
  promptMs: 'Apakah fotosintesis?',
  originalLanguage: 'ZH',
  type: 'MULTIPLE_CHOICE',
  choices: [
    { key: 'A', textEn: 'A process where plants make food using sunlight', textZh: '植物利用阳光制造食物的过程' },
    { key: 'B', textEn: 'A process where animals digest food', textZh: '动物消化食物的过程' },
    { key: 'C', textEn: 'A type of plant disease', textZh: '一种植物疾病' },
    { key: 'D', textEn: 'A chemical reaction in the atmosphere', textZh: '大气中的化学反应' }
  ],
  answer: 'A',
  topic: 'Science'
};

export default function TestTranslateBubble() {
  const [displayLanguage, setDisplayLanguage] = useState<'en' | 'zh' | 'ms'>('en');
  
  // Toggle language
  const toggleLanguage = () => {
    setDisplayLanguage(prev => prev === 'en' ? 'zh' : prev === 'zh' ? 'ms' : 'en');
  };
  
  return (
    <div className="min-h-screen bg-gray-100">
      <Head>
        <title>Test Translate Bubble</title>
      </Head>
      
      <main className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-4">Translate Bubble Test Page</h1>
        
        <div className="mb-4">
          <button 
            onClick={toggleLanguage}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            {displayLanguage === 'en' ? 'Switch to Chinese' : 
             displayLanguage === 'zh' ? 'Switch to Malay' : 'Switch to English'}
          </button>
        </div>
        
        <QuizProvider initialQuestions={[sampleQuestion]}>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="bg-white p-4 rounded-lg shadow-md flex-1">
              <h2 className="text-xl font-semibold mb-2">
                {displayLanguage === 'en' ? 'Test Content' : 
                 displayLanguage === 'zh' ? '测试内容' : 'Kandungan Ujian'}
              </h2>
              
              <div className="mb-4">
                <p className="font-bold">
                  {displayLanguage === 'en' ? 'Instructions:' : 
                   displayLanguage === 'zh' ? '说明：' : 'Arahan:'}
                </p>
                <p>
                  {displayLanguage === 'en' 
                    ? 'Select some text below and click the translate button that appears.' 
                    : displayLanguage === 'zh'
                      ? '选择下面的一些文本，然后点击出现的翻译按钮。'
                      : 'Pilih beberapa teks di bawah dan klik butang terjemahan yang muncul.'}
                </p>
              </div>
              
              <div className="mb-4 p-4 border border-gray-300 rounded">
                <p className="mb-2" id="test-text">
                  {displayLanguage === 'en' 
                    ? 'Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with the help of chlorophyll.' 
                    : displayLanguage === 'zh'
                      ? '光合作用是绿色植物和一些其他生物利用阳光在叶绿素的帮助下合成食物的过程。'
                      : 'Fotosintesis adalah proses di mana tumbuhan hijau dan beberapa organisma lain menggunakan cahaya matahari untuk mensintesis makanan dengan bantuan klorofil.'}
                </p>
              </div>
              
              <div className="mb-4">
                <button 
                  onClick={() => {
                    // Manually trigger a translation
                    const translationEvent = new CustomEvent('translationComplete', {
                      detail: {
                        translatedText: 'Photosynthesis is the process by which green plants and some other organisms use sunlight to synthesize foods with the help of chlorophyll.',
                        originalText: '光合作用是绿色植物和一些其他生物利用阳光在叶绿素的帮助下合成食物的过程。'
                      }
                    });
                    window.dispatchEvent(translationEvent);
                  }}
                  className="bg-green-500 text-white px-4 py-2 rounded"
                >
                  {displayLanguage === 'en' ? 'Simulate Translation Event' : 
                   displayLanguage === 'zh' ? '模拟翻译事件' : 'Simulasi Acara Terjemahan'}
                </button>
              </div>
              
              <TranslationBubbleTester displayLanguage={displayLanguage} />
            </div>
            
            <div className="bg-white p-4 rounded-lg shadow-md md:w-2/5">
              <h2 className="text-xl font-semibold mb-2">
                {displayLanguage === 'en' ? 'AI Tutor Chat' : 
                 displayLanguage === 'zh' ? 'AI 导师聊天' : 'Sembang Tutor AI'}
              </h2>
              
              <AiTutorPanel 
                displayLanguage={displayLanguage === 'ms' ? 'en' : displayLanguage} 
                visible={true} 
              />
            </div>
          </div>
        </QuizProvider>
      </main>
    </div>
  );
}

// Component to test the translation bubble
function TranslationBubbleTester({ displayLanguage }: { displayLanguage: 'en' | 'zh' | 'ms' }) {
  const { isVisible, position, handleTranslate, handleClose } = useTranslateBubble(true, sampleQuestion);
  
  return (
    <div className="relative">
      {isVisible && position && (
        <div 
          className="absolute bg-white shadow-lg rounded-lg p-2 z-10 transform -translate-x-1/2"
          style={{ 
            top: `${position.top}px`, 
            left: `${position.left}px` 
          }}
        >
          <button 
            onClick={handleTranslate}
            className="bg-blue-500 text-white px-3 py-1 rounded text-sm"
          >
            {displayLanguage === 'en' ? 'Translate' : 
             displayLanguage === 'zh' ? '翻译' : 'Terjemah'}
          </button>
          <button 
            onClick={handleClose}
            className="ml-2 text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
      )}
    </div>
  );
}
