import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { Prisma } from '@prisma/client';

interface RawQueryResult {
  id: number;
  questionId: string;
  type: string;
  promptEn: string;
  promptZh: string;
  topic: string;
  choice_id: number | null;
  choice_key: string | null;
  choice_en: string | null;
  choice_zh: string | null;
  answer_key: string | null;
  answer_en: string | null;
  answer_zh: string | null;
  explanation_en: string | null;
  explanation_zh: string | null;
  subject: string; // Add subject
  unit: number; // Add unit
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'GET') {
    try {
      const { units, subject, year, random, count, types } = req.query;

      let questions: RawQueryResult[];

      if (random === 'true') {
        const limit = count ? Number(count) : 10; // Default to 10 random questions
        const typesArray = types ? (Array.isArray(types) ? types : [types]).map(type => type.toUpperCase()) : [];

        questions = await prisma.$queryRaw<RawQueryResult[]>(
          Prisma.sql`
            SELECT
              q.id, q."questionId", q.type, q."promptEn", q."promptZh", u."topicEn" as topic, -- Select u."topicEn" as topic
              c.id as choice_id, c.key as choice_key, c."textEn" as choice_en, c."textZh" as choice_zh,
              a.key as answer_key, a."textEn" as answer_en, a."textZh" as answer_zh,
              e."textEn" as explanation_en, e."textZh" as explanation_zh,
              s.name as subject, u."unitNumber" as unit
            FROM "TG_Question" q
            LEFT JOIN "TG_Choice" c ON c."questionId" = q.id
            LEFT JOIN "TG_ExplanationText" e ON e."questionId" = q.id
            LEFT JOIN "TG_Answer" a ON a."questionId" = q.id
            JOIN "TG_Unit" u ON q."unitId" = u.id
            JOIN "TG_Subject" s ON u."subjectId" = s.id
            WHERE q.id IN (
              SELECT q_sub.id
              FROM "TG_Question" q_sub
              WHERE q_sub."promptEn" IS NOT NULL
              AND q_sub."unitId" IS NOT NULL
              ${typesArray.length > 0 ? Prisma.sql`AND q_sub.type = ANY(${typesArray}::"QuestionType"[])` : Prisma.empty}
              ORDER BY RANDOM()
              LIMIT ${limit}
            );
          `
        );
      } else {
        // Existing logic for fetching questions by unit, subject, and year
        const unitNumbers = units ? (Array.isArray(units) ? units : [units]).map(Number) : [];
        const yearNumber = Number(year);
        const typesArray = types ? (Array.isArray(types) ? types : [types]).map(type => type.toUpperCase()) : [];


        questions = await prisma.$queryRaw<RawQueryResult[]>(
          Prisma.sql`
            SELECT
              q.id, q."questionId", q.type, q."promptEn", q."promptZh", u."topicEn" as topic, -- Select u."topicEn" as topic
              c.id as choice_id, c.key as choice_key, c."textEn" as choice_en, c."textZh" as choice_zh,
              a.key as answer_key, a."textEn" as answer_en, a."textZh" as answer_zh,
              e."textEn" as explanation_en, e."textZh" as explanation_zh,
              s.name as subject, u."unitNumber" as unit
            FROM "TG_Question" q
            LEFT JOIN "TG_Choice" c ON c."questionId" = q.id
            LEFT JOIN "TG_ExplanationText" e ON e."questionId" = q.id
            LEFT JOIN "TG_Answer" a ON a."questionId" = q.id
            JOIN "TG_Unit" u ON q."unitId" = u.id
            JOIN "TG_Subject" s ON u."subjectId" = s.id
            WHERE q.id IN (
              SELECT q_sub.id
              FROM "TG_Question" q_sub
              WHERE q_sub."unitId" IN (
                SELECT id FROM "TG_Unit"
                WHERE "unitNumber" = ANY(${unitNumbers})
                AND "subjectId" = (
                  SELECT id FROM "TG_Subject" WHERE name = ${subject}
                )
                AND "yearId" = (
                  SELECT id FROM "TG_Year" WHERE "yearNumber" = ${yearNumber}
                )
              )
              AND q_sub."promptEn" IS NOT NULL
              ${typesArray.length > 0 ? Prisma.sql`AND q_sub.type = ANY(${typesArray}::"QuestionType"[])` : Prisma.empty}
            )
            ORDER BY q."unitId" ASC;
          `
        );
      }

      // Group the results by question and aggregate choices
      const questionMap = new Map();
      const choicesMap = new Map<number, any[]>(); // Map to store choices by question ID

      questions.forEach(row => {
        if (!questionMap.has(row.id)) {
          questionMap.set(row.id, {
            id: row.questionId,
            type: row.type.toLowerCase(),
            prompt: {
              en: row.promptEn,
              zh: row.promptZh
            },
            choices: undefined, // Initialize choices as undefined
            answer: row.type === 'MULTIPLE_CHOICE' ? row.answer_key : { en: row.answer_en, zh: row.answer_zh },
            topic: row.topic, // Use the aliased topic
            explanation: {
              en: row.explanation_en,
              zh: row.explanation_zh
            },
            subject: row.subject,
            unit: row.unit
          });
          if (row.type === 'MULTIPLE_CHOICE') {
            choicesMap.set(row.id, []); // Initialize choices array for MC questions
          }
        }

        if (row.choice_id && row.type === 'MULTIPLE_CHOICE') {
          const choices = choicesMap.get(row.id);
          if (choices) {
            choices.push({
              key: row.choice_key,
              en: row.choice_en,
              zh: row.choice_zh
            });
          }
        }
      });

      // Assign aggregated choices to questions
      questionMap.forEach((question, questionId) => {
        if (question.type === 'multiple_choice') {
          question.choices = choicesMap.get(questionId);
        }
      });

      const formattedQuestions = Array.from(questionMap.values());
      res.status(200).json(formattedQuestions);
    } catch (error) {
      console.error('API error:', error);
      res.status(500).json({ error: 'Failed to load questions' });
    }
  } else if (req.method === 'POST') {
    try {
      const { units, subject, year, random, count, types, childId, quizType } = req.body;

      // Fetch the configured question limit, default to 5
      const limitSetting = await prisma.setting.findUnique({ where: { key: 'quizQuestionLimit' } });
      let configuredLimit = limitSetting?.value ? parseInt(limitSetting.value, 10) : 5; // Use let
      if (isNaN(configuredLimit) || configuredLimit <= 0) {
        console.warn(`Invalid quizQuestionLimit setting found: ${limitSetting?.value}. Defaulting to 5.`);
        configuredLimit = 5; // Fallback to default if parsing fails or value is invalid
      }


      // Add validation for subject
      if (!subject || typeof subject !== 'string') {
        return res.status(400).json({ error: 'Missing or invalid subject in request body' });
      }

      let questions: RawQueryResult[];

      if (random) { // Assuming random is a boolean in the request body
        // Use configuredLimit as the default if count is not provided
        const limit = count ? Number(count) : configuredLimit;
        const typesArray = types ? (Array.isArray(types) ? types : [types]).map((type: string) => type.toUpperCase()) : [];

        questions = await prisma.$queryRaw<RawQueryResult[]>(
          Prisma.sql`
            SELECT
              q.id, q."questionId", q.type, q."promptEn", q."promptZh", u."topicEn" as topic,
              c.id as choice_id, c.key as choice_key, c."textEn" as choice_en, c."textZh" as choice_zh,
              a.key as answer_key, a."textEn" as answer_en, a."textZh" as answer_zh,
              e."textEn" as explanation_en, e."textZh" as explanation_zh,
              s.name as subject, u."unitNumber" as unit
            FROM "TG_Question" q
            LEFT JOIN "TG_Choice" c ON c."questionId" = q.id
            LEFT JOIN "TG_ExplanationText" e ON e."questionId" = q.id
            LEFT JOIN "TG_Answer" a ON a."questionId" = q.id
            JOIN "TG_Unit" u ON q."unitId" = u.id
            JOIN "TG_Subject" s ON u."subjectId" = s.id
            WHERE q.id IN (
              SELECT q_sub.id
              FROM "TG_Question" q_sub
              WHERE q_sub."promptEn" IS NOT NULL
              AND q_sub."unitId" IS NOT NULL
              ${typesArray.length > 0 ? Prisma.sql`AND q_sub.type = ANY(${typesArray}::"QuestionType"[])` : Prisma.empty}
              ORDER BY RANDOM()
              LIMIT ${limit}
            );
          `
        );
      } else {
        const unitNumbers = units ? (Array.isArray(units) ? units : [units]).map(Number) : [];
        const yearNumber = Number(year);
        const typesArray = types ? (Array.isArray(types) ? types : [types]).map((type: string) => type.toUpperCase()) : [];

        questions = await prisma.$queryRaw<RawQueryResult[]>(
          Prisma.sql`
            SELECT
              q.id, q."questionId", q.type, q."promptEn", q."promptZh", u."topicEn" as topic,
              c.id as choice_id, c.key as choice_key, c."textEn" as choice_en, c."textZh" as choice_zh,
              a.key as answer_key, a."textEn" as answer_en, a."textZh" as answer_zh,
              e."textEn" as explanation_en, e."textZh" as explanation_zh,
              s.name as subject, u."unitNumber" as unit
            FROM "TG_Question" q
            LEFT JOIN "TG_Choice" c ON c."questionId" = q.id
            LEFT JOIN "TG_ExplanationText" e ON e."questionId" = q.id
            LEFT JOIN "TG_Answer" a ON a."questionId" = q.id
            JOIN "TG_Unit" u ON q."unitId" = u.id
            JOIN "TG_Subject" s ON u."subjectId" = s.id
            WHERE q.id IN (
              SELECT q_sub.id
              FROM "TG_Question" q_sub
              WHERE q_sub."unitId" IN (
                SELECT id FROM "TG_Unit"
                WHERE "unitNumber" = ANY(${unitNumbers})
                AND "subjectId" = (
                  SELECT id FROM "TG_Subject" WHERE name = ${subject}
                )
                AND "yearId" = (
                  SELECT id FROM "TG_Year" WHERE "yearNumber" = ${yearNumber}
                )
              )
              AND q_sub."promptEn" IS NOT NULL
              ${typesArray.length > 0 ? Prisma.sql`AND q_sub.type = ANY(${typesArray}::"QuestionType"[])` : Prisma.empty}
              ORDER BY RANDOM() -- Order randomly before limiting
              LIMIT ${configuredLimit} -- Apply limit here
            )
            ORDER BY q."unitId" ASC; -- Keep outer order if needed, though questions are already limited randomly
          `
        );
      }

      // Extract unique question IDs from the potentially duplicated raw results
      const uniqueQuestionIds = [...new Set(questions.map(q => q.id))];

      // Find subject and unit IDs
      const subjectRecord = await prisma.subject.findUnique({ where: { name: subject } });
      const unitRecord = units && units.length > 0 ? await prisma.unit.findFirst({
        where: {
          unitNumber: Number(units[0]), // Assuming one unit for simplicity in attempt creation
          subjectId: subjectRecord?.id,
          yearId: Number(year) // Assuming year is passed
        }
      }) : null;


      // Create a new QuizAttempt record
      const quizAttempt = await prisma.quizAttempt.create({
        data: {
          childId: Number(childId),
          subjectId: subjectRecord?.id || 0, // Provide a default or handle error
          unitId: unitRecord?.id, // Can be null for random quizzes across units
          questionIds: uniqueQuestionIds.map(String), // Store unique IDs as array of strings
          currentQuestionIndex: 0,
          // Set the quiz type based on the quizType parameter
          // @ts-ignore - quizType is a valid field in the database but TypeScript doesn't know about it yet
          quizType: quizType?.toUpperCase() === 'MASTERY' ? 'MASTERY' :
                   quizType?.toUpperCase() === 'QUICK' ? 'QUICK' : 'TEST',
        },
      });

      // Group the results by question and aggregate choices
      const questionMap = new Map();
      const choicesMap = new Map<number, any[]>();

      questions.forEach(row => {
        if (!questionMap.has(row.id)) {
          questionMap.set(row.id, {
            id: row.questionId,
            type: row.type.toLowerCase(),
            prompt: {
              en: row.promptEn,
              zh: row.promptZh
            },
            choices: undefined,
            answer: row.type === 'MULTIPLE_CHOICE' ? row.answer_key : { en: row.answer_en, zh: row.answer_zh },
            topic: row.topic,
            explanation: {
              en: row.explanation_en,
              zh: row.explanation_zh
            },
            subject: row.subject,
            unit: row.unit
          });
          if (row.type === 'MULTIPLE_CHOICE') {
            choicesMap.set(row.id, []);
          }
        }

        if (row.choice_id && row.type === 'MULTIPLE_CHOICE') {
          const choices = choicesMap.get(row.id);
          if (choices) {
            choices.push({
              key: row.choice_key,
              en: row.choice_en,
              zh: row.choice_zh
            });
          }
        }
      });

      // Assign aggregated choices to questions
      questionMap.forEach((question, questionId) => {
        if (question.type === 'multiple_choice') {
          question.choices = choicesMap.get(questionId);
        }
      });

      const formattedQuestions = Array.from(questionMap.values());
      res.status(200).json({ questions: formattedQuestions, quizAttemptId: quizAttempt.id });
    } catch (error) {
      console.error('API error:', error);
      res.status(500).json({ error: 'Failed to generate and save quiz' });
    }
  }
  else {
    res.status(405).json({ error: 'Method not allowed' });
  }
}
