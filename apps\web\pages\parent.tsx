import { useEffect } from 'react';
import { useRouter } from 'next/router';
import ParentPortal from '../components/ParentPortal';
import { useSession } from 'next-auth/react';

export default function ParentPage() {
  const { data: session, status } = useSession(); // Use useSession hook
  const router = useRouter();

  useEffect(() => {
    // Redirect to login if not authenticated after loading
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // Show a loading state while session is loading
  if (status === 'loading') {
    return <div>Loading...</div>; // Or a more sophisticated loading component
  }

  // If authenticated, render the ParentPortal
  if (status === 'authenticated') {
    // Optional: Add a check here if session.user?.role is not 'PARENT' and redirect if necessary
    // if (session.user?.role !== 'PARENT') {
    //   router.push('/'); // Redirect to a different page if not a parent
    //   return null;
    // }
    return <ParentPortal />;
  }

  // If unauthenticated, useEffect will handle redirection, so return null or a fallback
  return null;
}
