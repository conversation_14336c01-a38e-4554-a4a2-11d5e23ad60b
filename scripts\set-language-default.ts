import { PrismaClient, Language, TranslationStatus } from '@prisma/client';

// Create a new PrismaClient instance
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Starting to back-fill language metadata for existing questions...');
    
    // Update all existing questions to set default language metadata
    const result = await prisma.question.updateMany({
      data: {
        originalLanguage: Language.ZH,
        translationState: TranslationStatus.PARTIAL
      }
    });
    
    console.log(`Back-fill complete. Updated ${result.count} questions.`);
  } catch (error) {
    console.error('Error during back-fill:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
