"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.start = void 0;
const fastify_1 = __importDefault(require("fastify"));
const cors_1 = __importDefault(require("@fastify/cors"));
const swagger_1 = __importDefault(require("@fastify/swagger"));
const swagger_ui_1 = __importDefault(require("@fastify/swagger-ui"));
const db_1 = require("@quiz/db");
const app = (0, fastify_1.default)({
    logger: {
        level: process.env.LOG_LEVEL || 'info',
        transport: process.env.NODE_ENV === 'development'
            ? {
                target: 'pino-pretty',
                options: {
                    translateTime: 'HH:MM:ss',
                    colorize: true,
                    ignore: 'pid,hostname'
                }
            }
            : undefined
    }
});
// Register CORS plugin
app.register(cors_1.default, {
    origin: true,
    credentials: true
});
// Register Swagger plugin
app.register(swagger_1.default, {
    openapi: {
        openapi: '3.0.0',
        info: {
            title: 'My Quiz App API',
            description: 'API documentation for the Quiz Application',
            version: '1.0.0',
        },
        servers: [
            {
                url: 'http://localhost:4000',
                description: 'Development server'
            }
        ],
        tags: [
            { name: 'health', description: 'Health check endpoints' },
            { name: 'quiz', description: 'Quiz related endpoints' }
        ],
    }
});
// Register Swagger UI plugin
app.register(swagger_ui_1.default, {
    routePrefix: '/documentation',
    uiConfig: {
        docExpansion: 'full',
        deepLinking: false
    },
    uiHooks: {
        onRequest: function (request, reply, next) { next(); },
        preHandler: function (request, reply, next) { next(); }
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
    transformSpecification: (swaggerObject, request, reply) => { return swaggerObject; },
    transformSpecificationClone: true
});
// Global error handler
app.setErrorHandler((error, request, reply) => {
    app.log.error(error, 'Unhandled error occurred');
    const statusCode = error.statusCode || 500;
    const response = process.env.NODE_ENV === 'production'
        ? { message: 'Internal server error' }
        : {
            message: error.message,
            stack: error.stack,
            statusCode
        };
    reply.status(statusCode).send(response);
});
// Health check route
app.get('/health', {
    schema: {
        tags: ['health'],
        summary: 'Health check endpoint',
        description: 'Returns the health status of the API server',
        response: {
            200: {
                type: 'object',
                properties: {
                    status: { type: 'string', example: 'ok' },
                    timestamp: { type: 'string', format: 'date-time' },
                    service: { type: 'string', example: 'quiz-api' }
                }
            }
        }
    }
}, async () => ({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'quiz-api'
}));
// Quiz attempt route
app.get('/quiz/:id', {
    schema: {
        tags: ['quiz'],
        summary: 'Get quiz attempt by ID',
        description: 'Retrieves a quiz attempt with questions and child language preferences',
        params: {
            type: 'object',
            properties: {
                id: { type: 'string', description: 'Quiz attempt ID' }
            },
            required: ['id']
        },
        response: {
            200: {
                type: 'object',
                properties: {
                    quizAttempt: {
                        type: 'object',
                        properties: {
                            id: { type: 'number' },
                            questionIds: { type: 'array', items: { type: 'string' } },
                            child: {
                                type: 'object',
                                properties: {
                                    quizLanguage: { type: 'string' },
                                    menuLanguage: { type: 'string' },
                                    showDualLanguage: { type: 'boolean' }
                                }
                            }
                        }
                    },
                    questions: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                id: { type: 'number' },
                                promptEn: { type: 'string' },
                                promptZh: { type: 'string' },
                                choices: { type: 'array' },
                                answer: { type: 'object' },
                                explanation: { type: 'object' }
                            }
                        }
                    },
                    childLanguagePreferences: {
                        type: 'object',
                        properties: {
                            quizLanguage: { type: 'string' },
                            menuLanguage: { type: 'string' },
                            showDualLanguage: { type: 'boolean' }
                        }
                    }
                }
            },
            400: {
                type: 'object',
                properties: {
                    message: { type: 'string', example: 'Invalid attempt ID' }
                }
            },
            404: {
                type: 'object',
                properties: {
                    message: { type: 'string', example: 'Quiz attempt not found' }
                }
            },
            500: {
                type: 'object',
                properties: {
                    message: { type: 'string', example: 'Internal server error' }
                }
            }
        }
    }
}, async (request, reply) => {
    const { id } = request.params;
    try {
        const attemptId = Number(id);
        if (isNaN(attemptId)) {
            return reply.code(400).send({ message: 'Invalid attempt ID' });
        }
        const attempt = await db_1.prisma.quizAttempt.findUnique({
            where: { id: attemptId },
            include: {
                child: {
                    select: {
                        quizLanguage: true,
                        menuLanguage: true,
                        showDualLanguage: true
                    }
                }
            }
        });
        if (!attempt) {
            return reply.code(404).send({ message: 'Quiz attempt not found' });
        }
        // Get the actual questions based on questionIds
        const questions = await db_1.prisma.question.findMany({
            where: {
                id: {
                    in: attempt.questionIds.map(id => Number(id)),
                },
            },
            include: {
                choices: true,
                answer: true,
                explanation: true,
                unit: {
                    select: {
                        unitNumber: true,
                        topicEn: true,
                        topicZh: true,
                    }
                },
                subject: {
                    select: {
                        name: true,
                    }
                },
                year: {
                    select: {
                        yearNumber: true,
                    }
                }
            },
        });
        // Transform the response to match the expected format
        const childLanguagePreferences = {
            quizLanguage: attempt.child?.quizLanguage || 'ZH',
            menuLanguage: attempt.child?.menuLanguage || 'EN',
            showDualLanguage: attempt.child?.showDualLanguage || false
        };
        return {
            quizAttempt: attempt,
            questions,
            childLanguagePreferences
        };
    }
    catch (error) {
        app.log.error(error);
        return reply.code(500).send({ message: 'Internal server error' });
    }
});
const start = async () => {
    try {
        await app.listen({ port: 4000, host: '0.0.0.0' });
        app.log.info('API server listening on port 4000');
    }
    catch (err) {
        app.log.error(err);
        process.exit(1);
    }
};
exports.start = start;
// Start the server if this file is run directly
if (require.main === module) {
    (0, exports.start)();
}
exports.default = app;
//# sourceMappingURL=server.js.map