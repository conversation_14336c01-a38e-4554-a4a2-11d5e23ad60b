export const buildPrompt = (ctx:{
  num:number,
  types:string[],
  year:number,
  subject:string,
  unit?:string,
  notes:string,
  language?:string,
  /** Optional: force a TP distribution, e.g. [2,3,3,4] for 4 Qs */
  tpDistribution?:number[]
}) => `
Generate ${ctx.num} ${ctx.types.join(', ')} questions for Year ${ctx.year} ${ctx.subject}${ctx.unit ? ' ('+ctx.unit+')' : ''}.

Primary language: ${ctx.language || 'ZH'} (${ctx.language === 'EN' ? 'English' : ctx.language === 'MS' ? 'Malay' : 'Chinese'})
${ctx.language === 'EN' ? 'Generate questions primarily in English. The promptEn field should contain the main question content, and promptZh should be the Chinese translation.' :
  ctx.language === 'MS' ? 'Generate questions primarily in Malay. The promptEn field should still be in English, but focus on making the Malay content in promptZh accurate and natural.' :
  'Generate questions primarily in Chinese. The promptZh field should contain the main question content, and promptEn should be the English translation.'}

IMPORTANT: Return ONLY a raw JSON array without any markdown formatting, explanation, or code blocks.
DO NOT use markdown code blocks around your response. The response must be valid JSON that can be parsed directly.

Each question **MUST** include:
- "schemaVersion" = "1.2"  (Semantic version bump)
- "tpLevel" *(integer 1–6)* indicating difficulty according to Tahap Penguasaan (TP) rubric:
  1 = recall, 2 = explain/understand, 3 = apply, 4 = analyse/procedural, 5 = evaluate/adapt, 6 = create/innovate.
- "keywords" object { en:[], zh:[], ms:[] } for highlightable terms per language.

${ctx.tpDistribution 
  ? `STRICT REQUIREMENT: You MUST use exactly these TP levels in this order: [${ctx.tpDistribution.join(', ')}]. The first question must have tpLevel ${ctx.tpDistribution[0]}, the second question must have tpLevel ${ctx.tpDistribution[1]}, and so on.` 
  : 'Produce a balanced mix of questions across TP levels 1-6.'}

Allowed types and mandatory fields:

################################################################
# MULTIPLE_CHOICE  (text)
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"MULTIPLE_CHOICE",
  "tpLevel":3,
  "promptEn":"...",
  "promptZh":"...",
  "promptMs":"...",
  "choices":[{"key":"A","textEn":"...","textZh":"...","textMs":"..."}],
  "answer":{"key":"A"},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["...","..."],
    "zh":["...","..."],
    "ms":["...","..."]
  }
}

# MULTIPLE_CHOICE_IMAGE
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"MULTIPLE_CHOICE_IMAGE",
  "tpLevel":2,
  "promptEn":"...",
  "promptZh":"...",
  "promptMs":"...",
  "choices":[{"key":"A","textEn":"...","textZh":"...","textMs":"...","mediaUrl":"https://..."}],
  "answer":{"key":"A"},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["...","..."],
    "zh":["...","..."],
    "ms":["...","..."]
  }
}

# PICTURE_PROMPT
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"PICTURE_PROMPT",
  "tpLevel":2,
  "promptEn":"...",
  "promptZh":"...",
  "promptMs":"...",
  "spec":{"promptImageUrl":"https://..."},
  "choices":[{"key":"A","textEn":"...","textZh":"...","textMs":"..."}],
  "answer":{"key":"A"},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["...","..."],
    "zh":["...","..."],
    "ms":["...","..."]
  }
}

# TRUE_FALSE
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"TRUE_FALSE",
  "tpLevel":1,
  "promptEn":"...",
  "promptZh":"...",
  "promptMs":"...",
  "answer":{"key":"TRUE"},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["...","..."],
    "zh":["...","..."],
    "ms":["...","..."]
  }
}

# SHORT_ANSWER
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"SHORT_ANSWER",
  "tpLevel":2,
  "promptEn":"...",
  "promptZh":"...",
  "promptMs":"...",
  "answer":{"textEn":"...","textZh":"...","textMs":"..."},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["...","..."],
    "zh":["...","..."],
    "ms":["...","..."]
  }
}

# LONG_ANSWER
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"LONG_ANSWER",
  "tpLevel":5,
  "promptEn":"...",
  "promptZh":"...",
  "promptMs":"...",
  "answer":{"textEn":"model answer","textZh":"...","textMs":"..."},
  "explanation":{"textEn":"key points: ...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["...","..."],
    "zh":["...","..."],
    "ms":["...","..."]
  }
}

# FILL_IN_THE_BLANK
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"FILL_IN_THE_BLANK",
  "tpLevel":1,
  "promptEn":"The ___ orbits the Sun.",
  "promptZh":"__ 绕着太阳运行。",
  "promptMs":"___ mengelilingi Matahari.",
  "spec":{"blanks":[{"index":0,"correct":"Earth"}]},
  "answer":{"textEn":"Earth","textZh":"地球","textMs":"Bumi"},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["Earth","orbits"],
    "zh":["地球","运行"],
    "ms":["Bumi","mengelilingi"]
  }
}

# MATCHING
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"MATCHING",
  "tpLevel":3,
  "promptEn":"Match the animals to their homes.",
  "promptZh":"将动物与它们的家匹配。",
  "promptMs":"Padankan haiwan dengan rumah mereka.",
  "spec":{"pairs":[
    {"left":"Bee","leftZh":"蜜蜂","leftMs":"Lebah","right":"Hive","rightZh":"蜂巢","rightMs":"Sarang Lebah"},
    {"left":"Bird","leftZh":"鸟","leftMs":"Burung","right":"Nest","rightZh":"鸟巢","rightMs":"Sarang"},
    {"left":"Fish","leftZh":"鱼","leftMs":"Ikan","right":"Ocean","rightZh":"海洋","rightMs":"Lautan"},
    {"left":"Fox","leftZh":"狐狸","leftMs":"Rubah","right":"Den","rightZh":"狐狸窝","rightMs":"Lubang"}
  ]},
  "answer":{"answerSpec":{"pairs":[...] }},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["match","animals"],
    "zh":["匹配","动物"],
    "ms":["padankan","haiwan"]
  }
}

# SEQUENCING
{
  "schemaVersion":"1.2",
  "id":"uuid",
  "type":"SEQUENCING",
  "tpLevel":4,
  "promptEn":"Arrange the life-cycle stages.",
  "promptZh":"安排生命周期的各个阶段。",
  "promptMs":"Susun peringkat kitaran hidup.",
  "spec":{
    "items":[
      {"textEn":"Egg","textZh":"卵","textMs":"Telur"},
      {"textEn":"Larva","textZh":"幼虫","textMs":"Larva"},
      {"textEn":"Pupa","textZh":"蛹","textMs":"Pupa"},
      {"textEn":"Adult","textZh":"成虫","textMs":"Dewasa"}
    ]
  },
  "answer":{"answerSpec":{"order":[0,1,2,3]}},
  "explanation":{"textEn":"...","textZh":"...","textMs":"..."},
  "keywords":{
    "en":["life-cycle","sequence"],
    "zh":["生命周期","顺序"],
    "ms":["kitaran hidup","urutan"]
  }
}
################################################################

CONTEXT:
${ctx.notes}
`;

