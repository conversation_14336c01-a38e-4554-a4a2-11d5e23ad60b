# Testing the Question Generation API

This document outlines the testing approach for the Question Generation API and provides guidance on how to run and extend the tests.

## Overview

The Question Generation API (`/api/admin/generate-questions`) is responsible for:

1. Validating input parameters
2. Creating a generation batch in the database
3. Creating a queue file for the worker to process
4. Returning a success response with the batch ID

## Test Files

- `test/generate-questions.test.ts`: Tests for the API payload validation and integration

## Running the Tests

To run the tests:

```bash
npm test -- test/generate-questions.test.ts
```

## Test Coverage

The tests cover the following scenarios:

### Payload Validation

- Valid payload structure
- Missing required fields (yearId, subjectId, questionTypes, model)
- Number of questions exceeding maximum (100)
- Empty questionTypes array
- Default values for optional fields (numQuestions, provider, language)

### Queue File Structure

- Correct structure of the queue file JSON

### API Integration

- Successful API call with valid payload
- Error handling for invalid payloads
- Error handling for server errors

## Extending the Tests

### Adding New Test Cases

To add new test cases:

1. Add a new test function in the appropriate describe block
2. Mock the necessary responses
3. Make assertions about the expected behavior

Example:

```typescript
test('new test case', () => {
  // Setup
  const testData = { /* ... */ };
  
  // Execute
  const result = someFunction(testData);
  
  // Assert
  expect(result).toEqual(expectedResult);
});
```

### Testing the Component

The `QuestionGenerator` component test (`test/components/admin/QuestionGenerator.test.tsx`) attempts to test the React component that uses this API. However, it requires additional Jest configuration to handle JSX and TypeScript properly.

To make this test work, you would need to:

1. Configure Jest to handle TypeScript and React
2. Set up the necessary mocks for the component's dependencies
3. Ensure the test environment can render React components

## Troubleshooting

### Common Issues

1. **Missing required fields**: Ensure all required fields are provided in the payload
2. **Invalid question types**: Make sure questionTypes contains valid QuestionType enum values
3. **No notes found**: Verify that notes exist for the specified year, subject, and unit

### Debugging API Issues

When the API returns an error:

1. Check the error message in the response
2. Look for error logs in the server console
3. Verify the request payload matches the expected format

## Example Valid Payload

```json
{
  "yearId": 1,
  "subjectId": 2,
  "unitId": 3,
  "numQuestions": 5,
  "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE"],
  "provider": "openrouter",
  "model": "anthropic/claude-3-opus:beta",
  "language": "ZH",
  "tpDistribution": [1, 2, 3, 4, 5]
}
```

## Next Steps

1. Improve component testing by configuring Jest for React and TypeScript
2. Add integration tests for the question generation worker
3. Add end-to-end tests that verify the entire question generation flow
