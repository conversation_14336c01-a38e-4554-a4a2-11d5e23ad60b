import { useState, useEffect } from 'react';
import { QuestionType, Language } from '@prisma/client';
import useSWR from 'swr';
import TokenUsageSummary from './TokenUsageSummary';
import BatchesTable from './BatchesTable';

// Define types
interface Year {
  id: number;
  yearNumber: number;
}

interface Subject {
  id: number;
  name: string;
}

interface Unit {
  id: number;
  unitNumber: number;
  topicEn: string;
  topicZh: string;
}

interface Batch {
  id: number;
  adminName: string;
  yearNumber: number;
  subjectName: string;
  unitNumber?: number;
  unitTopic?: string;
  questionTypes: QuestionType[];
  numQuestions: number;
  modelUsed: string;
  language: Language;
  status: string;
  createdAt: string;
  completedAt?: string;
  questionCount: number;
  noteCount: number;
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
}

// Fetcher function for SWR
const fetcher = (url: string) => fetch(url).then(res => res.json());

export default function QuestionGenerator() {
  // Form state
  const [yearId, setYearId] = useState<number | ''>('');
  const [subjectId, setSubjectId] = useState<number | ''>('');
  const [unitId, setUnitId] = useState<number | ''>('');
  const [numQuestions, setNumQuestions] = useState<number>(10);
  const [selectedQuestionTypes, setSelectedQuestionTypes] = useState<QuestionType[]>([
    QuestionType.MULTIPLE_CHOICE
  ]);
  const [provider, setProvider] = useState<string>('openrouter'); // AI provider (openrouter or gemini)
  const [model, setModel] = useState<string>('google/gemini-2.0-flash-exp:free');

  // Update model when provider changes
  useEffect(() => {
    // Only update the model if the provider changes
    if (provider === 'gemini' && !model.startsWith('gemini-')) {
      setModel('gemini-1.5-pro'); // Default Gemini model
    } else if (provider === 'openrouter' && model.startsWith('gemini-')) {
      setModel('google/gemini-2.0-flash-exp:free'); // Default OpenRouter model
    }
  }, [provider, model]);
  const [language, setLanguage] = useState<Language>(Language.ZH); // Default to Chinese
  const [tpDistribution, setTpDistribution] = useState<string>(''); // TP level distribution

  // Data state
  const [years, setYears] = useState<Year[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [toast, setToast] = useState<{ show: boolean; message: string; type: 'success' | 'error' }>({
    show: false,
    message: '',
    type: 'success'
  });

  // Fetch batches using SWR (only need the mutate function)
  const { mutate: refreshBatches } = useSWR<Batch[]>(
    '/api/admin/batches',
    fetcher,
    { refreshInterval: 10000 } // Refresh every 10 seconds
  );

  // Fetch years
  useEffect(() => {
    const fetchYears = async () => {
      try {
        const response = await fetch('/api/years');
        if (response.ok) {
          const data = await response.json();
          setYears(data);
        }
      } catch (error) {
        console.error('Error fetching years:', error);
      }
    };
    fetchYears();
  }, []);

  // Fetch subjects
  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const response = await fetch('/api/subjects');
        if (response.ok) {
          const data = await response.json();
          setSubjects(data);
        }
      } catch (error) {
        console.error('Error fetching subjects:', error);
      }
    };
    fetchSubjects();
  }, []);

  // Fetch units when subject changes
  useEffect(() => {
    const fetchUnits = async () => {
      if (!subjectId) {
        setUnits([]);
        return;
      }

      try {
        const response = await fetch(`/api/topics?subjectId=${subjectId}`);
        if (response.ok) {
          const data = await response.json();
          setUnits(data);
        }
      } catch (error) {
        console.error('Error fetching units:', error);
      }
    };
    fetchUnits();
  }, [subjectId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!yearId || !subjectId || selectedQuestionTypes.length === 0 || !model) {
      setToast({
        show: true,
        message: 'Please fill in all required fields',
        type: 'error'
      });
      setTimeout(() => setToast({ ...toast, show: false }), 3000);
      return;
    }

    setIsLoading(true);

    try {
      // Parse TP distribution if provided
      let parsedTpDistribution: number[] | undefined = undefined;
      if (tpDistribution.trim()) {
        parsedTpDistribution = tpDistribution.split(',')
          .map(level => parseInt(level.trim()))
          .filter(level => !isNaN(level) && level >= 1 && level <= 6);

        // Validate that we have the right number of levels
        if (parsedTpDistribution.length > 0 && parsedTpDistribution.length !== numQuestions) {
          setToast({
            show: true,
            message: `TP distribution must have exactly ${numQuestions} levels or be left empty`,
            type: 'error'
          });
          setIsLoading(false);
          setTimeout(() => setToast({ ...toast, show: false }), 5000);
          return;
        }
      }

      const response = await fetch('/api/admin/generate-questions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          yearId,
          subjectId,
          unitId: unitId || undefined,
          numQuestions,
          questionTypes: selectedQuestionTypes,
          provider,
          model,
          language,
          tpDistribution: parsedTpDistribution
        })
      });

      if (response.ok) {
        const data = await response.json();
        setToast({
          show: true,
          message: `Job queued successfully! Batch ID: ${data.batchId}`,
          type: 'success'
        });
        refreshBatches(); // Refresh the batches list
      } else {
        const error = await response.json();
        setToast({
          show: true,
          message: `Error: ${error.message || 'Failed to queue job'}`,
          type: 'error'
        });
      }
    } catch (error) {
      setToast({
        show: true,
        message: `Error: ${error.message || 'An unexpected error occurred'}`,
        type: 'error'
      });
    } finally {
      setIsLoading(false);
      setTimeout(() => setToast({ ...toast, show: false }), 5000);
    }
  };

  // Handle question type toggle
  const toggleQuestionType = (type: QuestionType) => {
    setSelectedQuestionTypes(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  // Handle restart batch
  const handleRestartBatch = async (batchId: number) => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/admin/restart-batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ batchId })
      });

      if (response.ok) {
        const data = await response.json();
        setToast({
          show: true,
          message: data.message || 'Batch restarted successfully',
          type: 'success'
        });
        refreshBatches(); // Refresh the batches list
      } else {
        const error = await response.json();
        setToast({
          show: true,
          message: `Error: ${error.message || 'Failed to restart batch'}`,
          type: 'error'
        });
      }
    } catch (error) {
      setToast({
        show: true,
        message: `Error: ${error.message || 'An unexpected error occurred'}`,
        type: 'error'
      });
    } finally {
      setIsLoading(false);
      setTimeout(() => setToast({ ...toast, show: false }), 5000);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h1 className="text-2xl font-bold mb-8">Question Generator</h1>

      {/* Toast Notification */}
      {toast.show && (
        <div className={`fixed top-4 right-4 p-4 rounded-md shadow-md ${
          toast.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {toast.message}
        </div>
      )}

      {/* Token Usage Summary */}
      <div className="mb-8">
        <TokenUsageSummary />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Form Section */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Generate Questions</h2>
          <p className="text-sm text-gray-600 mb-4">
            Questions will be generated from Lesson Notes <strong>and</strong> any Sample Question notes you've uploaded.
          </p>
          <form onSubmit={handleSubmit}>
            {/* Year Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Year <span className="text-red-500">*</span>
              </label>
              <select
                value={yearId}
                onChange={(e) => setYearId(e.target.value ? Number(e.target.value) : '')}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              >
                <option value="">Select Year</option>
                {years.map((year) => (
                  <option key={year.id} value={year.id}>
                    Year {year.yearNumber}
                  </option>
                ))}
              </select>
            </div>

            {/* Subject Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Subject <span className="text-red-500">*</span>
              </label>
              <select
                value={subjectId}
                onChange={(e) => setSubjectId(e.target.value ? Number(e.target.value) : '')}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              >
                <option value="">Select Subject</option>
                {subjects.map((subject) => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Unit Selection (Optional) */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Unit (Optional)
              </label>
              <select
                value={unitId}
                onChange={(e) => setUnitId(e.target.value ? Number(e.target.value) : '')}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">All Units</option>
                {units.map((unit) => (
                  <option key={unit.id} value={unit.id}>
                    Unit {unit.unitNumber}: {unit.topicEn}
                  </option>
                ))}
              </select>
            </div>

            {/* Number of Questions */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Number of Questions (10-100)
              </label>
              <input
                type="number"
                min="10"
                max="100"
                value={numQuestions}
                onChange={(e) => setNumQuestions(Number(e.target.value))}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            {/* TP Level Distribution */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                TP Level Distribution (Optional)
              </label>
              <input
                type="text"
                value={tpDistribution}
                onChange={(e) => setTpDistribution(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="e.g., 1,2,3,3,4,5,6"
              />
              <p className="text-xs text-gray-500 mt-1">
                Comma-separated list of TP levels (1-6). Leave empty for balanced distribution.
              </p>
            </div>

            {/* Question Types */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Question Types <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                {Object.values(QuestionType).map((type) => (
                  <div key={type} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`type-${type}`}
                      checked={selectedQuestionTypes.includes(type)}
                      onChange={() => toggleQuestionType(type)}
                      className="mr-2"
                    />
                    <label htmlFor={`type-${type}`} className="text-sm">
                      {type.replace(/_/g, ' ')}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Language Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Generation Language <span className="text-red-500">*</span>
              </label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value as Language)}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              >
                <option value={Language.ZH}>Chinese (中文)</option>
                <option value={Language.EN}>English</option>
                <option value={Language.MS}>Malay</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                The primary language for generated questions. Default is Chinese.
              </p>
            </div>

            {/* Provider Selection */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                AI Provider <span className="text-red-500">*</span>
              </label>
              <select
                value={provider}
                onChange={(e) => setProvider(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              >
                <option value="openrouter">OpenRouter</option>
                <option value="gemini">Google Gemini</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Select the AI provider to use for question generation.
              </p>
            </div>

            {/* Model Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                AI Model <span className="text-red-500">*</span>
              </label>
              {provider === 'openrouter' ? (
                <>
                  <input
                    type="text"
                    value={model}
                    onChange={(e) => setModel(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="e.g., gpt-4o, llama3"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Supported models: gpt-4o, llama3, claude-3-opus, etc.
                  </p>
                </>
              ) : (
                <>
                  <select
                    value={model}
                    onChange={(e) => setModel(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    required
                  >
                    <option value="gemini-2.5-pro-preview-05-06">gemini-2.5-pro-preview-05-06</option>
                    <option value="gemini-2.5-flash-preview-04-17">gemini-2.5-flash-preview-04-17</option>
                    <option value="gemini-2.0-flash-exp">Gemini 2.0 Flash</option>
                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                    <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                    <option value="gemini-1.0-pro">Gemini 1.0 Pro</option>
                  </select>
                  <p className="text-xs text-gray-500 mt-1">
                    Select a Gemini model version.
                  </p>
                </>
              )}
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-2 px-4 rounded-md text-white font-medium ${
                isLoading ? 'bg-blue-400' : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isLoading ? 'Generating...' : 'Generate Questions'}
            </button>
          </form>
        </div>

        {/* Batches Table */}
        <BatchesTable
          onRestartBatch={handleRestartBatch}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
