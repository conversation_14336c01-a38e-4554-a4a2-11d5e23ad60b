import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { LocalDiskStorage } from '../services/storage/LocalDiskStorage';
import fs from 'fs/promises';
import path from 'path';

describe('Storage System', () => {
  const storage = new LocalDiskStorage();
  const testKey = `test-${Date.now()}.txt`;
  const testContent = Buffer.from('Hello, world!');
  const testMime = 'text/plain';
  
  // Clean up after tests
  afterAll(async () => {
    try {
      await storage.delete(testKey);
    } catch (error) {
      // Ignore errors
    }
  });
  
  it('should save a file to local storage', async () => {
    const url = await storage.save(testContent, testKey, testMime);
    
    // Check that the URL is correct
    expect(url).toBe(`/uploads/${testKey}`);
    
    // Check that the file exists
    const filePath = path.join(process.cwd(), 'uploads', testKey);
    const stats = await fs.stat(filePath);
    expect(stats.isFile()).toBe(true);
    
    // Check the file content
    const content = await fs.readFile(filePath);
    expect(content.toString()).toBe(testContent.toString());
  });
  
  it('should delete a file from local storage', async () => {
    const filePath = path.join(process.cwd(), 'uploads', testKey);
    
    // First, make sure the file exists
    const stats = await fs.stat(filePath);
    expect(stats.isFile()).toBe(true);
    
    // Delete the file
    await storage.delete(testKey);
    
    // Check that the file no longer exists
    try {
      await fs.stat(filePath);
      // If we get here, the file still exists
      expect(true).toBe(false); // This will fail the test
    } catch (error) {
      // This is expected - the file should not exist
      expect(error.code).toBe('ENOENT');
    }
  });
});
