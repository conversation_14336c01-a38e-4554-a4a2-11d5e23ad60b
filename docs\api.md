# API Documentation

## Overview

This document describes the API documentation generation process for the My Quiz App project. The project uses both Next.js API routes and a dedicated Fastify API server.

## API Architecture

### Next.js API Routes (Port 3000)
- **Location**: `apps/web/pages/api/`
- **Purpose**: Main application logic, authentication, and web-specific endpoints
- **Documentation**: Manual documentation in `docs/api-*.md` files

### Fastify API Server (Port 4000)
- **Location**: `apps/api/src/`
- **Purpose**: Dedicated API endpoints with auto-generated OpenAPI documentation
- **Documentation**: Auto-generated via Swagger/OpenAPI

## Auto-Generated API Documentation

### Setup

The Fastify API server includes automatic OpenAPI documentation generation using:
- `@fastify/swagger` - OpenAPI specification generation
- `@fastify/swagger-ui` - Interactive API documentation UI
- `openapi-typescript-codegen` - TypeScript client generation

### Accessing Documentation

#### Interactive Swagger UI
When the API server is running (port 4000), visit:
```
http://localhost:4000/documentation
```

#### OpenAPI JSON Specification
The raw OpenAPI specification is available at:
```
http://localhost:4000/documentation/json
```

### Generating Documentation

#### Prerequisites
1. Start the API server:
```bash
cd apps/api
npm run dev
```

2. Ensure the server is running on port 4000

#### Generate Markdown Documentation
From the API directory:
```bash
cd apps/api
npm run docs
```

This command:
1. Fetches the OpenAPI specification from `http://localhost:4000/documentation/json`
2. Generates TypeScript client code and markdown documentation
3. Outputs files to `docs/api/` directory

#### Generated Files Structure
```
docs/api/
├── index.ts              # Main API client
├── models/               # TypeScript interfaces
├── services/             # API service classes
└── README.md            # Auto-generated API documentation
```

### Adding Documentation to New Endpoints

When creating new Fastify routes, include OpenAPI schema:

```typescript
app.get('/example/:id', {
  schema: {
    tags: ['example'],
    summary: 'Get example by ID',
    description: 'Detailed description of the endpoint',
    params: {
      type: 'object',
      properties: {
        id: { type: 'string', description: 'Example ID' }
      },
      required: ['id']
    },
    response: {
      200: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' }
        }
      },
      404: {
        type: 'object',
        properties: {
          message: { type: 'string', example: 'Example not found' }
        }
      }
    }
  }
}, async (request, reply) => {
  // Route implementation
});
```

### Schema Guidelines

#### Tags
Use consistent tags to group related endpoints:
- `health` - Health check endpoints
- `quiz` - Quiz-related operations
- `auth` - Authentication endpoints
- `admin` - Administrative functions

#### Response Schemas
Always define response schemas for:
- Success responses (200, 201, etc.)
- Error responses (400, 401, 404, 500)

#### Parameter Validation
Include parameter schemas for:
- Path parameters (`params`)
- Query parameters (`querystring`)
- Request body (`body`)

## Manual Documentation

### Next.js API Routes
Next.js API routes are documented manually in separate files:

- `docs/api-overview.md` - General API information
- `docs/api-auth.md` - Authentication endpoints
- `docs/api-quiz.md` - Quiz-related endpoints
- `docs/api-admin.md` - Administrative endpoints
- `docs/api-analytics.md` - Analytics endpoints

### Documentation Standards

#### Endpoint Format
```markdown
### Endpoint Name

Brief description of what the endpoint does.

- **URL**: `/api/endpoint`
- **Method**: `GET|POST|PUT|DELETE`
- **Authentication Required**: Yes/No
- **Role Required**: ADMIN|STUDENT|None

#### Request Parameters
- `param1` (string, required): Description
- `param2` (number, optional): Description

#### Request Body
```json
{
  "field1": "value",
  "field2": 123
}
```

#### Response
```json
{
  "data": { ... },
  "message": "Success message"
}
```

#### Error Responses
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error
```

## Turbo Integration

### Build Task
The documentation generation is integrated with Turbo:

```json
{
  "scripts": {
    "docs": "turbo run docs"
  }
}
```

### Turbo Configuration
In `turbo.json`:
```json
{
  "tasks": {
    "docs": {
      "cache": false,
      "dependsOn": ["^build"]
    }
  }
}
```

## Development Workflow

### 1. Start Development Servers
```bash
npm run dev  # Starts both web (3000) and api (4000)
```

### 2. Add New API Endpoints
- For Fastify: Add to `apps/api/src/server.ts` with OpenAPI schema
- For Next.js: Add to `apps/web/pages/api/` with manual documentation

### 3. Generate Documentation
```bash
npm run docs  # Generates all documentation
```

### 4. Review Documentation
- Check Swagger UI at `http://localhost:4000/documentation`
- Review generated files in `docs/api/`
- Update manual documentation as needed

## Best Practices

### OpenAPI Schema Design
1. **Consistent Naming**: Use camelCase for properties
2. **Detailed Descriptions**: Provide clear, helpful descriptions
3. **Example Values**: Include realistic example data
4. **Error Handling**: Document all possible error responses
5. **Versioning**: Include API version in the specification

### Documentation Maintenance
1. **Keep Updated**: Regenerate docs after API changes
2. **Review Changes**: Check generated documentation for accuracy
3. **Version Control**: Commit generated documentation files
4. **Testing**: Verify examples work with actual API

### Performance Considerations
1. **Caching**: Swagger UI caches specifications
2. **Development Only**: Consider disabling Swagger in production
3. **Large Schemas**: Split complex schemas into reusable components

This documentation system provides comprehensive, up-to-date API documentation that scales with the application.
