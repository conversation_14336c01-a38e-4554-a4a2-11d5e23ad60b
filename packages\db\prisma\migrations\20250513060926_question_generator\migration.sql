-- CreateEnum
CREATE TYPE "QuestionStatus" AS ENUM ('DRAFT', 'LIVE', 'ARCHIVED');

-- CreateEnum
CREATE TYPE "BatchStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED');

-- AlterTable
ALTER TABLE "TG_Question" ADD COLUMN     "generationBatchId" INTEGER,
ADD COLUMN     "status" "QuestionStatus" NOT NULL DEFAULT 'DRAFT';

-- CreateTable
CREATE TABLE "TG_GenerationBatch" (
    "id" SERIAL NOT NULL,
    "adminId" INTEGER NOT NULL,
    "yearId" INTEGER NOT NULL,
    "subjectId" INTEGER NOT NULL,
    "unitId" INTEGER,
    "questionTypes" "QuestionType"[],
    "numQuestions" INTEGER NOT NULL,
    "modelUsed" TEXT NOT NULL,
    "status" "BatchStatus" NOT NULL DEFAULT 'PENDING',
    "jobId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "TG_GenerationBatch_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_BatchNotes" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,

    CONSTRAINT "_BatchNotes_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_BatchNotes_B_index" ON "_BatchNotes"("B");

-- AddForeignKey
ALTER TABLE "TG_Question" ADD CONSTRAINT "TG_Question_generationBatchId_fkey" FOREIGN KEY ("generationBatchId") REFERENCES "TG_GenerationBatch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_GenerationBatch" ADD CONSTRAINT "TG_GenerationBatch_adminId_fkey" FOREIGN KEY ("adminId") REFERENCES "TG_Account"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_GenerationBatch" ADD CONSTRAINT "TG_GenerationBatch_yearId_fkey" FOREIGN KEY ("yearId") REFERENCES "TG_Year"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_GenerationBatch" ADD CONSTRAINT "TG_GenerationBatch_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "TG_Subject"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TG_GenerationBatch" ADD CONSTRAINT "TG_GenerationBatch_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "TG_Unit"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_BatchNotes" ADD CONSTRAINT "_BatchNotes_A_fkey" FOREIGN KEY ("A") REFERENCES "TG_GenerationBatch"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_BatchNotes" ADD CONSTRAINT "_BatchNotes_B_fkey" FOREIGN KEY ("B") REFERENCES "TG_Notes"("id") ON DELETE CASCADE ON UPDATE CASCADE;
