import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const { unitId } = req.query;
    const studentId = req.headers['x-student-id'];

    if (!unitId || Array.isArray(unitId)) {
      return res.status(400).json({ message: 'Invalid unit ID' });
    }

    if (!studentId || Array.isArray(studentId)) {
      return res.status(400).json({ message: 'Invalid student ID' });
    }

    // Find the mastery record for this student and unit
    const mastery = await prisma.studentMastery.findUnique({
      where: {
        studentId_scope_scopeId: {
          studentId: parseInt(studentId),
          scope: 'unit',
          scopeId: parseInt(unitId),
        },
      },
      select: {
        currentTp: true,
        confidence: true,
      },
    });

    if (!mastery) {
      // Return default values if no mastery record exists
      return res.status(200).json({
        currentTp: 0,
        confidence: 'low',
      });
    }

    return res.status(200).json(mastery);
  } catch (error) {
    console.error('Error fetching mastery data:', error);
    return res.status(500).json({ message: 'Error fetching mastery data' });
  }
}
