import React from 'react';
import { render, screen } from '@testing-library/react';
import MultipleChoice from '../../components/renderers/MultipleChoice';
import { QuizProvider } from '../../components/quiz/QuizContext';
import '@testing-library/jest-dom';

// Mock lodash shuffle to control the output for testing
jest.mock('lodash/shuffle', () => {
  return jest.fn((arr) => {
    // Return a predictable shuffled order for testing
    if (arr && arr.length > 0) {
      // Reverse the array to simulate shuffling
      return [...arr].reverse();
    }
    return arr;
  });
});

// Mock the useQuiz hook
jest.mock('../../components/quiz/QuizContext', () => ({
  useQuiz: () => ({
    handleNext: jest.fn(),
  }),
  QuizProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('MultipleChoice Component', () => {
  const mockChoices = [
    { key: 'A', textEn: 'Option A', textZh: '选项A' },
    { key: 'B', textEn: 'Option B', textZh: '选项B' },
    { key: 'C', textEn: 'Option C', textZh: '选项C' },
    { key: 'D', textEn: 'Option D', textZh: '选项D' },
  ];

  const defaultProps = {
    promptEn: 'What is the answer?',
    promptZh: '答案是什么？',
    originalPrompt: 'What is the answer?',
    choices: mockChoices,
    onAnswerChange: jest.fn(),
    selectedAnswer: '',
    displayLanguage: 'en' as const,
    questionId: '123',
  };

  it('renders shuffled choices', () => {
    render(<MultipleChoice {...defaultProps} />);
    
    // Since we mocked shuffle to reverse the array, we expect the choices to be rendered in reverse order
    const choiceElements = screen.getAllByRole('button');
    
    // Check that the choices are rendered in the reversed order
    expect(choiceElements[0]).toHaveTextContent('D. Option D');
    expect(choiceElements[1]).toHaveTextContent('C. Option C');
    expect(choiceElements[2]).toHaveTextContent('B. Option B');
    expect(choiceElements[3]).toHaveTextContent('A. Option A');
  });

  it('handles answer selection correctly', () => {
    const onAnswerChange = jest.fn();
    render(
      <MultipleChoice 
        {...defaultProps} 
        onAnswerChange={onAnswerChange}
      />
    );
    
    // Click on the first choice (which should be D after shuffling)
    const firstChoice = screen.getAllByRole('button')[0];
    firstChoice.click();
    
    // Check that onAnswerChange was called with the correct key
    expect(onAnswerChange).toHaveBeenCalledWith('D');
  });
});
