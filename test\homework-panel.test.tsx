import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import HomeworkPanel from '../components/HomeworkPanel';
import { Language } from '@prisma/client';
import * as SWR from 'swr';

// Mock SWR
jest.mock('swr');

describe('HomeworkPanel', () => {
  const mockChildId = 123;
  const mockLang = 'ZH' as Language;

  // Mock homework data
  const mockHomeworkData = [
    {
      id: 1,
      subject: 'Mathematics',
      unitNumber: 5,
      topic: {
        en: 'Fractions',
        zh: '分数',
        ms: 'Pecahan'
      },
      progress: '2/10',
      quizType: 'test'
    },
    {
      id: 2,
      subject: 'Science',
      unitNumber: 3,
      topic: {
        en: 'Plants',
        zh: '植物',
        ms: 'Tumbuhan'
      },
      progress: '0/8',
      quizType: 'quick'
    }
  ];

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should show loading state when data is not yet available', () => {
    // Mock SWR to return undefined data (loading state)
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: undefined,
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={mockLang} />);

    expect(screen.getByText('Loading…')).toBeInTheDocument();
  });

  it('should show error state when there is an error', () => {
    // Mock SWR to return an error
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: undefined,
      error: new Error('Failed to fetch')
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={mockLang} />);

    expect(screen.getByText('Error loading homework')).toBeInTheDocument();
  });

  it('should show "No incomplete quizzes" message when data is empty', () => {
    // Mock SWR to return empty data
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: [],
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={mockLang} />);

    expect(screen.getByText('No incomplete quizzes. 🎉')).toBeInTheDocument();
  });

  it('should render homework items correctly with Chinese language preference', () => {
    // Mock SWR to return homework data
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: mockHomeworkData,
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={'ZH' as Language} />);

    // Check if both homework items are rendered
    expect(screen.getByText('Mathematics · Unit 5')).toBeInTheDocument();
    expect(screen.getByText('Science · Unit 3')).toBeInTheDocument();

    // Check if Chinese topics are shown (based on ZH language preference)
    expect(screen.getByText('分数')).toBeInTheDocument();
    expect(screen.getByText('植物')).toBeInTheDocument();

    // Check if progress is shown
    expect(screen.getByText('2/10 completed')).toBeInTheDocument();
    expect(screen.getByText('0/8 completed')).toBeInTheDocument();

    // Check if Resume buttons are present with correct links
    const resumeButtons = screen.getAllByTitle('Resume Quiz');
    expect(resumeButtons).toHaveLength(2);
    expect(resumeButtons[0]).toHaveAttribute('href', '/quiz/1');
    expect(resumeButtons[1]).toHaveAttribute('href', '/quiz/2');

    // Check if Cancel buttons are present
    const cancelButtons = screen.getAllByTitle('Cancel Quiz');
    expect(cancelButtons).toHaveLength(2);

    // Check if quiz types are displayed
    expect(screen.getByText('Test')).toBeInTheDocument();
    expect(screen.getByText('Quick')).toBeInTheDocument();

    // Check if Start Quiz button is present
    expect(screen.getByText('Start New Quiz')).toBeInTheDocument();
  });

  it('should render homework items correctly with English language preference', () => {
    // Mock SWR to return homework data
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: mockHomeworkData,
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={'EN' as Language} />);

    // Check if English topics are shown (based on EN language preference)
    expect(screen.getByText('Fractions')).toBeInTheDocument();
    expect(screen.getByText('Plants')).toBeInTheDocument();
  });

  it('should render homework items correctly with Malay language preference', () => {
    // Mock SWR to return homework data
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: mockHomeworkData,
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={'MS' as Language} />);

    // Check if Malay topics are shown (based on MS language preference)
    expect(screen.getByText('Pecahan')).toBeInTheDocument();
    expect(screen.getByText('Tumbuhan')).toBeInTheDocument();
  });

  it('should fall back to English topics when Malay language is preferred but no Malay translation exists', () => {
    // Create mock data with missing Malay translation
    const mockDataWithMissingTranslation = [
      {
        id: 3,
        subject: 'History',
        unitNumber: 2,
        topic: {
          en: 'Ancient Civilizations',
          zh: '古代文明',
          ms: undefined // Missing Malay translation
        },
        progress: '1/5',
        quizType: 'mastery'
      }
    ];

    // Mock SWR to return data with missing translation
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: mockDataWithMissingTranslation,
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={'MS' as Language} />);

    // Should fall back to English topic since ms is undefined
    expect(screen.getByText('Ancient Civilizations')).toBeInTheDocument();

    // Check if mastery quiz type is displayed
    expect(screen.getByText('Mastery')).toBeInTheDocument();
  });

  it('should handle quiz cancellation', async () => {
    // Mock fetch for cancel API
    global.fetch = jest.fn().mockImplementation((url) => {
      if (url === '/api/quiz/cancel') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ success: true })
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockHomeworkData)
      });
    });

    // Mock window.confirm to always return true
    window.confirm = jest.fn().mockReturnValue(true);

    // Mock window.alert
    window.alert = jest.fn();

    // Mock SWR mutate function
    const mutateMock = jest.fn();
    jest.spyOn(require('swr'), 'mutate').mockImplementation(mutateMock);

    // Render component
    render(<HomeworkPanel childId={mockChildId} lang={mockLang} />);

    // Click the cancel button for the first quiz
    const cancelButtons = screen.getAllByTitle('Cancel Quiz');
    fireEvent.click(cancelButtons[0]);

    // Check if confirm was called
    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to cancel this quiz attempt?');

    // Wait for the API call to complete
    await waitFor(() => {
      // Check if fetch was called with the correct parameters
      expect(global.fetch).toHaveBeenCalledWith('/api/quiz/cancel', expect.objectContaining({
        method: 'POST',
        body: JSON.stringify({ attemptId: 1 })
      }));

      // Check if mutate was called to refresh the data
      expect(mutateMock).toHaveBeenCalledWith(`/api/homework?childId=${mockChildId}`);

      // Check if alert was shown
      expect(window.alert).toHaveBeenCalledWith('Quiz attempt canceled.');
    });

    // Clean up mocks
    jest.restoreAllMocks();
  });

  it('should navigate to start-quiz page when Start New Quiz button is clicked', () => {
    // Mock window.location.href
    const originalLocation = window.location;
    delete window.location;
    window.location = { href: '' } as Location;

    // Mock SWR to return homework data
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: mockHomeworkData,
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={mockLang} />);

    // Click the Start New Quiz button
    const startQuizButton = screen.getByText('Start New Quiz');
    fireEvent.click(startQuizButton);

    // Check if navigation occurred
    expect(window.location.href).toBe('/start-quiz');

    // Restore window.location
    window.location = originalLocation;
  });

  it('should track analytics event when data loads', () => {
    // Create a spy on console.log which is used for analytics tracking in the component
    const consoleSpy = jest.spyOn(console, 'log');

    // Mock SWR to return homework data
    jest.spyOn(SWR, 'default').mockReturnValue({
      data: mockHomeworkData,
      error: undefined
    } as any);

    render(<HomeworkPanel childId={mockChildId} lang={mockLang} />);

    // Check if analytics event was tracked
    expect(consoleSpy).toHaveBeenCalledWith(
      'Analytics event: HomeworkPanelShown',
      { count: 2 }
    );
  });
});
