import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../lib/auth';

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    console.log('Checking if admin user already exists...');
    
    // Check if the admin user already exists
    const existingAdmin = await prisma.account.findUnique({
      where: {
        email: '<EMAIL>'
      }
    });

    if (existingAdmin) {
      console.log('Admin user already exists. Skipping creation.');
      return;
    }

    console.log('Creating admin user...');
    
    // Hash the password
    const { hash: passwordHash, salt } = await hashPassword('superpassword!');
    
    // Create the admin user
    const adminUser = await prisma.account.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password_hash: passwordHash,
        salt: salt,
        role: 'ADMIN',
        status: 'ACTIVE',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
    
    console.log(`Admin user created with ID: ${adminUser.id}`);
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
createAdminUser()
  .then(() => console.log('Admin user creation process completed.'))
  .catch(error => console.error('Error in admin user creation process:', error));
