# Admin API Endpoints

This document provides documentation for the admin-only API endpoints in the My Quiz App application.

## Accounts

### Get All Accounts

Retrieves all accounts with their associated children and licenses.

- **URL**: `/api/admin/accounts`
- **Method**: `GET`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Response

```json
[
  {
    "id": 1,
    "email": "<EMAIL>",
    "name": "Parent Name",
    "role": "PARENT",
    "status": "ACTIVE",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z",
    "children": [
      {
        "id": 1,
        "name": "Child Name",
        "username": "childusername",
        "year": "Year 5",
        "accountId": 1,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "license": {
      "id": 1,
      "type": "PREMIUM",
      "duration": 365,
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2024-01-01T00:00:00.000Z",
      "accountId": 1,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
]
```

### Create Account

Creates a new parent account with children.

- **URL**: `/api/admin/create-account`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Request Body

```json
{
  "name": "Parent Name",
  "email": "<EMAIL>",
  "password": "securepassword",
  "children": [
    {
      "name": "Child Name",
      "username": "childusername",
      "pin": "1234",
      "year": "Year 5"
    }
  ],
  "license": {
    "type": "PREMIUM",
    "duration": 365
  }
}
```

#### Response

```json
{
  "id": 1,
  "email": "<EMAIL>",
  "name": "Parent Name",
  "role": "PARENT",
  "status": "ACTIVE",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z",
  "children": [
    {
      "id": 1,
      "name": "Child Name",
      "username": "childusername",
      "year": "Year 5",
      "accountId": 1,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "license": {
    "id": 1,
    "type": "PREMIUM",
    "duration": 365,
    "startDate": "2023-01-01T00:00:00.000Z",
    "endDate": "2024-01-01T00:00:00.000Z",
    "accountId": 1,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

## Quiz Configuration

### Get Quiz Configurations

Retrieves quiz configurations.

- **URL**: `/api/admin/quiz-config`
- **Method**: `GET`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Request Parameters

| Parameter | Type   | Required | Description                                                |
|-----------|--------|----------|------------------------------------------------------------|
| mode      | string | No       | Filter by quiz mode (e.g., "MASTERY", "TEST")              |

#### Response

```json
[
  {
    "id": 1,
    "mode": "MASTERY",
    "numQuestions": 5,
    "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE"],
    "allowTranslate": true,
    "allowHints": true,
    "allowAiTutor": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  {
    "id": 2,
    "mode": "TEST",
    "numQuestions": 10,
    "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE", "MATCHING"],
    "allowTranslate": false,
    "allowHints": false,
    "allowAiTutor": false,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
]
```

### Update Quiz Configuration

Updates a quiz configuration.

- **URL**: `/api/admin/quiz-config`
- **Method**: `PUT`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Request Body

```json
{
  "mode": "MASTERY",
  "numQuestions": 5,
  "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE"],
  "allowTranslate": true,
  "allowHints": true,
  "allowAiTutor": true
}
```

#### Response

```json
{
  "id": 1,
  "mode": "MASTERY",
  "numQuestions": 5,
  "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE"],
  "allowTranslate": true,
  "allowHints": true,
  "allowAiTutor": true,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:10:00.000Z"
}
```

## Question Management

### Generate Questions

Generates questions using AI.

- **URL**: `/api/admin/generate-questions`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Request Body

```json
{
  "yearId": 1,
  "subjectId": 1,
  "unitId": 2,
  "numQuestions": 10,
  "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE"],
  "provider": "openrouter",
  "model": "anthropic/claude-3-opus",
  "language": "ZH",
  "tpDistribution": [1, 2, 3, 4, 5, 6]
}
```

#### Response

```json
{
  "id": 1,
  "adminId": 1,
  "yearId": 1,
  "subjectId": 1,
  "unitId": 2,
  "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE"],
  "numQuestions": 10,
  "provider": "openrouter",
  "modelUsed": "anthropic/claude-3-opus",
  "language": "ZH",
  "status": "PENDING",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z",
  "metadata": {
    "tpDistribution": [1, 2, 3, 4, 5, 6]
  }
}
```

### Get Question by ID

Retrieves a question by its ID.

- **URL**: `/api/admin/question/{id}`
- **Method**: `GET`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Response

```json
{
  "id": 1,
  "type": "MULTIPLE_CHOICE",
  "promptEn": "What is 2+2?",
  "promptZh": "2+2等于几?",
  "tpLevel": 1,
  "choices": [
    {
      "id": 1,
      "key": "A",
      "textEn": "3",
      "textZh": "3"
    },
    {
      "id": 2,
      "key": "B",
      "textEn": "4",
      "textZh": "4"
    }
  ],
  "answer": {
    "id": 1,
    "key": "B",
    "textEn": "4",
    "textZh": "4"
  },
  "explanation": {
    "id": 1,
    "textEn": "2+2=4 is a basic addition fact.",
    "textZh": "2+2=4 是基本的加法事实。"
  },
  "unit": {
    "unitNumber": 1,
    "topicEn": "Addition",
    "topicZh": "加法"
  },
  "subject": {
    "name": "Mathematics"
  },
  "year": {
    "yearNumber": "Year 1"
  }
}
```

### Add Question

Adds a new question.

- **URL**: `/api/admin/add-question`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Request Body

```json
{
  "type": "MULTIPLE_CHOICE",
  "year": 1,
  "subject": 1,
  "unit": 1,
  "prompt": "What is 2+2?",
  "promptZh": "2+2等于几?",
  "tpLevel": 1,
  "choices": [
    {
      "key": "A",
      "text": "3",
      "textZh": "3"
    },
    {
      "key": "B",
      "text": "4",
      "textZh": "4"
    }
  ],
  "answer": {
    "key": "B",
    "text": "4",
    "textZh": "4"
  },
  "explanation": {
    "text": "2+2=4 is a basic addition fact.",
    "textZh": "2+2=4 是基本的加法事实。"
  }
}
```

#### Response

```json
{
  "id": 1,
  "type": "MULTIPLE_CHOICE",
  "promptEn": "What is 2+2?",
  "promptZh": "2+2等于几?",
  "tpLevel": 1,
  "yearId": 1,
  "subjectId": 1,
  "unitId": 1,
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

## Child Management

### Update Child

Updates a child's details including name, year, and language preferences.

- **URL**: `/api/admin/update-child`
- **Method**: `PUT`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Request Body

```json
{
  "childId": 1,
  "name": "Updated Child Name",
  "year": "Year 6",
  "quizLanguage": "ZH",
  "menuLanguage": "EN"
}
```

#### Response

```json
{
  "message": "Child updated successfully",
  "child": {
    "id": 1,
    "name": "Updated Child Name",
    "year": "Year 6",
    "username": "childusername",
    "quizLanguage": "ZH",
    "menuLanguage": "EN"
  }
}
```

### Reset Child PIN

Resets a child's PIN.

- **URL**: `/api/admin/reset-child-pin`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Request Body

```json
{
  "childId": 1,
  "newPin": "123456"
}
```

#### Response

```json
{
  "message": "PIN reset successfully"
}
```

## Generation Batches

### Get Generation Batches

Retrieves all question generation batches.

- **URL**: `/api/admin/batches`
- **Method**: `GET`
- **Authentication Required**: Yes
- **Role Required**: ADMIN

#### Response

```json
[
  {
    "id": 1,
    "adminId": 1,
    "yearId": 1,
    "subjectId": 1,
    "unitId": 2,
    "questionTypes": ["MULTIPLE_CHOICE", "TRUE_FALSE"],
    "numQuestions": 10,
    "provider": "openrouter",
    "modelUsed": "anthropic/claude-3-opus",
    "language": "ZH",
    "status": "COMPLETED",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:10:00.000Z",
    "admin": {
      "name": "Admin Name",
      "email": "<EMAIL>"
    },
    "year": {
      "yearNumber": "Year 1"
    },
    "subject": {
      "name": "Mathematics"
    },
    "unit": {
      "unitNumber": 1,
      "topicEn": "Addition"
    },
    "questions": [
      {
        "id": 1
      },
      {
        "id": 2
      }
    ],
    "notes": [
      {
        "id": 1
      }
    ]
  }
]
```
