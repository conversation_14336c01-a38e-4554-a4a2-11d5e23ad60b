import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import prisma from '../../../lib/prisma';
import fs from 'fs/promises';
import path from 'path';
// Define BatchStatus enum to match Prisma schema
enum BatchStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED'
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check authentication and admin role
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    if (session.user?.role !== 'ADMIN') {
      return res.status(403).json({ message: 'Forbidden - Admin access required' });
    }

    // Get batch ID from request body
    const { batchId } = req.body;

    if (!batchId) {
      return res.status(400).json({ message: 'Batch ID is required' });
    }

    // Find the batch
    const batch = await prisma.generationBatch.findUnique({
      where: { id: Number(batchId) }
    });

    if (!batch) {
      return res.status(404).json({ message: 'Batch not found' });
    }

    // Check if the batch is in FAILED status
    if (batch.status !== BatchStatus.FAILED) {
      return res.status(400).json({
        message: `Cannot restart batch with status ${batch.status}. Only FAILED batches can be restarted.`
      });
    }

    // Update batch status to PENDING
    await prisma.generationBatch.update({
      where: { id: Number(batchId) },
      data: {
        status: BatchStatus.PENDING,
        completedAt: null // Reset completed time
      }
    });

    // Fetch the original batch to get its metadata
    const originalBatch = await prisma.generationBatch.findUnique({
      where: { id: Number(batchId) },
      select: {
        id: true,
        provider: true,
        modelUsed: true,
        language: true,
        metadata: true // This should contain the tpDistribution if it was stored
      }
    });

    if (!originalBatch) {
      return res.status(404).json({ message: 'Batch not found' });
    }

    // Create queue directory if it doesn't exist
    const queueDir = path.join(process.cwd(), 'queue');
    await fs.mkdir(queueDir, { recursive: true });

    // Create queue marker file with all original parameters
    const queueFile = path.join(queueDir, `generation-${batchId}.json`);

    // Extract tpDistribution from metadata if it exists
    let tpDistribution = undefined;
    if (originalBatch.metadata && typeof originalBatch.metadata === 'object') {
      try {
        // @ts-ignore - metadata might be stored as a JSON string or object
        tpDistribution = originalBatch.metadata.tpDistribution;
      } catch (error) {
        console.error('Error parsing metadata:', error);
      }
    }

    await fs.writeFile(
      queueFile,
      JSON.stringify({
        batchId: Number(batchId),
        provider: originalBatch.provider,
        language: originalBatch.language,
        tpDistribution: tpDistribution
      }),
      'utf-8'
    );

    // Return success response
    return res.status(200).json({
      success: true,
      message: `Batch ${batchId} has been restarted successfully`
    });
  } catch (error) {
    console.error('Error restarting batch:', error);
    return res.status(500).json({ message: 'Internal server error', error: String(error) });
  }
}

