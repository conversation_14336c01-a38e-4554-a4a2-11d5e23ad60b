import React from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';

const ShortAnswer: React.FC<BaseRendererProps<'SHORT_ANSWER'>> = ({
  promptEn,
  promptZh,
  originalPrompt,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  onMouseUp,
  isIncorrect,
  submittedAnswer,
  isCorrect,
  disableAnswerSelection
}) => {
  // Get the original language from the parent component
  const isChineseOriginal = originalPrompt === promptZh;
  const maxChars = spec?.maxChars;

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div className="text-lg font-medium mb-6 text-black">
        {originalPrompt || (displayLanguage === 'en' ? promptEn : promptZh)}
      </div>

      {promptMediaUrl && (
        <PromptMedia url={promptMediaUrl} alt={promptMediaAlt || ''} />
      )}

      <div className="mb-4">
        <input
          type="text"
          value={selectedAnswer}
          onChange={e => onAnswerChange(e.target.value)}
          placeholder={isChineseOriginal ? '请输入答案…' : 'Enter your answer...'}
          className={`w-full text-black rounded p-2 ${isIncorrect && selectedAnswer === submittedAnswer ? 'border-2 border-red-500' : ''} ${disableAnswerSelection ? 'bg-gray-100 cursor-not-allowed' : ''}`}
          maxLength={maxChars}
          disabled={disableAnswerSelection}
        />
        {maxChars && (
          <div className="text-sm text-right mt-1">
            {selectedAnswer.length}/{maxChars}
          </div>
        )}
        {isIncorrect && selectedAnswer && selectedAnswer === submittedAnswer && (
          <div className="text-red-500 mt-2 font-medium flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            {displayLanguage === 'en' ? 'Incorrect answer. Please try again.' : '答案不正确。请再试一次。'}
          </div>
        )}
      </div>
    </RendererWrapper>
  );
};

export default ShortAnswer;
