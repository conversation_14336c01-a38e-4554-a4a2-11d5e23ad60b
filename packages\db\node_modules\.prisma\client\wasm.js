
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.6.0
 * Query Engine version: f676762280b54cd07c770017ed3711ddde35f37a
 */
Prisma.prismaVersion = {
  client: "6.6.0",
  engine: "f676762280b54cd07c770017ed3711ddde35f37a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.SubjectScalarFieldEnum = {
  id: 'id',
  name: 'name'
};

exports.Prisma.YearScalarFieldEnum = {
  id: 'id',
  yearNumber: 'yearNumber'
};

exports.Prisma.UnitScalarFieldEnum = {
  id: 'id',
  unitNumber: 'unitNumber',
  topicEn: 'topicEn',
  topicZh: 'topicZh',
  topicMs: 'topicMs',
  subjectId: 'subjectId',
  yearId: 'yearId'
};

exports.Prisma.QuestionScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  type: 'type',
  promptEn: 'promptEn',
  promptZh: 'promptZh',
  promptMs: 'promptMs',
  promptMediaId: 'promptMediaId',
  spec: 'spec',
  keywords: 'keywords',
  tpLevel: 'tpLevel',
  originalLanguage: 'originalLanguage',
  translationState: 'translationState',
  status: 'status',
  subjectId: 'subjectId',
  yearId: 'yearId',
  unitId: 'unitId',
  subTopicEn: 'subTopicEn',
  subTopicZh: 'subTopicZh',
  subTopicMs: 'subTopicMs',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  generationBatchId: 'generationBatchId'
};

exports.Prisma.MediaScalarFieldEnum = {
  id: 'id',
  url: 'url',
  altEn: 'altEn',
  altZh: 'altZh',
  altMs: 'altMs',
  createdAt: 'createdAt'
};

exports.Prisma.ChoiceScalarFieldEnum = {
  id: 'id',
  key: 'key',
  textEn: 'textEn',
  textZh: 'textZh',
  textMs: 'textMs',
  mediaId: 'mediaId',
  questionId: 'questionId'
};

exports.Prisma.AnswerScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  type: 'type',
  key: 'key',
  textEn: 'textEn',
  textZh: 'textZh',
  textMs: 'textMs',
  answerSpec: 'answerSpec',
  createdAt: 'createdAt'
};

exports.Prisma.ExplanationTextScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  textEn: 'textEn',
  textZh: 'textZh',
  textMs: 'textMs'
};

exports.Prisma.AccountScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  password_hash: 'password_hash',
  salt: 'salt',
  role: 'role',
  name: 'name',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ChildScalarFieldEnum = {
  id: 'id',
  name: 'name',
  year: 'year',
  username: 'username',
  pin: 'pin',
  pin_hash: 'pin_hash',
  salt: 'salt',
  quizLanguage: 'quizLanguage',
  menuLanguage: 'menuLanguage',
  showDualLanguage: 'showDualLanguage',
  accountId: 'accountId'
};

exports.Prisma.LicenseScalarFieldEnum = {
  id: 'id',
  type: 'type',
  duration: 'duration',
  accountId: 'accountId'
};

exports.Prisma.TranslationLogScalarFieldEnum = {
  id: 'id',
  timestamp: 'timestamp',
  translatedText: 'translatedText',
  childId: 'childId',
  questionId: 'questionId'
};

exports.Prisma.QuizAttemptScalarFieldEnum = {
  id: 'id',
  questionIds: 'questionIds',
  startTime: 'startTime',
  endTime: 'endTime',
  score: 'score',
  childId: 'childId',
  subjectId: 'subjectId',
  unitId: 'unitId',
  currentQuestionIndex: 'currentQuestionIndex',
  status: 'status',
  quizType: 'quizType',
  metadata: 'metadata'
};

exports.Prisma.StudentAnswerScalarFieldEnum = {
  id: 'id',
  submittedAt: 'submittedAt',
  submittedKey: 'submittedKey',
  submittedText: 'submittedText',
  submittedJson: 'submittedJson',
  isCorrect: 'isCorrect',
  firstTryCorrect: 'firstTryCorrect',
  quizType: 'quizType',
  childId: 'childId',
  questionId: 'questionId',
  quizAttemptId: 'quizAttemptId'
};

exports.Prisma.StudentMasteryScalarFieldEnum = {
  scope: 'scope',
  scopeId: 'scopeId',
  studentId: 'studentId',
  currentTp: 'currentTp',
  confidence: 'confidence',
  computedAt: 'computedAt'
};

exports.Prisma.SettingScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NoteScalarFieldEnum = {
  id: 'id',
  filename: 'filename',
  fileUrl: 'fileUrl',
  mimeType: 'mimeType',
  fileSize: 'fileSize',
  contentType: 'contentType',
  yearId: 'yearId',
  subjectId: 'subjectId',
  unitId: 'unitId',
  createdAt: 'createdAt'
};

exports.Prisma.GenerationBatchScalarFieldEnum = {
  id: 'id',
  adminId: 'adminId',
  yearId: 'yearId',
  subjectId: 'subjectId',
  unitId: 'unitId',
  questionTypes: 'questionTypes',
  numQuestions: 'numQuestions',
  provider: 'provider',
  modelUsed: 'modelUsed',
  language: 'language',
  status: 'status',
  jobId: 'jobId',
  createdAt: 'createdAt',
  completedAt: 'completedAt',
  promptTokens: 'promptTokens',
  completionTokens: 'completionTokens',
  totalTokens: 'totalTokens',
  metadata: 'metadata'
};

exports.Prisma.QuizConfigScalarFieldEnum = {
  id: 'id',
  mode: 'mode',
  numQuestions: 'numQuestions',
  questionTypes: 'questionTypes',
  allowTranslate: 'allowTranslate',
  allowHints: 'allowHints',
  allowAiTutor: 'allowAiTutor',
  reviewMissedQuestions: 'reviewMissedQuestions',
  updatedAt: 'updatedAt'
};

exports.Prisma.HintLogScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  childId: 'childId',
  hint: 'hint',
  timestamp: 'timestamp'
};

exports.Prisma.AiTutorLogScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  childId: 'childId',
  userQuestion: 'userQuestion',
  aiResponse: 'aiResponse',
  language: 'language',
  timestamp: 'timestamp'
};

exports.Prisma.QuestionFlagScalarFieldEnum = {
  id: 'id',
  questionId: 'questionId',
  childId: 'childId',
  quizAttemptId: 'quizAttemptId',
  submittedKey: 'submittedKey',
  submittedText: 'submittedText',
  submittedJson: 'submittedJson',
  status: 'status',
  reviewNotes: 'reviewNotes',
  createdAt: 'createdAt',
  reviewedAt: 'reviewedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.QuestionType = exports.$Enums.QuestionType = {
  MULTIPLE_CHOICE: 'MULTIPLE_CHOICE',
  MULTIPLE_CHOICE_IMAGE: 'MULTIPLE_CHOICE_IMAGE',
  PICTURE_PROMPT: 'PICTURE_PROMPT',
  FILL_IN_THE_BLANK: 'FILL_IN_THE_BLANK',
  TRUE_FALSE: 'TRUE_FALSE',
  SHORT_ANSWER: 'SHORT_ANSWER',
  LONG_ANSWER: 'LONG_ANSWER',
  MATCHING: 'MATCHING',
  SEQUENCING: 'SEQUENCING'
};

exports.Language = exports.$Enums.Language = {
  EN: 'EN',
  ZH: 'ZH',
  MS: 'MS'
};

exports.TranslationStatus = exports.$Enums.TranslationStatus = {
  NONE: 'NONE',
  PARTIAL: 'PARTIAL',
  COMPLETE: 'COMPLETE'
};

exports.QuestionStatus = exports.$Enums.QuestionStatus = {
  DRAFT: 'DRAFT',
  LIVE: 'LIVE',
  ARCHIVED: 'ARCHIVED'
};

exports.AnswerType = exports.$Enums.AnswerType = {
  SINGLE_CHOICE: 'SINGLE_CHOICE',
  MULTI_CHOICE: 'MULTI_CHOICE',
  SHORT_TEXT: 'SHORT_TEXT',
  TRUE_FALSE: 'TRUE_FALSE',
  FILL_IN_THE_BLANK: 'FILL_IN_THE_BLANK',
  MATCHING: 'MATCHING',
  SEQUENCING: 'SEQUENCING',
  LONG_TEXT_RUBRIC: 'LONG_TEXT_RUBRIC'
};

exports.Role = exports.$Enums.Role = {
  PARENT: 'PARENT',
  CHILD: 'CHILD',
  TEACHER: 'TEACHER',
  ADMIN: 'ADMIN'
};

exports.Status = exports.$Enums.Status = {
  ACTIVE: 'ACTIVE',
  PENDING: 'PENDING',
  INACTIVE: 'INACTIVE'
};

exports.LicenseType = exports.$Enums.LicenseType = {
  FREE_TRIAL: 'FREE_TRIAL',
  STANDARD_PLAN: 'STANDARD_PLAN'
};

exports.QuizStatus = exports.$Enums.QuizStatus = {
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  CANCELED: 'CANCELED'
};

exports.QuizType = exports.$Enums.QuizType = {
  MASTERY: 'MASTERY',
  TEST: 'TEST',
  QUICK: 'QUICK'
};

exports.NoteType = exports.$Enums.NoteType = {
  PDF: 'PDF',
  IMAGE: 'IMAGE',
  TEXT: 'TEXT',
  MARKDOWN: 'MARKDOWN',
  SAMPLE: 'SAMPLE',
  OTHER: 'OTHER'
};

exports.BatchStatus = exports.$Enums.BatchStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED'
};

exports.QuizMode = exports.$Enums.QuizMode = {
  MASTERY: 'MASTERY',
  TEST: 'TEST'
};

exports.FlagStatus = exports.$Enums.FlagStatus = {
  PENDING: 'PENDING',
  REVIEWED: 'REVIEWED',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED'
};

exports.Prisma.ModelName = {
  Subject: 'Subject',
  Year: 'Year',
  Unit: 'Unit',
  Question: 'Question',
  Media: 'Media',
  Choice: 'Choice',
  Answer: 'Answer',
  ExplanationText: 'ExplanationText',
  Account: 'Account',
  Child: 'Child',
  License: 'License',
  TranslationLog: 'TranslationLog',
  QuizAttempt: 'QuizAttempt',
  StudentAnswer: 'StudentAnswer',
  StudentMastery: 'StudentMastery',
  Setting: 'Setting',
  Note: 'Note',
  GenerationBatch: 'GenerationBatch',
  QuizConfig: 'QuizConfig',
  HintLog: 'HintLog',
  AiTutorLog: 'AiTutorLog',
  QuestionFlag: 'QuestionFlag'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
