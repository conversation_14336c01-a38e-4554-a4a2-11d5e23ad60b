# Logging Configuration
LOG_LEVEL=info

# Database connection
DATABASE_URL="postgresql://appuser:mysecretpasswordquizapp@localhost:5433/education"

# NextAuth configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Storage configuration
STORAGE_DRIVER=local

# PostgreSQL configuration for Docker
POSTGRES_USER=appuser
POSTGRES_PASSWORD=mysecretpasswordquizapp
POSTGRES_DB=education

# AI API keys for question generation and AI tutor
OPENROUTER_API_KEY=your-openrouter-api-key-here
GEMINI_API_KEY=your-gemini-api-key-here

# === Adaptive TP-Mastery (prototype) ===
FEATURE_ADAPTIVE_V2=off          # back-end / node
NEXT_PUBLIC_FEATURE_ADAPTIVE_V2=off   # front-end / browser

# === Quiz Version ===
NEXT_PUBLIC_QUIZ_VERSION=v1      # v1 or v2

# === Error Tracking (Optional) ===
# NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn-here
