import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "./auth/[...nextauth]";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    // Count the number of flagged questions
    const count = await prisma.questionFlag.count();
    
    // Get the most recent flag if any exist
    const latestFlag = count > 0 
      ? await prisma.questionFlag.findFirst({
          orderBy: { createdAt: 'desc' },
          include: {
            question: {
              select: {
                questionId: true,
                promptEn: true
              }
            },
            child: {
              select: {
                name: true
              }
            }
          }
        })
      : null;

    return res.status(200).json({ 
      success: true, 
      count,
      latestFlag
    });
  } catch (error) {
    console.error('Error testing flag functionality:', error);
    return res.status(500).json({ 
      success: false,
      message: 'An error occurred while testing flag functionality',
      error: String(error)
    });
  }
}
