/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\index.tsx */ \"(pages-dir-node)/./pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./components/ParentPortal.tsx":
/*!*************************************!*\
  !*** ./components/ParentPortal.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ParentPortal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction ParentPortal({ onAssignHomework }) {\n    const [isUserMenuOpen, setIsUserMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTab, setSelectedTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('progress');\n    const [selectedTopics, setSelectedTopics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [syllabus, setSyllabus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dueDate, setDueDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedSubject, setSelectedSubject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State for incomplete quizzes\n    const [incompleteQuizzes, setIncompleteQuizzes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingIncompleteQuizzes, setLoadingIncompleteQuizzes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [incompleteQuizzesError, setIncompleteQuizzesError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // New state for analytics data\n    const [translationAnalytics, setTranslationAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [knowledgeAnalytics, setKnowledgeAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loadingAnalytics, setLoadingAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [analyticsError, setAnalyticsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Mock available subjects with icons\n    const availableSubjects = {\n        Science: '🧪',\n        Mathematics: '🔢',\n        English: '📚'\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParentPortal.useEffect\": ()=>{\n            // Fetch syllabus data from API endpoint\n            fetch('/api/syllabus').then({\n                \"ParentPortal.useEffect\": (res)=>res.json()\n            }[\"ParentPortal.useEffect\"]).then({\n                \"ParentPortal.useEffect\": (data)=>{\n                    if (data && data.syllabus) {\n                        setSyllabus(data.syllabus);\n                    } else {\n                        console.error('Syllabus data not in expected format:', data);\n                        setSyllabus([]);\n                    }\n                }\n            }[\"ParentPortal.useEffect\"]).catch({\n                \"ParentPortal.useEffect\": (error)=>{\n                    console.error('Error loading syllabus:', error);\n                    setSyllabus([]);\n                }\n            }[\"ParentPortal.useEffect\"]);\n        }\n    }[\"ParentPortal.useEffect\"], []);\n    // New useEffect to fetch analytics data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParentPortal.useEffect\": ()=>{\n            const fetchAnalytics = {\n                \"ParentPortal.useEffect.fetchAnalytics\": async ()=>{\n                    setLoadingAnalytics(true);\n                    setAnalyticsError(null);\n                    try {\n                        // TODO: Get actual childId from auth context or selected child\n                        const childId = 1; // Placeholder childId\n                        const [translationRes, knowledgeRes] = await Promise.all([\n                            fetch(`/api/analytics/translations?childId=${childId}`),\n                            fetch(`/api/analytics/knowledge?childId=${childId}`)\n                        ]);\n                        if (!translationRes.ok) throw new Error('Failed to fetch translation analytics');\n                        if (!knowledgeRes.ok) throw new Error('Failed to fetch knowledge analytics');\n                        const translationData = await translationRes.json();\n                        const knowledgeData = await knowledgeRes.json();\n                        setTranslationAnalytics(translationData);\n                        setKnowledgeAnalytics(knowledgeData);\n                    } catch (error) {\n                        console.error('Error fetching analytics:', error);\n                        setAnalyticsError(error.message);\n                    } finally{\n                        setLoadingAnalytics(false);\n                    }\n                }\n            }[\"ParentPortal.useEffect.fetchAnalytics\"];\n            fetchAnalytics();\n        }\n    }[\"ParentPortal.useEffect\"], []); // Add dependencies here if filtering by child or time period is implemented\n    // Fetch incomplete quizzes for the parent's children\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ParentPortal.useEffect\": ()=>{\n            const fetchIncompleteQuizzes = {\n                \"ParentPortal.useEffect.fetchIncompleteQuizzes\": async ()=>{\n                    if (session?.user?.id) {\n                        setLoadingIncompleteQuizzes(true);\n                        setIncompleteQuizzesError(null);\n                        try {\n                            const response = await fetch(`/api/parent/incomplete-quizzes?parentId=${session.user.id}`); // Pass parentId\n                            if (!response.ok) {\n                                throw new Error(`Error fetching incomplete quizzes: ${response.statusText}`);\n                            }\n                            const data = await response.json();\n                            setIncompleteQuizzes(data.incompleteQuizzes);\n                        } catch (error) {\n                            console.error('Error fetching incomplete quizzes:', error);\n                            setIncompleteQuizzesError(error.message);\n                        } finally{\n                            setLoadingIncompleteQuizzes(false);\n                        }\n                    }\n                }\n            }[\"ParentPortal.useEffect.fetchIncompleteQuizzes\"];\n            // Only fetch if session is authenticated\n            if (status === 'authenticated') {\n                fetchIncompleteQuizzes();\n            }\n        }\n    }[\"ParentPortal.useEffect\"], [\n        session?.user?.id,\n        status\n    ]); // Depend on session.user.id and status\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    console.log('ParentPortal session state:', session);\n    // Mock parent data (keep for structure, but use session.user.name for display)\n    const parentData = {\n        name: session?.user?.name || 'Parent',\n        children: [\n            {\n                name: 'John Smith',\n                grade: '10th Grade',\n                recentActivity: [\n                    {\n                        type: 'quiz',\n                        subject: 'Science',\n                        score: 85,\n                        date: '2025-04-22'\n                    },\n                    {\n                        type: 'homework',\n                        subject: 'Mathematics',\n                        status: 'Completed',\n                        date: '2025-04-21'\n                    }\n                ],\n                progress: {\n                    Mathematics: {\n                        score: 75,\n                        trend: '+5%'\n                    },\n                    Science: {\n                        score: 85,\n                        trend: '+2%'\n                    },\n                    History: {\n                        score: 92,\n                        trend: '+8%'\n                    }\n                },\n                upcomingTasks: [\n                    {\n                        type: 'quiz',\n                        subject: 'Mathematics',\n                        dueDate: '2025-04-25'\n                    },\n                    {\n                        type: 'homework',\n                        subject: 'History',\n                        dueDate: '2025-04-24'\n                    }\n                ]\n            }\n        ],\n        notifications: [\n            {\n                type: 'milestone',\n                message: 'John completed Science Chapter 5 with 85%',\n                date: '2025-04-22'\n            },\n            {\n                type: 'review',\n                message: 'Mathematics needs review - Chapter 4 scores below average',\n                date: '2025-04-21'\n            }\n        ]\n    };\n    const handleTopicSelect = (unit)=>{\n        setSelectedTopics((prev)=>{\n            if (prev.includes(unit)) {\n                return prev.filter((t)=>t !== unit);\n            }\n            return [\n                ...prev,\n                unit\n            ];\n        });\n    };\n    const handleSubjectSelect = (subject)=>{\n        setSelectedSubject(subject);\n        setSelectedTopics([]); // Reset selected topics when changing subject\n    };\n    const handleAssignTopics = ()=>{\n        if (selectedTopics.length > 0 && dueDate) {\n            // Here you would typically make an API call to save the assignment\n            console.log('Assigning topics:', selectedTopics, 'Due date:', dueDate);\n            if (onAssignHomework) {\n                onAssignHomework();\n            }\n            // Reset selection\n            setSelectedTopics([]);\n            setDueDate('');\n        }\n    };\n    // Show loading state while session is loading\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n            lineNumber: 188,\n            columnNumber: 12\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (status === 'unauthenticated') {\n        router.push('/login');\n        return null; // Return null or a loading indicator while redirecting\n    }\n    // If authenticated, render the portal\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 py-4 flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: \"QuizApp\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm bg-green-100 text-green-800 px-3 py-1 rounded-full\",\n                                    children: \"Parent Portal\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right mr-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: parentData.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: \"Parent Account\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsUserMenuOpen(!isUserMenuOpen),\n                                            className: \"flex items-center space-x-2 focus:outline-none\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white\",\n                                                    children: parentData.name.charAt(0)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"▼\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        isUserMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-2 text-xs text-gray-500\",\n                                                    children: \"Signed in as Parent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Account Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Email Preferences\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: \"Help Center\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-100\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n                                                            callbackUrl: '/login'\n                                                        }),\n                                                    className: \"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 focus:outline-none\",\n                                                    children: \"Sign Out\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"-mb-px flex space-x-8\",\n                        children: [\n                            'progress',\n                            'assign topics',\n                            'resources',\n                            'notifications'\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedTab(tab),\n                                className: `${selectedTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm capitalize`,\n                                children: tab\n                            }, tab, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 py-8\",\n                children: [\n                    selectedTab === 'progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Incomplete Quizzes\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    loadingIncompleteQuizzes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Loading incomplete quizzes...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 44\n                                    }, this),\n                                    incompleteQuizzesError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500\",\n                                        children: [\n                                            \"Error loading incomplete quizzes: \",\n                                            incompleteQuizzesError\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 42\n                                    }, this),\n                                    !loadingIncompleteQuizzes && incompleteQuizzes.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No incomplete quizzes found for your children.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    !loadingIncompleteQuizzes && incompleteQuizzes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: incompleteQuizzes.map((quiz)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-gray-50 rounded-lg border-l-4 border-yellow-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: [\n                                                                        quiz.childName,\n                                                                        \" - \",\n                                                                        quiz.subject,\n                                                                        \" \",\n                                                                        quiz.unit ? `Unit ${quiz.unit}` : ''\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 281,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500 mt-1\",\n                                                                    children: [\n                                                                        quiz.quizType,\n                                                                        \" Quiz - Attempted \",\n                                                                        quiz.attemptedQuestions,\n                                                                        \" of \",\n                                                                        quiz.totalQuestions,\n                                                                        \" questions\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Started: \",\n                                                                new Date(quiz.startTime).toLocaleString(),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                \"Elapsed: \",\n                                                                Math.floor(quiz.elapsedTimeSeconds / 60),\n                                                                \"m \",\n                                                                quiz.elapsedTimeSeconds % 60,\n                                                                \"s\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, quiz.id, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this),\n                            loadingAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Loading analytics...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 34\n                            }, this),\n                            analyticsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-500\",\n                                children: [\n                                    \"Error loading analytics: \",\n                                    analyticsError\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 32\n                            }, this),\n                            translationAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Translation Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Total Translations: \",\n                                            translationAnalytics.translationCount || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Common Translated Words:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: translationAnalytics.commonWords && translationAnalytics.commonWords.length > 0 ? translationAnalytics.commonWords.slice(0, 10).map((word, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    word.word || 'Unknown',\n                                                    \" (\",\n                                                    word.count || 0,\n                                                    \")\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No common words found\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, this),\n                            knowledgeAnalytics && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Knowledge Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: [\n                                            \"Total Quiz Attempts: \",\n                                            knowledgeAnalytics.attemptCount || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Attempts by Subject:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.subjectAttemptCounts && Object.entries(knowledgeAnalytics.subjectAttemptCounts).length > 0 ? Object.entries(knowledgeAnalytics.subjectAttemptCounts).map(([subject, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    subject,\n                                                    \": \",\n                                                    count\n                                                ]\n                                            }, subject, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No subject data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Attempts by Unit:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.unitAttemptCounts && Object.entries(knowledgeAnalytics.unitAttemptCounts).length > 0 ? Object.entries(knowledgeAnalytics.unitAttemptCounts).map(([unit, count])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    unit,\n                                                    \": \",\n                                                    count\n                                                ]\n                                            }, unit, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No unit data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Subject Success Rates:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.subjectSuccessRates && knowledgeAnalytics.subjectSuccessRates.length > 0 ? knowledgeAnalytics.subjectSuccessRates.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    item.subject,\n                                                    \": \",\n                                                    (item.successRate || 0).toFixed(2),\n                                                    \"%\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No subject success data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mt-4 mb-2\",\n                                        children: \"Unit Success Rates:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        children: knowledgeAnalytics.unitSuccessRates && knowledgeAnalytics.unitSuccessRates.length > 0 ? knowledgeAnalytics.unitSuccessRates.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    item.unit,\n                                                    \": \",\n                                                    (item.successRate || 0).toFixed(2),\n                                                    \"%\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 23\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"No unit success data available\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'assign topics' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: !selectedSubject ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: \"\\uD83D\\uDCDA\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Select a Subject\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                            children: Object.entries(availableSubjects).map(([subject, icon])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleSubjectSelect(subject),\n                                                    className: \"p-4 rounded-lg border-2 border-transparent hover:border-blue-500 bg-blue-50 hover:bg-blue-100 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-3xl mb-2\",\n                                                            children: icon\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium\",\n                                                            children: subject\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, subject, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mr-2\",\n                                                            children: availableSubjects[selectedSubject]\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedSubject,\n                                                        \" Topics\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedSubject(null),\n                                                    className: \"text-sm text-blue-600 hover:text-blue-800\",\n                                                    children: \"Change Subject\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-3\",\n                                            children: syllabus && syllabus.length > 0 ? syllabus.map((topic)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleTopicSelect(topic.unit),\n                                                    className: `p-3 rounded-md flex items-center ${selectedTopics.includes(topic.unit) ? 'bg-blue-100 border-2 border-blue-500' : 'bg-blue-50 hover:bg-blue-100 border-2 border-transparent'}`,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: selectedTopics.includes(topic.unit),\n                                                            onChange: ()=>{},\n                                                            className: \"h-4 w-4 text-blue-600 rounded mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-left flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-sm\",\n                                                                    children: [\n                                                                        \"Unit \",\n                                                                        topic.unit,\n                                                                        \": \",\n                                                                        topic.topic?.en || 'Unknown'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: topic.topic?.zh || ''\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, topic.unit, true, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"col-span-2 text-center py-4 text-gray-500\",\n                                                children: \"No topics available for this subject\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Assignment Details\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Due Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: dueDate,\n                                                        onChange: (e)=>setDueDate(e.target.value),\n                                                        className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700\",\n                                                        children: \"Selected Topics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-1 text-sm text-gray-500\",\n                                                        children: selectedTopics.length === 0 ? 'No topics selected' : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"list-disc pl-5\",\n                                                            children: selectedTopics.map((unit)=>{\n                                                                const topic = syllabus && syllabus.find((t)=>t.unit === unit);\n                                                                return topic ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"Unit \",\n                                                                        topic.unit,\n                                                                        \": \",\n                                                                        topic.topic?.en || 'Unknown'\n                                                                    ]\n                                                                }, unit, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 29\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"Unit \",\n                                                                        unit\n                                                                    ]\n                                                                }, unit, true, {\n                                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleAssignTopics,\n                                                disabled: selectedTopics.length === 0 || !dueDate,\n                                                className: `w-full py-3 rounded-md text-white font-medium ${selectedTopics.length > 0 && dueDate ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'}`,\n                                                children: [\n                                                    \"Assign Topics (\",\n                                                    selectedTopics.length,\n                                                    \" selected)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'homework' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"Upcoming Tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onAssignHomework,\n                                            className: \"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\",\n                                            children: \"Assign New Task\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        parentData.children && parentData.children[0] && parentData.children[0].upcomingTasks && parentData.children[0].upcomingTasks.map((task, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: task.subject\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: task.type\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Due: \",\n                                                                task.dueDate\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 21\n                                            }, this)),\n                                        (!parentData.children || !parentData.children[0] || !parentData.children[0].upcomingTasks || parentData.children[0].upcomingTasks.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 text-center py-4\",\n                                            children: \"No upcoming tasks\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'resources' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold mb-4\",\n                                    children: \"Recommended Resources\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        'Mathematics Practice Sets',\n                                        'Science Video Tutorials',\n                                        'History Study Guides',\n                                        'Language Arts Worksheets'\n                                    ].map((resource)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: resource\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mb-4\",\n                                                    children: \"Supplementary materials to support learning\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 text-sm hover:text-blue-700\",\n                                                    children: \"Access Resource →\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, resource, true, {\n                                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                            lineNumber: 532,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 11\n                    }, this),\n                    selectedTab === 'notifications' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Recent Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            parentData.notifications && parentData.notifications.map((notification, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium\",\n                                                                        children: notification.message\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                                        children: notification.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: notification.date\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, index, false, {\n                                                    fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            (!parentData.notifications || parentData.notifications.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-500 text-center py-4\",\n                                                children: \"No notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Notification Preferences\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            'Weekly Progress Reports',\n                                            'Assignment Alerts',\n                                            'Performance Updates',\n                                            'Resource Recommendations'\n                                        ].map((pref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: pref\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 577,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Receive updates via email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"relative inline-flex items-center cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                className: \"sr-only peer\",\n                                                                defaultChecked: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, pref, true, {\n                                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\components\\\\ParentPortal.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/ParentPortal.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n// pages/_app.tsx\n\n\n\nfunction MyApp({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_2__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_app.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSxpQkFBaUI7O0FBQ2E7QUFFbUI7QUFFakQsU0FBU0MsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFdBQVcsRUFBRUMsT0FBTyxFQUFFLEdBQUdELFdBQVcsRUFBWTtJQUMxRSxxQkFDRSw4REFBQ0gsNERBQWVBO1FBQUNJLFNBQVNBO2tCQUN4Qiw0RUFBQ0Y7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QjtBQUVBLGlFQUFlRixLQUFLQSxFQUFBIiwic291cmNlcyI6WyJEOlxccHJvamVjdHNcXEdpdEh1YlxcbXktcXVpei1hcHBcXGFwcHNcXHdlYlxccGFnZXNcXF9hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhZ2VzL19hcHAudHN4XHJcbmltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJ1xyXG5pbXBvcnQgdHlwZSB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnXHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcclxuXHJcbmZ1bmN0aW9uIE15QXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHM6IHsgc2Vzc2lvbiwgLi4ucGFnZVByb3BzIH0gfTogQXBwUHJvcHMpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9ufT5cclxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxyXG4gICAgPC9TZXNzaW9uUHJvdmlkZXI+XHJcbiAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBNeUFwcFxyXG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTXlBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJzZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                        dangerouslySetInnerHTML: {\n                            __html: `\n            @font-face {\n              font-family: 'Chinese Fallback';\n              src: local('SimSun'), local('Microsoft YaHei'), local('STHeiti');\n              unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF;\n            }\n          `\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ParentPortal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/ParentPortal */ \"(pages-dir-node)/./components/ParentPortal.tsx\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n// Test component to switch between roles\n\n\n // Import useRouter\n\n\n\n// Test component to switch between roles\nfunction RoleSwitcher({ onSwitch }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 bg-white p-4 rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-sm font-bold mb-2\",\n                children: \"Test Mode: Switch Role\"\n            }, void 0, false, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onSwitch(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD),\n                        className: \"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                        children: \"Student View\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onSwitch(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.PARENT),\n                        className: \"px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600\",\n                        children: \"Parent View\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\nfunction Home() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)(); // Initialize useRouter\n    // Function to switch roles for testing\n    const switchRole = async (role)=>{\n        // Sign out current session\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signOut)({\n            redirect: false\n        });\n        // Mock credentials based on role\n        const credentials = role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD ? {\n            username: 'johndoe',\n            pin: '1234'\n        } // Mock child credentials\n         : {\n            email: '<EMAIL>',\n            password: 'password123'\n        }; // Mock parent credentials\n        // Sign in with new role\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.signIn)('credentials', {\n            ...credentials,\n            redirect: false\n        });\n    };\n    // Handle redirects based on authentication status and user role\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                router.push('/login');\n            } else if (status === 'authenticated' && session?.user) {\n                const user = session.user;\n                if (user.role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD) {\n                    router.push('/student-dashboard');\n                }\n            // Parent users stay on this page to see the ParentPortal\n            }\n        }\n    }[\"Home.useEffect\"], [\n        status,\n        session,\n        router\n    ]);\n    // Show loading state while session is loading or redirecting\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this);\n    }\n    // If not authenticated, show loading while redirecting\n    if (status === 'unauthenticated') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Redirecting to login...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    const user = session?.user;\n    // If child user, show loading while redirecting\n    if (user?.role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.CHILD) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Redirecting to dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: user?.role === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Role.PARENT ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ParentPortal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            onAssignHomework: ()=>{}\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 109,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"Access Denied\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"You don't have permission to access this page.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n                lineNumber: 112,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\projects\\\\GitHub\\\\my-quiz-app\\\\apps\\\\web\\\\pages\\\\index.tsx\",\n            lineNumber: 111,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/index.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();