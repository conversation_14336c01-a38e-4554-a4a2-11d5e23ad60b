import React from 'react';
import { BaseRendererProps, RendererWrapper, PromptMedia } from './BaseRenderer';

const PicturePrompt: React.FC<BaseRendererProps<'PICTURE_PROMPT'>> = ({
  promptEn,
  promptZh,
  originalPrompt,
  promptMediaUrl,
  promptMediaAlt,
  spec,
  onAnswerChange,
  selectedAnswer,
  displayLanguage,
  onMouseUp
}) => {
  // Get the original language from the parent component
  const isChineseOriginal = originalPrompt === promptZh;
  // Use alt text from spec if available, otherwise use the provided alt
  // Follow the original language of the question
  const altText = isChineseOriginal
    ? (spec?.altTextZh || promptMediaAlt || '')
    : (spec?.altTextEn || promptMediaAlt || '');

  return (
    <RendererWrapper onMouseUp={onMouseUp}>
      <div className="text-lg font-medium mb-6 text-black">
        {originalPrompt || (displayLanguage === 'en' ? promptEn : promptZh)}
      </div>

      {promptMediaUrl && (
        <div className="mb-6">
          <PromptMedia
            url={promptMediaUrl}
            alt={altText}
            style="large"
          />

          {spec?.license && (
            <div className="text-xs text-gray-400 text-center mt-1">
              {spec.license}
            </div>
          )}
        </div>
      )}

      <div className="mb-4">
        <textarea
          value={selectedAnswer}
          onChange={e => onAnswerChange(e.target.value)}
          placeholder={isChineseOriginal ? '在此处写下您的答案...' : 'Write your answer here...'}
          className="w-full text-black rounded p-2 h-32"
        />
      </div>
    </RendererWrapper>
  );
};

export default PicturePrompt;
