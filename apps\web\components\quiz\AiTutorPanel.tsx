import React from 'react';
import ChatHistory from './ChatHistory';
import { useQuiz } from './QuizContext';

interface AiTutorPanelProps {
  displayLanguage: 'en' | 'zh';
  visible: boolean;
}

const AiTutorPanel: React.FC<AiTutorPanelProps> = ({
  displayLanguage,
  visible
}) => {
  const {
    aiTutorMessages,
    aiTutorInput,
    setAiTutorInput,
    aiTutorIsLoading,
    handleAskAiTutor
  } = useQuiz();

  // When the user presses Enter in the input field
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && aiTutorInput.trim()) {
      handleAskAiTutor(aiTutorInput);
    }
  };

  // When the user clicks the Send button
  const handleSendClick = () => {
    if (aiTutorInput.trim()) {
      handleAskAiTutor(aiTutorInput);
    }
  };

  if (!visible) return null;

  return (
    <div className="flex-1 h-full flex flex-col pt-2 md:pt-0 border-t md:border-t-0 md:border-l border-white border-opacity-50 md:pl-3">
      <h3 className="font-bold mb-2 pr-8">{displayLanguage === 'en' ? 'AI Tutor' : 'AI 导师'}</h3>

      {/* Chat history */}
      <div className="flex-grow overflow-hidden">
        <ChatHistory
          messages={aiTutorMessages}
          displayLanguage={displayLanguage}
        />
      </div>

      {/* Chat input */}
      <div className="flex mt-2 mb-12">
        <input
          type="text"
          value={aiTutorInput}
          onChange={e => setAiTutorInput(e.target.value)}
          placeholder={displayLanguage === 'en' ? 'Ask the AI Tutor...' : '问AI导师…'}
          className="w-full text-black rounded-l p-2"
          onKeyDown={handleKeyDown}
          disabled={aiTutorIsLoading}
        />
        <button
          onClick={handleSendClick}
          className={`${aiTutorIsLoading ? 'bg-gray-500' : 'bg-orange-500 hover:bg-orange-600'} text-white font-semibold py-2 px-4 rounded-r focus:outline-none`}
          disabled={aiTutorIsLoading}
        >
          {aiTutorIsLoading
            ? (displayLanguage === 'en' ? '...' : '...')
            : (displayLanguage === 'en' ? 'Send' : '发送')}
        </button>
      </div>
    </div>
  );
};

export default AiTutorPanel;
