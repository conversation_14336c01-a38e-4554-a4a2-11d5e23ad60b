import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { QuizMode } from '@prisma/client';
import shuffle from 'lodash/shuffle';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Check if user is a CHILD
  if (session.user?.role !== 'CHILD') {
    return res.status(403).json({ message: 'Forbidden - Child access required' });
  }

  try {
    const { subjectId, yearId, unitId } = req.body;

    // Validate required fields
    if (!subjectId) {
      return res.status(400).json({ message: 'Subject ID is required' });
    }

    if (!yearId) {
      return res.status(400).json({ message: 'Year ID is required' });
    }

    // Get the child ID from the session
    const childId = Number(session.user.id);

    // 1. Fetch the quiz config for MASTERY mode
    const cfg = await prisma.quizConfig.findUnique({
      where: { mode: 'MASTERY' as QuizMode },
    });

    if (!cfg) {
      return res.status(500).json({ message: 'Quiz configuration not found' });
    }

    // 2. Build question pool
    const candidates = await prisma.question.findMany({
      where: {
        subjectId: Number(subjectId),
        yearId: Number(yearId),
        unitId: unitId ? Number(unitId) : undefined,
        type: { in: cfg.questionTypes as any[] },
      },
      orderBy: { id: 'asc' },
    });

    if (candidates.length === 0) {
      return res.status(404).json({ message: 'No questions found for the specified criteria' });
    }

    // 3. Random-shuffle and slice to cfg.numQuestions
    const shuffledCandidates = shuffle(candidates);
    const numQuestions = Math.min(cfg.numQuestions, shuffledCandidates.length);
    const selected = shuffledCandidates.slice(0, numQuestions);

    // 4. Create TG_QuizAttempt
    const attempt = await prisma.quizAttempt.create({
      data: {
        childId: childId,
        subjectId: Number(subjectId),
        unitId: unitId ? Number(unitId) : undefined,
        questionIds: selected.map(q => q.id.toString()),
        currentQuestionIndex: 0,
        quizType: 'MASTERY',
        status: 'ACTIVE',
        metadata: {
          configSnapshot: {
            numQuestions: cfg.numQuestions,
            allowTranslate: cfg.allowTranslate,
            allowHints: cfg.allowHints,
            allowAiTutor: cfg.allowAiTutor,
          },
        },
      },
    });

    // 5. Return the attempt ID
    return res.status(200).json({ attemptId: attempt.id });
  } catch (error) {
    console.error('Error creating practice quiz:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
