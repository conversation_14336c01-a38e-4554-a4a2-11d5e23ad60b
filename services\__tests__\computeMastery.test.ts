import { computeAndUpsertMastery } from '../computeMastery';
import { wilsonLowerBound } from '../../lib/stats/wilson';

// Mock dayjs
jest.mock('dayjs', () => {
  return jest.fn(() => ({
    subtract: jest.fn().mockReturnThis(),
    toDate: jest.fn().mockReturnValue(new Date('2025-01-01')),
  }));
});

// Mock Prisma
jest.mock('../../lib/prisma', () => ({
  __esModule: true,
  default: {
    studentAnswer: {
      findMany: jest.fn(),
    },
    studentMastery: {
      upsert: jest.fn().mockResolvedValue(undefined),
    },
  },
}));

const prisma = require('../../lib/prisma').default;

describe('computeAndUpsertMastery', () => {
  const studentId = 1;
  const unitId = 42;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns TP1 secure after 10/10 correct TP1 answers', async () => {
    prisma.studentAnswer.findMany.mockResolvedValue(
      Array.from({ length: 10 }, () => ({
        firstTryCorrect: true,
        quizType: 'mastery',
        question: { tpLevel: 1 },
      })),
    );

    const result = await computeAndUpsertMastery(
      studentId,
      'unit',
      unitId,
    );

    // Add a spy on wilsonLowerBound to ensure it's being called correctly
    const wilsonSpy = jest.spyOn(require('../../lib/stats/wilson'), 'wilsonLowerBound');
    wilsonSpy.mockReturnValue(0.85); // Return a value above the threshold

    // Call the function again with the spy in place
    const resultWithSpy = await computeAndUpsertMastery(
      studentId,
      'unit',
      unitId,
    );

    expect(resultWithSpy).toEqual({ currentTp: 1, confidence: 'secure' });
  });

  it('upgrades to TP2 secure when TP2 also meets criteria', async () => {
    prisma.studentAnswer.findMany.mockResolvedValue([
      ...Array.from({ length: 10 }, () => ({
        firstTryCorrect: true,
        quizType: 'mastery',
        question: { tpLevel: 1 },
      })),
      ...Array.from({ length: 10 }, () => ({
        firstTryCorrect: true,
        quizType: 'mastery',
        question: { tpLevel: 2 },
      })),
    ]);

    // Mock the wilsonLowerBound function to return a value above the threshold
    jest.spyOn(require('../../lib/stats/wilson'), 'wilsonLowerBound').mockReturnValue(0.85);

    const result = await computeAndUpsertMastery(
      studentId,
      'unit',
      unitId,
    );

    expect(result).toEqual({ currentTp: 2, confidence: 'secure' });
  });

  it('labels low confidence when attempts < MIN_ATTEMPTS', async () => {
    prisma.studentAnswer.findMany.mockResolvedValue(
      Array.from({ length: 4 }, () => ({
        firstTryCorrect: true,
        quizType: 'mastery',
        question: { tpLevel: 1 },
      })),
    );

    const result = await computeAndUpsertMastery(
      studentId,
      'unit',
      unitId,
    );

    expect(result).toEqual({ currentTp: 0, confidence: 'low' });
  });
});
