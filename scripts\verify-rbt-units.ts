import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Verifying Year 5 Reka Bentuk dan Teknologi units...');

    // Find Reka Bentuk dan Teknologi subject record
    const rbtSubject = await prisma.subject.findUnique({
      where: { name: 'Reka Bentuk dan Teknologi' },
      select: { id: true }
    });

    if (!rbtSubject) {
      console.error('Reka Bentuk dan Teknologi subject not found');
      return;
    }

    // Find Year 5 record
    const year5 = await prisma.year.findUnique({
      where: { yearNumber: 5 },
      select: { id: true }
    });

    if (!year5) {
      console.error('Year 5 not found');
      return;
    }

    // Query the units
    const units = await prisma.unit.findMany({
      where: {
        subjectId: rbtSubject.id,
        yearId: year5.id
      },
      orderBy: {
        unitNumber: 'asc'
      }
    });

    console.log(`Found ${units.length} units for Year 5 Reka Bentuk dan Teknologi:`);
    
    // Display the units in a table format
    console.log('Unit\tChinese Topic\tMalay Topic\tEnglish Topic');
    console.log('----\t------------\t-----------\t-------------');
    
    units.forEach(unit => {
      console.log(`${unit.unitNumber}\t${unit.topicZh}\t${unit.topicMs || 'N/A'}\t${unit.topicEn}`);
    });
  } catch (error) {
    console.error('Error verifying Reka Bentuk dan Teknologi units:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    console.error('Fatal error:', e);
    process.exit(1);
  });
