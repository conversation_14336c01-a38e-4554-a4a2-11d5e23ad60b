import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // This is a test endpoint to verify the translation API functionality
  
  // Check for OpenRouter Translator API key
  const openRouterTranslatorApiKey = process.env.OPENROUTER_TRANSLATOR_API_KEY;
  if (!openRouterTranslatorApiKey) {
    return res.status(500).json({ message: 'OpenRouter Translator API key not configured' });
  }
  
  try {
    // Sample text to translate
    const highlightedText = "这是一个测试";
    const question = "这是一个测试问题";
    
    // Prepare prompt for LLM
    const prompt = `Translate the following highlighted text to English. The text is from a question: "${question}". The highlighted text is: "${highlightedText}". Provide only the translated text in your response.`;
    
    // Query OpenRouter API
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openRouterTranslatorApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'qwen/qwen3-32b:free',
        messages: [
          { role: 'system', content: 'You are a helpful translator.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.7,
        max_tokens: 512
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      return res.status(500).json({
        message: 'LLM query failed',
        error: errorData.error?.message || 'Unknown error'
      });
    }
    
    const data = await response.json();
    const translatedText = data.choices?.[0]?.message?.content || 'Translation failed';
    
    return res.status(200).json({ 
      translatedText,
      success: true,
      apiKeyPresent: !!openRouterTranslatorApiKey,
      responseData: data
    });
  } catch (error) {
    console.error('Translation error:', error);
    return res.status(500).json({ 
      message: 'Translation error', 
      error: error instanceof Error ? error.message : 'Unknown error',
      apiKeyPresent: !!openRouterTranslatorApiKey
    });
  }
}
