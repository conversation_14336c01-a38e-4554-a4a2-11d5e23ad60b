{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "baseUrl": ".", "paths": {"@quiz/db": ["./packages/db"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "apps/*/node_modules", "packages/*/node_modules"], "ts-node": {"compilerOptions": {"module": "CommonJS"}}}