import { QuestionType } from '@prisma/client';
import { validateSpec, safeValidateSpec } from '../lib/validation/questionSpec';

describe('Question Spec Validation', () => {
  describe('Multiple Choice Image', () => {
    it('should validate a valid spec', () => {
      const spec = {
        shuffleChoices: true,
        choiceImageStyle: 'square'
      };
      
      expect(() => validateSpec(QuestionType.MULTIPLE_CHOICE_IMAGE, spec)).not.toThrow();
      
      const result = safeValidateSpec(QuestionType.MULTIPLE_CHOICE_IMAGE, spec);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(spec);
      }
    });
    
    it('should validate with optional fields missing', () => {
      const spec = {};
      
      expect(() => validateSpec(QuestionType.MULTIPLE_CHOICE_IMAGE, spec)).not.toThrow();
    });
    
    it('should reject invalid choiceImageStyle', () => {
      const spec = {
        choiceImageStyle: 'invalid'
      };
      
      expect(() => validateSpec(QuestionType.MULTIPLE_CHOICE_IMAGE, spec)).toThrow();
      
      const result = safeValidateSpec(QuestionType.MULTIPLE_CHOICE_IMAGE, spec);
      expect(result.success).toBe(false);
    });
  });
  
  describe('Fill in the Blank', () => {
    it('should validate a valid spec', () => {
      const spec = {
        template: 'The Earth rotates on its ___ and revolves around the ___.',
        blanks: [
          {
            correct: 'axis',
            distractors: ['equator', 'poles', 'center']
          },
          {
            correct: 'Sun',
            distractors: ['Moon', 'stars', 'galaxy']
          }
        ],
        caseSensitive: false
      };
      
      expect(() => validateSpec(QuestionType.FILL_IN_THE_BLANK, spec)).not.toThrow();
    });
    
    it('should reject empty template', () => {
      const spec = {
        template: '',
        blanks: [
          {
            correct: 'axis',
            distractors: ['equator', 'poles', 'center']
          }
        ]
      };
      
      expect(() => validateSpec(QuestionType.FILL_IN_THE_BLANK, spec)).toThrow();
    });
    
    it('should reject empty blanks array', () => {
      const spec = {
        template: 'The Earth rotates on its ___.',
        blanks: []
      };
      
      expect(() => validateSpec(QuestionType.FILL_IN_THE_BLANK, spec)).toThrow();
    });
  });
  
  describe('True/False', () => {
    it('should validate a valid spec', () => {
      const spec = {
        statement: 'The Moon is larger than Earth.',
        correctAnswer: false
      };
      
      expect(() => validateSpec(QuestionType.TRUE_FALSE, spec)).not.toThrow();
    });
    
    it('should reject missing statement', () => {
      const spec = {
        correctAnswer: true
      };
      
      expect(() => validateSpec(QuestionType.TRUE_FALSE, spec)).toThrow();
    });
    
    it('should reject missing correctAnswer', () => {
      const spec = {
        statement: 'The Moon is larger than Earth.'
      };
      
      expect(() => validateSpec(QuestionType.TRUE_FALSE, spec)).toThrow();
    });
  });
  
  describe('Matching', () => {
    it('should validate a valid spec', () => {
      const spec = {
        pairs: [
          { left: 'Mercury', right: 'Closest to the Sun' },
          { left: 'Venus', right: 'Hottest planet' }
        ],
        shuffleLeft: false,
        shuffleRight: true
      };
      
      expect(() => validateSpec(QuestionType.MATCHING, spec)).not.toThrow();
    });
    
    it('should validate with rightImage instead of right', () => {
      const spec = {
        pairs: [
          { left: 'Mercury', rightImage: 'https://example.com/mercury.jpg' },
          { left: 'Venus', rightImage: 'https://example.com/venus.jpg' }
        ]
      };
      
      expect(() => validateSpec(QuestionType.MATCHING, spec)).not.toThrow();
    });
    
    it('should reject less than two pairs', () => {
      const spec = {
        pairs: [
          { left: 'Mercury', right: 'Closest to the Sun' }
        ]
      };
      
      expect(() => validateSpec(QuestionType.MATCHING, spec)).toThrow();
    });
    
    it('should reject invalid rightImage URL', () => {
      const spec = {
        pairs: [
          { left: 'Mercury', rightImage: 'not-a-url' },
          { left: 'Venus', rightImage: 'https://example.com/venus.jpg' }
        ]
      };
      
      expect(() => validateSpec(QuestionType.MATCHING, spec)).toThrow();
    });
  });
  
  describe('Sequencing', () => {
    it('should validate a valid spec', () => {
      const spec = {
        items: ['First step', 'Second step', 'Third step'],
        correctOrder: [0, 1, 2],
        allowPartialCredit: true
      };
      
      expect(() => validateSpec(QuestionType.SEQUENCING, spec)).not.toThrow();
    });
    
    it('should reject mismatched items and correctOrder lengths', () => {
      const spec = {
        items: ['First step', 'Second step', 'Third step'],
        correctOrder: [0, 1]
      };
      
      expect(() => validateSpec(QuestionType.SEQUENCING, spec)).toThrow();
    });
    
    it('should reject invalid correctOrder values', () => {
      const spec = {
        items: ['First step', 'Second step', 'Third step'],
        correctOrder: [0, 1, 3] // 3 is out of bounds
      };
      
      expect(() => validateSpec(QuestionType.SEQUENCING, spec)).toThrow();
    });
  });
});
