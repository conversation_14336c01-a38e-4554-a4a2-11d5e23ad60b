import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from "next-auth/next";
import { authOptions } from "./auth/[...nextauth]";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { questionId, quizAttemptId, submittedKey, submittedText, submittedJson } = req.body;

  if (!questionId || !quizAttemptId) {
    return res.status(400).json({ message: 'Question ID and Quiz Attempt ID are required' });
  }

  try {
    // Get the child ID from the session
    const childId = Number(session.user?.id);
    
    if (!childId) {
      return res.status(400).json({ message: 'Child ID not found in session' });
    }

    // Create a new question flag
    const questionFlag = await prisma.questionFlag.create({
      data: {
        questionId: Number(questionId),
        childId,
        quizAttemptId: Number(quizAttemptId),
        submittedKey,
        submittedText,
        submittedJson,
        status: 'PENDING'
      }
    });

    return res.status(200).json({ 
      success: true, 
      message: 'Question flagged successfully',
      questionFlag 
    });
  } catch (error) {
    console.error('Error flagging question:', error);
    return res.status(500).json({ 
      success: false,
      message: 'An error occurred while flagging the question' 
    });
  }
}
