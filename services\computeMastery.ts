import prisma from '../lib/prisma';
import dayjs from 'dayjs';
import { wilsonLowerBound } from '../lib/stats/wilson';

const WINDOW_DAYS   = 28;   // rolling window
const THRESHOLD     = 0.80; // mastery cutoff (80 %)
const MIN_ATTEMPTS  = 5;    // reliability filter

type Scope = 'unit' | 'subject';

/**
 * Re-computes mastery for a student at the given scope and
 * upserts the result into TG_StudentMastery.
 */
export async function computeAndUpsertMastery(
  studentId: number,
  scope: Scope,
  scopeId: number
) {
  /* 1️⃣  fetch answers in the rolling window */
  const since = dayjs().subtract(WINDOW_DAYS, 'day').toDate();
  const answers = await prisma.studentAnswer.findMany({
    where: {
      childId: studentId,
      submittedAt: { gte: since },
      quizType: 'mastery',          // 👈 NEW filter
      question: scope === 'unit'
        ? { unitId: scopeId }
        : { subjectId: scopeId },
    },
    select: { firstTryCorrect: true, question: { select: { tpLevel: true } } },
  });

  /* 2️⃣  bucket answers by TP level */
  const buckets = Array.from({ length: 6 }, (_, i) => ({
    tp: i + 1,
    attempted: 0,
    correct:   0,
  }));

  for (const a of answers) {
    const b = buckets[a.question.tpLevel - 1];
    b.attempted += 1;
    if (a.firstTryCorrect) b.correct += 1;
  }

  /* 3️⃣  secure-mastery check using accuracy + Wilson bound */
  const secure = buckets.map(({ attempted, correct }) =>
    attempted >= MIN_ATTEMPTS &&
    correct / attempted >= THRESHOLD &&
    wilsonLowerBound(correct, attempted) >= THRESHOLD
  );

  /* 4️⃣  highest contiguous secure TP */
  let currentTp = 0;
  for (let i = 0; i < 6; i++) {
    if (secure[i] && secure.slice(0, i).every(Boolean)) {
      currentTp = i + 1;
    }
  }

  /* 5️⃣  confidence label */
  const confidence =
    currentTp === 0
      ? 'low'
      : buckets[currentTp - 1].attempted >= MIN_ATTEMPTS
        ? 'secure'
        : 'emerging';

  /* 6️⃣  upsert row */
  await prisma.studentMastery.upsert({
    where: { studentId_scope_scopeId: { studentId, scope, scopeId } },
    create: { studentId, scope, scopeId, currentTp, confidence },
    update: { currentTp, confidence, computedAt: new Date() },
  });

  return { currentTp, confidence };
}
