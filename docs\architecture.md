# My Quiz App - Architecture Overview

## System Architecture

My Quiz App is a modern educational platform built with a **Turborepo monorepo** architecture, featuring adaptive learning capabilities and AI-powered tutoring.

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Next.js Web App<br/>apps/web<br/>Port 3000]
        BROWSER[Browser<br/>React Components]
    end
    
    subgraph "API Layer"
        NEXTAPI[Next.js API Routes<br/>/api/*]
        FASTIFY[Fastify API Server<br/>apps/api<br/>Port 4000]
    end
    
    subgraph "Data Layer"
        DB[(PostgreSQL<br/>Port 5433<br/>Docker)]
        PRISMA[@quiz/db<br/>Prisma ORM]
    end
    
    subgraph "Shared Packages"
        CORE[@quiz/core<br/>Logger, Utils]
        STORAGE[Storage Service<br/>Local/Cloud]
    end
    
    subgraph "External Services"
        AI[AI Services<br/>OpenRouter/Gemini<br/>Translation & Tutoring]
        AUTH[NextAuth.js<br/>Authentication]
    end
    
    BROWSER --> WEB
    WEB --> NEXTAPI
    WEB --> FASTIFY
    NEXTAPI --> PRISMA
    FASTIFY --> PRISMA
    PRISMA --> DB
    WEB --> CORE
    FASTIFY --> CORE
    WEB --> STORAGE
    WEB --> AI
    WEB --> AUTH
    
    classDef webapp fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef data fill:#e8f5e8
    classDef external fill:#fff3e0
    
    class WEB,BROWSER webapp
    class NEXTAPI,FASTIFY api
    class DB,PRISMA data
    class AI,AUTH external
```

## Technology Stack

### Frontend
- **Framework**: Next.js 15.3.1 with React 18
- **Styling**: Tailwind CSS with custom components
- **State Management**: SWR for data fetching
- **UI Components**: Custom components with Lucide React icons
- **Drag & Drop**: @dnd-kit for interactive quiz features

### Backend
- **Web Server**: Next.js API routes for main application logic
- **API Server**: Fastify 5.2.0 for dedicated API endpoints
- **Authentication**: NextAuth.js with session management
- **Logging**: Pino logger with structured logging

### Database
- **Database**: PostgreSQL 16 (Docker containerized)
- **ORM**: Prisma 6.6.0 with type-safe queries
- **Migrations**: Prisma migrate for schema management
- **Connection**: Shared Prisma client via @quiz/db package

### Development Tools
- **Monorepo**: Turborepo for build orchestration
- **Language**: TypeScript throughout
- **Testing**: Jest with ts-jest for unit testing
- **Package Manager**: npm with workspaces

## Application Flow

### 1. User Authentication
```
Browser → Next.js → NextAuth.js → Database (User/Account tables)
```

### 2. Quiz Generation & Management
```
Admin Dashboard → API Routes → Prisma → Database
                ↓
AI Services (OpenRouter/Gemini) → Question Generation
```

### 3. Student Quiz Taking
```
Student Interface → Quiz Components → API Routes → Database
                                   ↓
AI Tutor Service → Hints & Explanations
```

### 4. Adaptive Learning
```
Quiz Answers → Mastery Algorithm → TP Level Calculation → Database
```

## Key Features

### Adaptive Learning System
- **TP Levels**: 6-tier proficiency system (TP1-TP6)
- **Wilson Confidence**: Statistical confidence intervals for mastery
- **28-day Window**: Rolling accuracy calculations
- **Feature Flag**: `FEATURE_ADAPTIVE_V2` environment variable

### AI Integration
- **Question Generation**: Automated question creation via AI APIs
- **AI Tutor**: Contextual hints and explanations
- **Translation**: Real-time text translation support
- **Multi-language**: Chinese/English dual language support

### Quiz Types
- **Mastery Quizzes**: Adaptive difficulty with immediate feedback
- **Test Mode**: Formal assessment without hints
- **Practice Mode**: Free exploration with full AI support

## Environment Configuration

### Required Environment Variables
```bash
# Database
DATABASE_URL="postgresql://user:pass@localhost:5433/education"

# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key

# AI Services
OPENROUTER_API_KEY=your-openrouter-key
GEMINI_API_KEY=your-gemini-key

# Feature Flags
FEATURE_ADAPTIVE_V2=on
NEXT_PUBLIC_FEATURE_ADAPTIVE_V2=on

# Logging
LOG_LEVEL=info
```

## Development Workflow

### Local Development
```bash
# Start database
docker-compose up -d db

# Install dependencies
npm install

# Run migrations
npm run migrate

# Start development servers
npm run dev  # Starts both web (3000) and api (4000)
```

### Package Structure
```
my-quiz-app/
├── apps/
│   ├── web/          # Next.js application (port 3000)
│   └── api/          # Fastify API server (port 4000)
├── packages/
│   ├── db/           # Shared Prisma database package
│   └── core/         # Shared utilities and logger
├── docs/             # Documentation
└── scripts/          # Database and utility scripts
```

## Security & Performance

### Security Features
- **Authentication**: Session-based auth with NextAuth.js
- **Authorization**: Role-based access (ADMIN, STUDENT)
- **Input Validation**: Prisma schema validation
- **Environment Isolation**: Separate dev/prod configurations

### Performance Optimizations
- **Database Indexing**: Optimized queries for quiz performance
- **Caching**: SWR for client-side data caching
- **Code Splitting**: Next.js automatic code splitting
- **Image Optimization**: Next.js image optimization

## Deployment Architecture

### Production Setup
- **Web App**: Vercel/Docker deployment
- **API Server**: Separate container/service
- **Database**: Managed PostgreSQL (AWS RDS/similar)
- **Storage**: Cloud storage for media files
- **Monitoring**: Structured logging with optional Sentry integration

This architecture provides a scalable, maintainable foundation for an educational platform with advanced AI capabilities and adaptive learning features.
