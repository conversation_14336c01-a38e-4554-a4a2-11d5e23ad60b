import React from 'react';
import { render, screen } from '@testing-library/react';
import Question<PERSON>enderer from '../../../components/quiz/QuestionRenderer';
import { QuizProvider } from '../../../components/quiz/QuizContext';
import { QuestionType } from '@prisma/client';

// Mock the renderer components
jest.mock('../../../components/renderers', () => ({
  MultipleChoice: () => <div data-testid="multiple-choice-renderer">Multiple Choice</div>,
  MultipleChoiceImage: () => <div data-testid="multiple-choice-image-renderer">Multiple Choice Image</div>,
  ShortAnswer: () => <div data-testid="short-answer-renderer">Short Answer</div>,
  FillInTheBlank: () => <div data-testid="fill-in-the-blank-renderer">Fill in the Blank</div>,
  TrueFalse: () => <div data-testid="true-false-renderer">True/False</div>,
  Matching: () => <div data-testid="matching-renderer">Matching</div>,
  Sequencing: () => <div data-testid="sequencing-renderer">Sequencing</div>,
  LongAnswer: () => <div data-testid="long-answer-renderer">Long Answer</div>,
  PicturePrompt: () => <div data-testid="picture-prompt-renderer">Picture Prompt</div>,
}));

describe('QuestionRenderer', () => {
  // Mock quiz context values
  const mockQuizContext = {
    questions: [
      {
        id: 1,
        questionId: 'q1',
        type: QuestionType.MULTIPLE_CHOICE,
        promptEn: 'English prompt',
        promptZh: 'Chinese prompt',
        originalLanguage: 'EN',
        choices: [
          { key: 'A', textEn: 'Option A', textZh: '选项A' },
          { key: 'B', textEn: 'Option B', textZh: '选项B' },
        ],
        answer: 'A',
        explanation: { en: 'Explanation', zh: '解释' },
        topic: 'Math',
        subject: { name: 'Mathematics' },
        unit: { unitNumber: 1, topicEn: 'Numbers', topicZh: '数字' },
      },
    ],
    currentIndex: 0,
    selectedAnswer: '',
    setSelectedAnswer: jest.fn(),
    displayLanguage: 'en',
    isAnswerIncorrect: false,
    submittedAnswer: '',
    getOriginalPrompt: jest.fn().mockReturnValue('English prompt'),
    handleNext: jest.fn(),
    handleMouseUp: jest.fn(),
    setContextMenu: jest.fn(),
  };

  it('renders the correct renderer for MULTIPLE_CHOICE', () => {
    render(
      <QuizProvider initialQuestions={mockQuizContext.questions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('multiple-choice-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for MULTIPLE_CHOICE_IMAGE', () => {
    const imageQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.MULTIPLE_CHOICE_IMAGE,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={imageQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('multiple-choice-image-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for SHORT_ANSWER', () => {
    const shortAnswerQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.SHORT_ANSWER,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={shortAnswerQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('short-answer-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for FILL_IN_THE_BLANK', () => {
    const fillInTheBlankQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.FILL_IN_THE_BLANK,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={fillInTheBlankQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('fill-in-the-blank-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for TRUE_FALSE', () => {
    const trueFalseQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.TRUE_FALSE,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={trueFalseQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('true-false-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for MATCHING', () => {
    const matchingQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.MATCHING,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={matchingQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('matching-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for SEQUENCING', () => {
    const sequencingQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.SEQUENCING,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={sequencingQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('sequencing-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for LONG_ANSWER', () => {
    const longAnswerQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.LONG_ANSWER,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={longAnswerQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('long-answer-renderer')).toBeInTheDocument();
  });

  it('renders the correct renderer for PICTURE_PROMPT', () => {
    const picturePromptQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: QuestionType.PICTURE_PROMPT,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={picturePromptQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByTestId('picture-prompt-renderer')).toBeInTheDocument();
  });

  it('renders an error message for unknown question type', () => {
    const unknownTypeQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: 'UNKNOWN_TYPE' as any,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={unknownTypeQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByText(/Unknown question type/)).toBeInTheDocument();
  });

  it('renders an error message when question type is missing', () => {
    const missingTypeQuestions = [
      {
        ...mockQuizContext.questions[0],
        type: undefined as any,
      },
    ];
    
    render(
      <QuizProvider initialQuestions={missingTypeQuestions}>
        <QuestionRenderer />
      </QuizProvider>
    );
    
    expect(screen.getByText(/Error: Question type is missing/)).toBeInTheDocument();
  });
});
