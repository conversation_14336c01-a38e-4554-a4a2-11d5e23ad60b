import prisma from '../lib/prisma';
import { hashPassword, hashPin } from '../lib/auth';

async function migratePasswords() {
  console.log('Starting password migration...');
  
  // Migrate accounts with plaintext passwords
  const accounts = await prisma.account.findMany({
    where: {
      password: { not: null },
      password_hash: null
    },
    include: {
      children: true
    }
  });

  console.log(`Found ${accounts.length} accounts to migrate`);

  for (const account of accounts) {
    if (!account.password) continue;
    
    try {
      // Generate hash and salt for account password
      const { hash: passwordHash, salt } = await hashPassword(account.password);

      // Update account with hashed password
      await prisma.account.update({
        where: { id: account.id },
        data: {
          password_hash: passwordHash,
          salt: salt,
          password: null // Clear plaintext password
        }
      });

      // Update all children's PINs using the same salt
      for (const child of account.children) {
        if (!child.pin) continue;

        const { hash: pinHash } = await hashPin(child.pin, salt);
        await prisma.child.update({
          where: { id: child.id },
          data: {
            pin_hash: pinHash,
            salt: salt,
            pin: null // Clear plaintext PIN
          }
        });
      }

      console.log(`Successfully migrated account ${account.email} and ${account.children.length} children`);
    } catch (error) {
      console.error(`Failed to migrate account ${account.email}:`, error);
    }
  }

  console.log('Migration complete!');
}

// Run the migration
migratePasswords()
  .catch(console.error)
  .finally(() => prisma.$disconnect());