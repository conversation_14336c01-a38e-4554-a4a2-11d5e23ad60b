import { Language } from '@prisma/client';
import { 
  pickPrompt, 
  pickAnswerText, 
  pickTopicText, 
  pickExplanationText 
} from '../lib/i18n';

describe('i18n helper functions', () => {
  describe('pickPrompt', () => {
    it('should return promptEn when originalLanguage is EN', () => {
      const question = {
        id: 1,
        questionId: 'q1',
        type: 'MULTIPLE_CHOICE',
        promptEn: 'English prompt',
        promptZh: '中文提示',
        promptMs: 'Malay prompt',
        originalLanguage: 'EN',
        translationState: 'COMPLETE',
        subjectId: 1,
        yearId: 1,
        unitId: 1,
        subTopicEn: 'Topic',
        subTopicZh: '主题',
        subTopicMs: 'Topik',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      expect(pickPrompt(question)).toBe('English prompt');
    });
    
    it('should return promptZh when originalLanguage is ZH', () => {
      const question = {
        id: 1,
        questionId: 'q1',
        type: 'MULTIPLE_CHOICE',
        promptEn: 'English prompt',
        promptZh: '中文提示',
        promptMs: 'Malay prompt',
        originalLanguage: 'ZH',
        translationState: 'COMPLETE',
        subjectId: 1,
        yearId: 1,
        unitId: 1,
        subTopicEn: 'Topic',
        subTopicZh: '主题',
        subTopicMs: 'Topik',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      expect(pickPrompt(question)).toBe('中文提示');
    });
    
    it('should return promptMs when originalLanguage is MS', () => {
      const question = {
        id: 1,
        questionId: 'q1',
        type: 'MULTIPLE_CHOICE',
        promptEn: 'English prompt',
        promptZh: '中文提示',
        promptMs: 'Malay prompt',
        originalLanguage: 'MS',
        translationState: 'COMPLETE',
        subjectId: 1,
        yearId: 1,
        unitId: 1,
        subTopicEn: 'Topic',
        subTopicZh: '主题',
        subTopicMs: 'Topik',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      expect(pickPrompt(question)).toBe('Malay prompt');
    });
    
    it('should default to promptZh when originalLanguage is not specified', () => {
      const question = {
        id: 1,
        questionId: 'q1',
        type: 'MULTIPLE_CHOICE',
        promptEn: 'English prompt',
        promptZh: '中文提示',
        subjectId: 1,
        yearId: 1,
        unitId: 1,
        subTopicEn: 'Topic',
        subTopicZh: '主题',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      expect(pickPrompt(question)).toBe('中文提示');
    });
    
    it('should handle missing prompt for the original language', () => {
      const question = {
        id: 1,
        questionId: 'q1',
        type: 'MULTIPLE_CHOICE',
        promptEn: 'English prompt',
        promptZh: '',
        originalLanguage: 'ZH',
        translationState: 'PARTIAL',
        subjectId: 1,
        yearId: 1,
        unitId: 1,
        subTopicEn: 'Topic',
        subTopicZh: '主题',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      expect(pickPrompt(question)).toBe('');
    });
  });
  
  describe('pickAnswerText', () => {
    it('should return textEn when language is EN', () => {
      const answer = {
        textEn: 'English answer',
        textZh: '中文答案',
        textMs: 'Malay answer'
      };
      
      expect(pickAnswerText(answer, Language.EN)).toBe('English answer');
    });
    
    it('should return textZh when language is ZH', () => {
      const answer = {
        textEn: 'English answer',
        textZh: '中文答案',
        textMs: 'Malay answer'
      };
      
      expect(pickAnswerText(answer, Language.ZH)).toBe('中文答案');
    });
    
    it('should return textMs when language is MS', () => {
      const answer = {
        textEn: 'English answer',
        textZh: '中文答案',
        textMs: 'Malay answer'
      };
      
      expect(pickAnswerText(answer, Language.MS)).toBe('Malay answer');
    });
    
    it('should handle missing text for the requested language', () => {
      const answer = {
        textEn: 'English answer',
        textZh: '中文答案'
      };
      
      expect(pickAnswerText(answer, Language.MS)).toBe('English answer');
    });
  });
  
  describe('pickTopicText', () => {
    it('should return topicEn when language is EN', () => {
      const topic = {
        topicEn: 'English topic',
        topicZh: '中文主题',
        topicMs: 'Malay topic'
      };
      
      expect(pickTopicText(topic, Language.EN)).toBe('English topic');
    });
    
    it('should return topicZh when language is ZH', () => {
      const topic = {
        topicEn: 'English topic',
        topicZh: '中文主题',
        topicMs: 'Malay topic'
      };
      
      expect(pickTopicText(topic, Language.ZH)).toBe('中文主题');
    });
  });
  
  describe('pickExplanationText', () => {
    it('should handle null explanation', () => {
      expect(pickExplanationText(null, Language.EN)).toBe('');
    });
    
    it('should return textEn when language is EN', () => {
      const explanation = {
        textEn: 'English explanation',
        textZh: '中文解释',
        textMs: 'Malay explanation'
      };
      
      expect(pickExplanationText(explanation, Language.EN)).toBe('English explanation');
    });
  });
});
