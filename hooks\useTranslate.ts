import { useState } from 'react';
import { Question } from '../types/quiz';

interface ContextMenu {
  x: number;
  y: number;
  text: string;
}

interface UseTranslateReturn {
  contextMenu: ContextMenu | null;
  translatedText: string | null;
  onMouseUp: (e: React.MouseEvent) => void;
  onTranslate: () => Promise<void>;
  clearContextMenu: () => void;
  menuProps: {
    style: {
      position: 'fixed';
      top: number;
      left: number;
      zIndex: number;
    };
    className: string;
    onClick: (e: React.MouseEvent) => void;
  } | null;
}

/**
 * Hook to manage text selection and translation functionality
 * @param currentQuestion The current question being displayed
 * @param displayLanguage The current display language ('en' | 'zh')
 * @returns Functions and state for translation functionality
 */
export function useTranslate(
  currentQuestion: Question | undefined,
  displayLanguage: 'en' | 'zh'
): UseTranslateReturn {
  const [contextMenu, setContextMenu] = useState<ContextMenu | null>(null);
  const [translatedText, setTranslatedText] = useState<string | null>(null);

  // Handle mouse up for text selection
  const onMouseUp = (e: React.MouseEvent) => {
    const selectedText = window.getSelection()?.toString().trim();
    if (!selectedText) return;

    // Check if the target element is within a question or choice container
    const target = e.target as HTMLElement;
    const isQuestionContent = target.closest('[data-question-content="true"]');
    const isChoiceContent = target.closest('[data-choice-content="true"]');

    if (isQuestionContent || isChoiceContent) {
      setContextMenu({ x: e.pageX, y: e.pageY, text: selectedText });
      setTranslatedText(null); // Clear previous translation
    }
  };

  // Clear context menu
  const clearContextMenu = () => {
    setContextMenu(null);
  };

  // Handle translation
  const onTranslate = async () => {
    if (contextMenu && currentQuestion) {
      setTranslatedText('Translating...'); // Show loading indicator
      try {
        const response = await fetch('/api/ai-translator', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            highlightedText: contextMenu.text,
            question: currentQuestion.promptZh
          }),
        });

        const data = await response.json();
        if (data.translatedText) {
          setTranslatedText(data.translatedText); // Display translated text

          // Log the translation
          try {
            // TODO: Get actual childId from auth context or similar
            const childId = 1; // Placeholder childId

            await fetch('/api/log-translation', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                childId: childId,
                questionId: currentQuestion.id,
                translatedText: contextMenu.text,
              }),
            });
          } catch (logError) {
            console.error('Error logging translation:', logError);
          }
        } else {
          setTranslatedText('Translation failed.'); // Show error message
        }
      } catch (error) {
        console.error('Translation error:', error);
        setTranslatedText('Translation failed.'); // Show error message
      }
    }
  };

  // Prepare menu props if context menu is active
  const menuProps = contextMenu ? {
    style: {
      position: 'fixed' as const,
      top: contextMenu.y,
      left: contextMenu.x,
      zIndex: 1000
    },
    className: "bg-white shadow-md rounded p-2",
    onClick: (e: React.MouseEvent) => e.stopPropagation()
  } : null;

  return {
    contextMenu,
    translatedText,
    onMouseUp,
    onTranslate,
    clearContextMenu,
    menuProps
  };
}

export default useTranslate;
