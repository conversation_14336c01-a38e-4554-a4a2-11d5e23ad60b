import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { signIn, useSession } from 'next-auth/react';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [username, setUsername] = useState('');
  const [pin, setPin] = useState('');
  const [loginType, setLoginType] = useState<'parent' | 'child'>('parent');
  const [loginError, setLoginError] = useState<string | null>(null); // Use local state for login errors
  const { data: session, status } = useSession(); // Use useSession hook
  const router = useRouter();

  useEffect(() => {
    // Redirect if authenticated
    if (status === 'authenticated') {
      switch (session.user?.role) {
        case 'PARENT':
          router.push('/parent');
          break;
        case 'CHILD':
          router.push('/student-dashboard'); // Changed from '/dashboard' to '/student-dashboard'
          break;
        case 'ADMIN':
          router.push('/admin');
          break;
        default:
          router.push('/'); // Default redirect
          break;
      }
    }
  }, [session, status, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError(null); // Clear previous errors

    let credentials = {};
    if (loginType === 'parent') {
      if (!email || !password) {
        setLoginError('Please enter both email and password.');
        return;
      }
      credentials = { email, password };
    } else {
      if (!username || !pin) {
        setLoginError('Please enter both username and PIN.');
        return;
      }
      credentials = { username, pin };
    }

    const result = await signIn('credentials', {
      ...credentials,
      redirect: false, // Prevent default redirect
      callbackUrl: '/', // Specify a default callback URL (can be overridden by NextAuth.js)
    });

    if (result?.error) {
      setLoginError(result.error);
    }
    // Redirection is handled by the useEffect hook based on session status
  };

  // Show loading state while session is loading
  if (status === 'loading') {
    return <div>Loading...</div>;
  }

  // If authenticated, useEffect will handle redirection, so no need to render login form
  if (status === 'authenticated') {
    return <div>Redirecting...</div>;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <Head>
        <title>Login - My Quiz App</title>
        <meta name="description" content="Login to My Quiz App" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold text-center mb-6">Login</h2>

        {/* Login Type Toggle */}
        <div className="flex mb-6 border rounded overflow-hidden">
          <button
            type="button"
            className={`flex-1 py-2 text-center ${
              loginType === 'parent' ? 'bg-blue-500 text-white' : 'bg-gray-100'
            }`}
            onClick={() => {
              setLoginType('parent');
              setUsername('');
              setPin('');
              setLoginError(null); // Clear error on type change
            }}
          >
            Parent Login
          </button>
          <button
            type="button"
            className={`flex-1 py-2 text-center ${
              loginType === 'child' ? 'bg-blue-500 text-white' : 'bg-gray-100'
            }`}
            onClick={() => {
              setLoginType('child');
              setEmail('');
              setPassword('');
              setLoginError(null); // Clear error on type change
            }}
          >
            Child Login
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Parent Login Fields */}
          {loginType === 'parent' && (
            <>
              <div className="mb-4">
                <label htmlFor="email" className="block text-gray-700 text-sm font-bold mb-2">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="mb-6">
                <label htmlFor="password" className="block text-gray-700 text-sm font-bold mb-2">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
            </>
          )}

          {/* Child Login Fields */}
          {loginType === 'child' && (
            <>
              <div className="mb-4">
                <label htmlFor="username" className="block text-gray-700 text-sm font-bold mb-2">
                  Username
                </label>
                <input
                  type="text"
                  id="username"
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  required
                />
              </div>
              <div className="mb-6">
                <label htmlFor="pin" className="block text-gray-700 text-sm font-bold mb-2">
                  PIN
                </label>
                <input
                  type="password"
                  id="pin"
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline"
                  value={pin}
                  onChange={(e) => setPin(e.target.value)}
                  required
                />
              </div>
            </>
          )}
          {loginError && <p className="text-red-500 text-xs italic mb-4">{loginError}</p>}
          <div className="flex items-center justify-between">
            <button
              type="submit"
              className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Sign In
            </button>
            <Link href="/register" className="inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800">
              Don't have an account? Register
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}
