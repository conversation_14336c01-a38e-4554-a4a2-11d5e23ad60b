import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../../lib/prisma';
import { getSession } from 'next-auth/react';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  // Add this check
  if (!prisma) {
    console.error('Prisma client is not initialized.');
    return res.status(500).json({ message: 'Database client not available' });
  }

  try {
    // Find all incomplete quiz attempts
    const incompleteQuizzes = await prisma.quizAttempt.findMany({
      where: {
        endTime: null, // Quiz is incomplete if endTime is null
      },
      select: {
        id: true,
        startTime: true,
        currentQuestionIndex: true,
        child: {
          select: {
            id: true, // Include child ID
            name: true,
          },
        },
        subject: {
          select: {
            name: true,
          },
        },
        unit: {
          select: {
            unitNumber: true,
          },
        },
      },
      orderBy: {
        startTime: 'desc', // Order by most recent attempts first
      },
    });

    // Format the output to include elapsed time and attempted questions count
    const formattedIncompleteQuizzes = incompleteQuizzes.map(quiz => ({
      id: quiz.id,
      childId: quiz.child.id, // Include child ID
      childName: quiz.child.name,
      subject: quiz.subject.name,
      unit: quiz.unit?.unitNumber,
      attemptedQuestions: quiz.currentQuestionIndex,
      // Removed totalQuestions and quizType as they are not in the schema
      startTime: quiz.startTime,
      elapsedTimeSeconds: Math.floor((Date.now() - new Date(quiz.startTime).getTime()) / 1000),
    }));


    res.status(200).json({ incompleteQuizzes: formattedIncompleteQuizzes });

  } catch (error) {
    console.error('Error fetching incomplete quizzes for admin:', error);
    res.status(500).json({ message: 'Error fetching incomplete quizzes' });
  }
}
