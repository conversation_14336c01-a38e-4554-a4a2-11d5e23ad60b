import React from 'react';
import { Keywords } from '../../types/quiz';
import { Language } from '@prisma/client';
import Highlighter from 'react-highlight-words';

interface HighlightedTextProps {
  text: string;
  keywords?: Keywords | null;
  language: Language;
  showHighlights?: boolean;
  showKeywords?: boolean; // Added for compatibility with existing code
  className?: string;
}

/**
 * Component that displays text with highlighted keywords using react-highlight-words
 */
const HighlightedText: React.FC<HighlightedTextProps> = ({
  text,
  keywords,
  language,
  showHighlights,
  showKeywords,
  className = ''
}) => {
  // Use either showHighlights or showKeywords (for backward compatibility)
  const shouldHighlight = showHighlights || showKeywords;

  // If highlighting is disabled or there are no keywords, just return the text
  if (!shouldHighlight || !keywords) {
    return <span className={className}>{text}</span>;
  }

  // Get the keywords for the current language
  let keywordsToHighlight: string[] = [];

  switch (language) {
    case Language.EN:
      keywordsToHighlight = keywords.en || [];
      break;
    case Language.ZH:
      keywordsToHighlight = keywords.zh || [];
      break;
    case Language.MS:
      keywordsToHighlight = keywords.ms || [];
      break;
    default:
      keywordsToHighlight = [];
  }

  // If no keywords for this language, just return the text
  if (keywordsToHighlight.length === 0) {
    return <span className={className}>{text}</span>;
  }

  // Return the text with highlighted keywords using react-highlight-words
  return (
    <Highlighter
      highlightClassName="bg-yellow-200 text-black px-1 rounded"
      searchWords={keywordsToHighlight}
      autoEscape={true}
      textToHighlight={text}
      className={className}
    />
  );
};

export default HighlightedText;
