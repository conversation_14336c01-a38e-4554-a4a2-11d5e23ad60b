import React, { useState, useEffect } from 'react';
import { QuizMode, QuestionType } from '@prisma/client';
import { mutate } from 'swr';

interface QuizConfigCardProps {
  mode: QuizMode;
  config: {
    id: number;
    mode: QuizMode;
    numQuestions: number;
    questionTypes: string[];
    allowTranslate: boolean;
    allowHints: boolean;
    allowAiTutor: boolean;
    updatedAt: string;
  };
}

const QuizConfigCard: React.FC<QuizConfigCardProps> = ({ mode, config }) => {
  // State for form values
  const [numQuestions, setNumQuestions] = useState(config.numQuestions);
  const [questionTypes, setQuestionTypes] = useState<string[]>(config.questionTypes);
  const [allowTranslate, setAllowTranslate] = useState(config.allowTranslate);
  const [allowHints, setAllowHints] = useState(config.allowHints);
  const [allowAiTutor, setAllowAiTutor] = useState(config.allowAiTutor);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState('');
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Update state when config changes
  useEffect(() => {
    setNumQuestions(config.numQuestions);
    setQuestionTypes(config.questionTypes);
    setAllowTranslate(config.allowTranslate);
    setAllowHints(config.allowHints);
    setAllowAiTutor(config.allowAiTutor);
  }, [config]);

  // All available question types
  const allQuestionTypes = Object.values(QuestionType);

  // Handle checkbox change for question types
  const handleQuestionTypeChange = (type: string) => {
    if (questionTypes.includes(type)) {
      setQuestionTypes(questionTypes.filter(t => t !== type));
    } else {
      setQuestionTypes([...questionTypes, type]);
    }
  };

  // Validation
  const isValid = numQuestions >= 1 && numQuestions <= 100 && questionTypes.length > 0;

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isValid) return;
    
    setIsSaving(true);
    setSaveError('');
    setSaveSuccess(false);
    
    try {
      const response = await fetch('/api/admin/quiz-config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode,
          numQuestions,
          questionTypes,
          allowTranslate,
          allowHints,
          allowAiTutor,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save configuration');
      }
      
      const updatedConfig = await response.json();
      
      // Update the SWR cache
      mutate('/api/admin/quiz-config');
      
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (error) {
      setSaveError(error.message || 'An error occurred while saving');
    } finally {
      setIsSaving(false);
    }
  };

  // Determine card header color based on mode
  const headerColor = mode === 'MASTERY' ? 'bg-blue-600' : 'bg-green-600';
  const buttonColor = mode === 'MASTERY' ? 'bg-blue-600 hover:bg-blue-700' : 'bg-green-600 hover:bg-green-700';
  const buttonDisabledColor = mode === 'MASTERY' ? 'bg-blue-300' : 'bg-green-300';

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className={`${headerColor} text-white p-4`}>
        <h2 className="text-xl font-bold">{mode} Quiz Configuration</h2>
        <p className="text-sm opacity-80">
          Last updated: {new Date(config.updatedAt).toLocaleString()}
        </p>
      </div>
      
      <form onSubmit={handleSubmit} className="p-6">
        {/* Number of Questions */}
        <div className="mb-6">
          <label htmlFor={`numQuestions-${mode}`} className="block text-sm font-medium text-gray-700 mb-1">
            Number of Questions
          </label>
          <input
            type="number"
            id={`numQuestions-${mode}`}
            min="1"
            max="100"
            value={numQuestions}
            onChange={(e) => setNumQuestions(parseInt(e.target.value) || 1)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {(numQuestions < 1 || numQuestions > 100) && (
            <p className="mt-1 text-sm text-red-600">Number of questions must be between 1 and 100</p>
          )}
        </div>
        
        {/* Question Types */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Question Types
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {allQuestionTypes.map((type) => (
              <div key={type} className="flex items-center">
                <input
                  type="checkbox"
                  id={`${type}-${mode}`}
                  checked={questionTypes.includes(type)}
                  onChange={() => handleQuestionTypeChange(type)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={`${type}-${mode}`} className="ml-2 block text-sm text-gray-700">
                  {type.replace(/_/g, ' ')}
                </label>
              </div>
            ))}
          </div>
          {questionTypes.length === 0 && (
            <p className="mt-1 text-sm text-red-600">Select at least one question type</p>
          )}
        </div>
        
        {/* Toggle Switches */}
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Allow Translation</span>
            <button
              type="button"
              onClick={() => setAllowTranslate(!allowTranslate)}
              className={`${
                allowTranslate ? 'bg-blue-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
            >
              <span
                className={`${
                  allowTranslate ? 'translate-x-6' : 'translate-x-1'
                } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
              />
            </button>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Allow Hints</span>
            <button
              type="button"
              onClick={() => setAllowHints(!allowHints)}
              className={`${
                allowHints ? 'bg-blue-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
            >
              <span
                className={`${
                  allowHints ? 'translate-x-6' : 'translate-x-1'
                } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
              />
            </button>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Allow AI Tutor</span>
            <button
              type="button"
              onClick={() => setAllowAiTutor(!allowAiTutor)}
              className={`${
                allowAiTutor ? 'bg-blue-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
            >
              <span
                className={`${
                  allowAiTutor ? 'translate-x-6' : 'translate-x-1'
                } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
              />
            </button>
          </div>
        </div>
        
        {/* Save Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={!isValid || isSaving}
            className={`px-4 py-2 text-white font-semibold rounded-md ${
              isValid && !isSaving ? buttonColor : buttonDisabledColor
            }`}
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
        
        {/* Error and Success Messages */}
        {saveError && (
          <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
            {saveError}
          </div>
        )}
        
        {saveSuccess && (
          <div className="mt-4 p-3 bg-green-100 text-green-700 rounded-md">
            Configuration saved successfully!
          </div>
        )}
      </form>
    </div>
  );
};

export default QuizConfigCard;
