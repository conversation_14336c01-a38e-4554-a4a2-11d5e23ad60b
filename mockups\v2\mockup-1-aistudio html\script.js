const questions = [
    {
        questionText: "Which part of a plant absorbs water and nutrients from the soil?",
        options: ["Leaves", "Stem", "Roots", "Flower"],
        correctAnswer: "Roots",
        explanation: "Roots anchor the plant and absorb water and nutrients from the soil."
    },
    {
        questionText: "What is the force that pulls objects towards the center of the Earth?",
        options: ["Magnetism", "Friction", "Gravity", "Buoyancy"],
        correctAnswer: "Gravity",
        explanation: "Gravity is the force that keeps us on the ground and makes objects fall."
    },
    {
        questionText: "Which of these is a source of light?",
        options: ["Moon", "Book", "Mirror", "Sun"],
        correctAnswer: "Sun",
        explanation: "The Sun is a star and produces its own light. The Moon reflects sunlight."
    },
    {
        questionText: "What state of matter is ice?",
        options: ["Solid", "Liquid", "Gas", "Plasma"],
        correctAnswer: "Solid",
        explanation: "Ice is the solid state of water."
    },
    {
        questionText: "Animals that eat only plants are called:",
        options: ["Carnivores", "Herbivores", "Omnivores", "Decomposers"],
        correctAnswer: "Herbivores",
        explanation: "Herbivores, like cows and rabbits, feed on plants."
    }
];

const progressBar = document.getElementById('progressBar');
const questionTitleEl = document.getElementById('questionTitle');
const questionTextEl = document.getElementById('questionText');
const answerOptionsEl = document.getElementById('answerOptions');
const actionButton = document.getElementById('actionButton');
const feedbackMessageEl = document.getElementById('feedbackMessage');
// Removed livesCountEl

let currentQuestionIndex = 0;
let selectedAnswer = null;
// Removed lives variable
let quizOver = false;

function loadQuestion() {
    if (currentQuestionIndex >= questions.length) {
        showQuizEnd();
        return;
    }
    quizOver = false;
    selectedAnswer = null;
    const currentQuestion = questions[currentQuestionIndex];

    questionTitleEl.textContent = "Select the correct answer";
    questionTextEl.textContent = currentQuestion.questionText;
    answerOptionsEl.innerHTML = '';

    currentQuestion.options.forEach(option => {
        const button = document.createElement('button');
        button.classList.add('option-btn');
        button.textContent = option;
        button.addEventListener('click', () => selectOption(button, option));
        answerOptionsEl.appendChild(button);
    });

    actionButton.textContent = 'CHECK';
    actionButton.disabled = true;
    actionButton.classList.remove('active', 'continue', 'got-it');
    feedbackMessageEl.style.display = 'none';
    feedbackMessageEl.className = 'feedback-message';

    updateProgressBar();
}

function selectOption(buttonEl, option) {
    if (quizOver) return;

    const allOptions = answerOptionsEl.querySelectorAll('.option-btn');
    allOptions.forEach(btn => btn.classList.remove('selected'));

    buttonEl.classList.add('selected');
    selectedAnswer = option;
    actionButton.disabled = false;
    actionButton.classList.add('active');
}

function checkAnswer() {
    if (!selectedAnswer) return;
    quizOver = true;

    const currentQuestion = questions[currentQuestionIndex];
    const allOptionButtons = Array.from(answerOptionsEl.querySelectorAll('.option-btn'));
    const selectedButton = allOptionButtons.find(btn => btn.textContent === selectedAnswer);
    const correctButton = allOptionButtons.find(btn => btn.textContent === currentQuestion.correctAnswer);

    actionButton.classList.remove('active');

    if (selectedAnswer === currentQuestion.correctAnswer) {
        selectedButton.classList.add('correct');
        feedbackMessageEl.innerHTML = `<strong>Correct!</strong><br>${currentQuestion.explanation}`; // Updated message
        feedbackMessageEl.classList.add('correct');
        actionButton.textContent = 'CONTINUE';
        actionButton.classList.add('continue');
    } else {
        selectedButton.classList.add('incorrect');
        if (correctButton) {
            correctButton.classList.add('show-correct');
        }
        feedbackMessageEl.innerHTML = `<strong>Incorrect.</strong><br>Correct Answer: ${currentQuestion.correctAnswer}<br><em>${currentQuestion.explanation}</em>`;
        feedbackMessageEl.classList.add('incorrect');
        actionButton.textContent = 'GOT IT';
        actionButton.classList.add('got-it');
        
        // Removed lives decrement and update
    }
    feedbackMessageEl.style.display = 'block';
    actionButton.disabled = false;
}

function nextQuestion() {
    currentQuestionIndex++;
    loadQuestion();
}

function updateProgressBar() {
    const progress = (currentQuestionIndex / questions.length) * 100;
    progressBar.style.width = `${progress}%`;
}

function showQuizEnd() {
    questionTitleEl.textContent = "Quiz Complete!";
    // Updated message to remove lives
    questionTextEl.textContent = `You have finished all the questions. Well done!`; 
    answerOptionsEl.innerHTML = '';
    actionButton.textContent = 'RESTART';
    actionButton.disabled = false;
    actionButton.classList.add('continue');
    actionButton.onclick = () => {
        currentQuestionIndex = 0;
        // Removed lives reset
        loadQuestion();
        actionButton.onclick = handleActionButtonClick;
    };
    feedbackMessageEl.style.display = 'none';
    progressBar.style.width = `100%`;
}


function handleActionButtonClick() {
    if (actionButton.textContent === 'CHECK') {
        checkAnswer();
    } else if (actionButton.textContent === 'CONTINUE' || actionButton.textContent === 'GOT IT') {
        nextQuestion();
    }
}

actionButton.addEventListener('click', handleActionButtonClick);

// Initialize
loadQuestion();