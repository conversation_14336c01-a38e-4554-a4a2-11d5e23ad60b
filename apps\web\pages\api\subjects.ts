import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';

/**
 * API endpoint for fetching all subjects
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    const subjects = await prisma.subject.findMany({
      orderBy: {
        name: 'asc',
      },
    });

    res.status(200).json(subjects);
  } catch (error) {
    console.error('Error fetching subjects:', error);
    res.status(500).json({ message: 'Failed to fetch subjects' });
  }
}
