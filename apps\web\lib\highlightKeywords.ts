import { Keywords } from '../types/quiz';
import { Language } from '@prisma/client';

/**
 * Highlights keywords in a text by wrapping them in a span with a highlight class
 * @param text The text to highlight keywords in
 * @param keywords The keywords to highlight
 * @param language The language of the text
 * @returns The text with highlighted keywords as HTML
 */
export function highlightKeywords(
  text: string,
  keywords: Keywords | null | undefined,
  language: Language
): string {
  if (!text || !keywords) return text;

  // Get the keywords for the current language
  let keywordsToHighlight: string[] = [];
  
  switch (language) {
    case Language.EN:
      keywordsToHighlight = keywords.en || [];
      break;
    case Language.ZH:
      keywordsToHighlight = keywords.zh || [];
      break;
    case Language.MS:
      keywordsToHighlight = keywords.ms || [];
      break;
    default:
      return text;
  }

  if (keywordsToHighlight.length === 0) return text;

  // Sort keywords by length (longest first) to avoid highlighting parts of longer keywords
  keywordsToHighlight.sort((a, b) => b.length - a.length);

  // Create a regex pattern that matches any of the keywords
  // Use word boundaries for English, but not for Chinese or Malay
  const patterns = keywordsToHighlight.map(keyword => {
    if (language === Language.EN) {
      // For English, use word boundaries
      return `\\b${escapeRegExp(keyword)}\\b`;
    } else {
      // For other languages, don't use word boundaries
      return escapeRegExp(keyword);
    }
  });

  const pattern = patterns.join('|');
  const regex = new RegExp(pattern, 'gi');

  // Replace keywords with highlighted versions
  return text.replace(regex, match => `<span class="keyword-highlight">${match}</span>`);
}

/**
 * Escapes special characters in a string for use in a regular expression
 * @param string The string to escape
 * @returns The escaped string
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
