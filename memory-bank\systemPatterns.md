# System Patterns

This document describes the system architecture, key technical decisions, and design patterns used in the My Quiz App project.

## Architecture
- The project is a Next.js application with a React frontend and Node.js backend API routes.
- Data persistence is handled by a PostgreSQL database using Prisma as the ORM.
- New database tables (`TG_TranslationLog`, `TG_QuizAttempt`, `TG_StudentAnswer`) have been added for analytics.

## Key Technical Decisions
- Use of Next.js for server-side rendering and API routes.
- Implementation of user authentication.
- Integration of AI features (translator, tutor).
- Implementation of analytics logging and display using new database models and API routes.

## Design Patterns
- Component-based architecture for the frontend.
- API endpoints for backend logic and data fetching.
- Use of Prisma for database interactions.

## Component Relationships
- Frontend components (`components/Quiz.tsx`, `components/ParentPortal.tsx`, `components/AdminDashboard.tsx`) interact with API routes to fetch and submit data.
- New API routes (`pages/api/log-translation.ts`, `pages/api/analytics/translations.ts`, `pages/api/analytics/knowledge.ts`, `pages/api/log-quiz-attempt.ts`) interact with the database through Prisma to log translation events, quiz attempts/answers, and fetch analytics data.
- Existing API routes interact with the database through Prisma for curriculum and account management.

## Critical Implementation Paths
- User authentication flow (registration, login, session management).
- Quiz taking process (fetching questions, submitting answers, displaying results, logging translations, logging quiz attempts and answers).
- Admin workflows (adding/editing content, viewing analytics).
- Parent workflows (viewing child progress and analytics).
- Data flow for analytics from quiz taking to database logging and then to dashboard display.
