import { NextApiRequest, NextApiResponse } from 'next';
import prisma from '../../lib/prisma';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth/[...nextauth]';

/**
 * API endpoint for fetching subjects for a specific year
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { yearNumber } = req.query;

  if (!yearNumber || typeof yearNumber !== 'string') {
    return res.status(400).json({ message: 'Year number is required' });
  }

  try {
    // Convert year string (e.g., "Year 5") to number (e.g., 5)
    let yearNum: number;
    
    if (yearNumber.startsWith('Year ')) {
      const yearStr = yearNumber.replace('Year ', '');
      yearNum = parseInt(yearStr, 10);
    } else {
      yearNum = parseInt(yearNumber, 10);
    }
    
    if (isNaN(yearNum)) {
      return res.status(400).json({ message: 'Invalid year format' });
    }

    // Find the year record
    const year = await prisma.year.findUnique({
      where: {
        yearNumber: yearNum,
      },
    });

    if (!year) {
      return res.status(404).json({ message: 'Year not found' });
    }

    // Find all units for this year and group by subject
    const units = await prisma.unit.findMany({
      where: {
        yearId: year.id,
      },
      include: {
        subject: true,
      },
    });

    // Extract unique subjects
    const subjectMap = new Map();
    units.forEach(unit => {
      if (!subjectMap.has(unit.subject.id)) {
        subjectMap.set(unit.subject.id, {
          id: unit.subject.id,
          name: unit.subject.name,
          unitCount: 0,
          completedCount: 0, // This would be populated from user data in a real implementation
        });
      }
      subjectMap.get(unit.subject.id).unitCount++;
    });

    // Convert map to array
    const subjects = Array.from(subjectMap.values());

    res.status(200).json(subjects);
  } catch (error) {
    console.error(`Error fetching subjects for year ${yearNumber}:`, error);
    res.status(500).json({ message: 'Failed to fetch subjects' });
  }
}
