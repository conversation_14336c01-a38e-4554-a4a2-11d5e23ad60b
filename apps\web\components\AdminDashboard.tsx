import { useState, useEffect, useRef } from 'react';
import AdminQuestionsPage from '../pages/admin/questions';
import NotesUploader from './admin/NotesUploader';
import QuestionGenerator from './admin/QuestionGenerator';
import ChildConfigModal from './admin/ChildConfigModal';
import ResetPinModal from './admin/ResetPinModal';
import Link from 'next/link';
import { useSession, signOut } from 'next-auth/react';
import { Language } from '@prisma/client';

interface Unit {
  id: number;
  unitNumber: number;
  topicEn: string;
  topicZh: string;
}

interface Subject {
  id: number;
  name: string;
  units: Unit[];
}

interface Year {
  id: number;
  yearNumber: number;
  subjects: Subject[];
}

interface Child {
  id: number;
  name: string;
  year: string;
  username: string;
  quizLanguage?: Language;
  menuLanguage?: Language;
}

interface Account {
  id: number;
  name: string;
  role: string;
  email: string;
  status: string;
  license?: {
    type: string;
    expiry?: string;
  };
  children: Child[];
}

const sampleParentData = [
  {
    id: 1,
    name: 'Parent 1',
    children: [
      { id: 1, name: 'Child 1' },
      { id: 2, name: 'Child 2' }
    ]
  },
  {
    id: 2,
    name: 'Parent 2',
    children: [
      { id: 3, name: 'Child 3' }
    ]
  }
];

export default function AdminDashboard() {
  const [years, setYears] = useState<Year[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [expandedYears, setExpandedYears] = useState<number[]>([]);
  const [expandedSubjects, setExpandedSubjects] = useState<number[]>([]);
  const [uploadStatus, setUploadStatus] = useState<{ type: 'success' | 'error', message: string } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [expandedParents, setExpandedParents] = useState(false);
  const [activeMenu, setActiveMenu] = useState<'accounts' | 'learning' | 'questions' | 'notes' | 'generator' | 'quiz-config' | 'flagged-questions'>('accounts');
  const [selectedAccount, setSelectedAccount] = useState<Account | null>(null);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isAddAccountDialogOpen, setIsAddAccountDialogOpen] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [newAccount, setNewAccount] = useState({
    name: '', // Updated to use 'name' instead of 'parent.fullName'
    email: '',
    password: '',
    children: [{ name: '', year: 'Year 1', username: '', pin: '' }],
    license: { type: 'FREE_TRIAL', duration: 14 },
  });

  // New state for admin analytics data
  const [adminTranslationAnalytics, setAdminTranslationAnalytics] = useState<any>(null);
  const [adminKnowledgeAnalytics, setAdminKnowledgeAnalytics] = useState<any>(null);
  const [loadingAdminAnalytics, setLoadingAdminAnalytics] = useState(false);
  const [adminAnalyticsError, setAdminAnalyticsError] = useState<string | null>(null);

  // State for admin incomplete quizzes
  const [adminIncompleteQuizzes, setAdminIncompleteQuizzes] = useState<any[]>([]);
  const [loadingAdminIncompleteQuizzes, setLoadingAdminIncompleteQuizzes] = useState(true);
  const [adminIncompleteQuizzesError, setAdminIncompleteQuizzesError] = useState<string | null>(null);

  // State for child configuration
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [isChildConfigModalOpen, setIsChildConfigModalOpen] = useState(false);
  const [isResetPinModalOpen, setIsResetPinModalOpen] = useState(false);
  const [childConfigError, setChildConfigError] = useState<string | null>(null);

  // State for user menu dropdown
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);

  // Get session data
  const { data: session } = useSession();


  const handleAddChild = () => {
    setNewAccount((prev) => ({
      ...prev,
      children: [...prev.children, { name: '', year: 'Year 1', username: '', pin: '' }],
    }));
  };

  const handleRemoveChild = (index: number) => {
    setNewAccount((prev) => ({
      ...prev,
      children: prev.children.filter((_, i) => i !== index),
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>, field: string, index?: number) => {
    if (index !== undefined) {
      const updatedChildren = [...newAccount.children];
      updatedChildren[index] = { ...updatedChildren[index], [field]: e.target.value };
      setNewAccount((prev) => ({ ...prev, children: updatedChildren }));
    } else {
      setNewAccount((prev) => ({
        ...prev,
        [field]: e.target.value, // Updated to directly modify 'name', 'email', or 'password'
      }));
    }
  };

  // Update type parameter to accept specific enum values
  const handleLicenseChange = (type: 'FREE_TRIAL' | 'STANDARD_PLAN', duration?: number) => {
    setNewAccount((prev) => ({
      ...prev,
      // Ensure duration is only set for FREE_TRIAL, null otherwise
      license: { type, duration: type === 'FREE_TRIAL' ? (duration ?? prev.license.duration) : null },
    }));
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Function to fetch accounts data
  const fetchAccountsData = async () => {
    try {
      const response = await fetch('/api/admin/accounts');
      if (!response.ok) {
        // Consider setting an error state here as well
        console.error('Failed to fetch accounts');
        throw new Error('Failed to fetch accounts');
      }
      const data = await response.json();
      setAccounts(data);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      // Set an error state to display to the user
      setError('Failed to fetch accounts. Please try again later.');
    }
  };

  // Fetch accounts data on initial mount
  useEffect(() => {
    fetchAccountsData();
  }, []);

  // New useEffect to fetch admin analytics data when the 'learning' tab is active
  useEffect(() => {
    const fetchAdminAnalytics = async () => {
      if (activeMenu === 'learning') {
        setLoadingAdminAnalytics(true);
        setAdminAnalyticsError(null);
        try {
          // Fetch translation analytics first
          try {
            const translationRes = await fetch('/api/analytics/translations');
            if (!translationRes.ok) {
              console.error('Translation analytics response not OK:', await translationRes.text());
              throw new Error('Failed to fetch translation analytics');
            }
            const translationData = await translationRes.json();
            setAdminTranslationAnalytics(translationData);
          } catch (translationError: any) {
            console.error('Error fetching translation analytics:', translationError);
            setAdminAnalyticsError(`Translation analytics error: ${translationError.message}`);
          }

          // Fetch knowledge analytics separately to isolate errors
          try {
            const knowledgeRes = await fetch('/api/analytics/knowledge');
            if (!knowledgeRes.ok) {
              console.error('Knowledge analytics response not OK:', await knowledgeRes.text());
              throw new Error('Failed to fetch knowledge analytics');
            }
            const knowledgeData = await knowledgeRes.json();
            setAdminKnowledgeAnalytics(knowledgeData);
          } catch (knowledgeError: any) {
            console.error('Error fetching knowledge analytics:', knowledgeError);
            setAdminAnalyticsError(`Knowledge analytics error: ${knowledgeError.message}`);
          }
        } catch (error: any) {
          console.error('Error in overall analytics fetch:', error);
          setAdminAnalyticsError(error.message);
        } finally {
          setLoadingAdminAnalytics(false);
        }
      }
    };

    fetchAdminAnalytics();
  }, [activeMenu]); // Rerun when activeMenu changes

  // Fetch incomplete quizzes for admin view when the 'learning' tab is active
  useEffect(() => {
    const fetchAdminIncompleteQuizzes = async () => {
      if (activeMenu === 'learning') {
        setLoadingAdminIncompleteQuizzes(true);
        setAdminIncompleteQuizzesError(null);
        try {
          const response = await fetch('/api/admin/incomplete-quizzes');
          if (!response.ok) {
            throw new Error(`Error fetching incomplete quizzes: ${response.statusText}`);
          }
          const data = await response.json();
          setAdminIncompleteQuizzes(data.incompleteQuizzes);
        } catch (error: any) {
          console.error('Error fetching admin incomplete quizzes:', error);
          setAdminIncompleteQuizzesError(error.message);
        } finally {
          setLoadingAdminIncompleteQuizzes(false);
        }
      }
    };

    fetchAdminIncompleteQuizzes();
  }, [activeMenu]); // Depend on activeMenu

  // Fetch curriculum data
  const fetchData = async () => {
    try {
      const response = await fetch('/api/admin/curriculum');
      if (!response.ok) {
        throw new Error('Failed to fetch curriculum data');
      }
      const data = await response.json();
      setYears(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const fileContent = await file.text();
      const syllabusData = JSON.parse(fileContent);

      const response = await fetch('/api/admin/upload-syllabus', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(syllabusData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to upload syllabus');
      }

      setUploadStatus({ type: 'success', message: 'Syllabus updated successfully' });
      // Refresh the data
      fetchData();
    } catch (err) {
      setUploadStatus({
        type: 'error',
        message: err instanceof Error ? err.message : 'Failed to upload syllabus'
      });
    } finally {
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const toggleYear = (yearId: number) => {
    setExpandedYears(prev =>
      prev.includes(yearId)
        ? prev.filter(id => id !== yearId)
        : [...prev, yearId]
    );
  };

  const toggleSubject = (subjectId: number) => {
    setExpandedSubjects(prev =>
      prev.includes(subjectId)
        ? prev.filter(id => id !== subjectId)
        : [...prev, subjectId]
    );
  };

  const toggleParentsPanel = () => {
    setExpandedParents((prev) => !prev);
  };

  const ChevronIcon = ({ expanded }: { expanded: boolean }) => (
    <svg
      className={`w-5 h-5 transition-transform ${expanded ? 'transform rotate-90' : ''}`}
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 5l7 7-7 7"
      />
    </svg>
  );

  if (loading) return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-gray-600">Loading...</div>
    </div>
  );

  if (error) return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-red-600">Error: {error}</div>
    </div>
  );

  const handleAddAccount = () => {
    // TODO: Implement logic to add a new account
    alert('Add Account button clicked!');
  };

  const handleResetPassword = (accountName: string) => {
    alert(`Reset password for ${accountName}`);
  };

  const handleEditAccount = (accountName: string) => {
    alert(`Edit account for ${accountName}`);
  };

  const handleDeleteAccount = (accountName: string) => {
    alert(`Delete account for ${accountName}`);
  };

  // Handler for opening the child configuration modal
  const handleConfigureChild = (child: Child) => {
    setSelectedChild(child);
    setIsChildConfigModalOpen(true);
  };

  // Handler for opening the reset PIN modal
  const handleResetChildPin = (child: Child) => {
    setSelectedChild(child);
    setIsResetPinModalOpen(true);
  };

  // Handler for updating child details
  const handleUpdateChild = async (childId: number, data: any) => {
    setChildConfigError(null);

    try {
      const response = await fetch('/api/admin/update-child', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          childId,
          ...data
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update child');
      }

      // Update the child in the accounts state
      const updatedAccounts = accounts.map(account => {
        if (account.children.some(child => child.id === childId)) {
          return {
            ...account,
            children: account.children.map(child =>
              child.id === childId
                ? { ...child, ...data }
                : child
            )
          };
        }
        return account;
      });

      setAccounts(updatedAccounts);

      // If the selected account has the updated child, update it too
      if (selectedAccount && selectedAccount.children.some(child => child.id === childId)) {
        setSelectedAccount({
          ...selectedAccount,
          children: selectedAccount.children.map(child =>
            child.id === childId
              ? { ...child, ...data }
              : child
          )
        });
      }

    } catch (error) {
      console.error('Error updating child:', error);
      setChildConfigError(error instanceof Error ? error.message : 'An unexpected error occurred');
      throw error;
    }
  };

  // Handler for resetting a child's PIN
  const handleResetPin = async (childId: number, newPin: string) => {
    setChildConfigError(null);

    try {
      const response = await fetch('/api/admin/reset-child-pin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          childId,
          newPin
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to reset PIN');
      }

    } catch (error) {
      console.error('Error resetting PIN:', error);
      setChildConfigError(error instanceof Error ? error.message : 'An unexpected error occurred');
      throw error;
    }
  };

  const handleSaveAccount = async () => {
    setSaveError(null); // Clear previous errors
    try {
      const response = await fetch('/api/admin/create-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newAccount),
      });

      if (!response.ok) {
        // If response is not OK, parse error message from body
        const errorData = await response.json();
        setSaveError(errorData.message || 'Failed to save the account. Please check the details.');
        return; // Stop execution
      }

      // If response is OK, proceed with success logic
      const result = await response.json(); // Assuming API returns the created account on success
      alert('Account successfully created!'); // Or use a more integrated notification

      // Reset the form and close the dialog
      setNewAccount({
        name: '', // Reset to empty name
        email: '',
        password: '',
        children: [{ name: '', year: 'Year 1', username: '', pin: '' }],
        license: { type: 'FREE_TRIAL', duration: 14 }, // Reset with enum value
      });
      setIsAddAccountDialogOpen(false);

      // Optionally refresh the accounts list
      // Refresh the accounts list by calling the extracted function
      fetchAccountsData();

    } catch (error) {
      // Catch network errors or unexpected issues
      console.error('Save account error:', error);
      setSaveError(error instanceof Error ? error.message : 'An unexpected error occurred.');
    }
  };

  // Function to open the dialog and reset state
  const openAddAccountDialog = () => {
    setSaveError(null); // Clear any previous save errors
    setNewAccount({ // Reset form fields
      name: '', // Reset to empty name
      email: '',
      password: '',
      children: [{ name: '', year: 'Year 1', username: '', pin: '' }],
      license: { type: 'FREE_TRIAL', duration: 14 },
    });
    setIsAddAccountDialogOpen(true);
  };

  const handleAccountClick = (account: Account) => {
    setSelectedAccount(account);
  };

  const renderSelectedAccountDetails = () => {
    if (!selectedAccount) return null;

    return (
      <div className="mt-6 bg-gray-50 p-4 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Children of {selectedAccount.name}</h2>
          <div className="text-sm text-gray-500">Total: {selectedAccount.children?.length || 0} children</div>
        </div>
        {selectedAccount.children && selectedAccount.children.length > 0 ? (
          <div className="grid gap-4">
            {selectedAccount.children.map((child) => (
              <div key={child.id} className="bg-white p-4 rounded-lg shadow-sm">
                <div className="flex justify-between">
                  <div>
                    <h3 className="font-medium">{child.name}</h3>
                    <p className="text-sm text-gray-600">Year: {child.year}</p>
                    <p className="text-sm text-gray-600">
                      Quiz Language: {child.quizLanguage || 'ZH'} |
                      Menu Language: {child.menuLanguage || 'EN'}
                    </p>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500 mb-2">
                      Username: {child.username}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleResetChildPin(child)}
                        className="text-blue-600 hover:underline text-sm px-2 py-1 border border-blue-300 rounded"
                        title="Reset PIN"
                      >
                        🔒 Reset PIN
                      </button>
                      <button
                        onClick={() => handleConfigureChild(child)}
                        className="text-green-600 hover:underline text-sm px-2 py-1 border border-green-300 rounded"
                        title="Configure Child"
                      >
                        ⚙️ Configure
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No children associated with this account.</p>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="text-2xl font-bold text-blue-600">QuizApp</div>
            <span className="text-sm bg-purple-100 text-purple-800 px-3 py-1 rounded-full">Admin Portal</span>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right mr-4">
              <div className="font-medium text-gray-900">{session?.user?.name || 'Admin'}</div>
              <div className="text-sm text-gray-500">{session?.user?.role || 'ADMIN'}</div>
            </div>
            <div className="relative">
              <button
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 focus:outline-none"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </button>
              {isUserMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10">
                  <button
                    onClick={() => signOut({ callbackUrl: '/login' })}
                    className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 focus:outline-none"
                  >
                    Sign Out
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveMenu('accounts')}
                className={`
                  ${activeMenu === 'accounts'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Accounts
              </button>
              <button
                onClick={() => setActiveMenu('learning')}
                className={`
                  ${activeMenu === 'learning'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Learning
              </button>
              <button
                onClick={() => setActiveMenu('questions')}
                className={`
                  ${activeMenu === 'questions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Questions
              </button>
              <button
                onClick={() => setActiveMenu('notes')}
                className={`
                  ${activeMenu === 'notes'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Notes
              </button>
              <button
                onClick={() => setActiveMenu('generator')}
                className={`
                  ${activeMenu === 'generator'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Question Generator
              </button>
              <Link
                href="/admin/quiz-config"
                className={`
                  ${activeMenu === 'quiz-config'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Quiz Config
              </Link>
              <Link
                href="/admin/flagged-questions"
                className={`
                  ${activeMenu === 'flagged-questions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <span>Flagged Questions</span>
                <span className="ml-2 bg-red-100 text-red-800 text-xs font-medium px-2 py-0.5 rounded-full">New</span>
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-8">
        {/* Child Configuration Modals */}
        {isChildConfigModalOpen && selectedChild && (
          <ChildConfigModal
            child={selectedChild}
            onClose={() => setIsChildConfigModalOpen(false)}
            onSave={handleUpdateChild}
          />
        )}

        {isResetPinModalOpen && selectedChild && (
          <ResetPinModal
            childId={selectedChild.id}
            childName={selectedChild.name}
            onClose={() => setIsResetPinModalOpen(false)}
            onReset={handleResetPin}
          />
        )}

        {activeMenu === 'accounts' && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Registered Accounts</h1>
              <button
                onClick={openAddAccountDialog}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold"
              >
                + Add Account
              </button>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Name</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Role</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Email</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">License</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Status</th>
                    <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {accounts.map((account, index) => (
                    <tr
                      key={index}
                      className="odd:bg-white even:bg-gray-50 hover:bg-gray-200"
                    >
                      <td
                        className="px-4 py-2 text-sm text-gray-700 cursor-pointer"
                        onClick={() => handleAccountClick(account)}
                      >{account.name}</td>
                      <td className="px-4 py-2 text-sm text-gray-700">{account.role}</td>
                      <td className="px-4 py-2 text-sm text-gray-700">{account.email}</td>
                      <td className="px-4 py-2 text-sm text-gray-700">
                        {account.license?.type === 'Trial' ? (
                          <span className="text-yellow-600">Trial (Expires {account.license.expiry})</span>
                        ) : account.license?.type === 'Standard' ? (
                          <span className="text-green-600">Standard</span>
                        ) : (
                          <span className="text-gray-600">No License</span>
                        )}
                      </td>
                      <td className="px-4 py-2 text-sm text-gray-700">{account.status}</td>
                      <td className="px-4 py-2 text-sm text-gray-700 flex space-x-2">
                        <button
                          onClick={() => handleResetPassword(account.name)}
                          className="text-blue-600 hover:underline"
                          title="Reset Password"
                        >
                          🔒
                        </button>
                        <button
                          onClick={() => handleEditAccount(account.name)}
                          className="text-green-600 hover:underline"
                          title="Edit Account"
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDeleteAccount(account.name)}
                          className="text-red-600 hover:underline"
                          title="Delete Account"
                        >
                          🗑️
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            {renderSelectedAccountDetails()}
          </div>
        )}

        {isAddAccountDialogOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-y-auto p-8">
              <h2 className="text-xl font-bold mb-4">ADD NEW FAMILY ACCOUNT</h2>

              {/* Display Save Error Message */}
              {saveError && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded">
                  {saveError}
                </div>
              )}

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Parent Details</h3>
                  <div className="space-y-4">
                    <input
                      type="text"
                      placeholder="Full Name"
                      value={newAccount.name}
                      onChange={(e) => handleInputChange(e, 'name')}
                      className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <input
                      type="email"
                      placeholder="Email"
                      value={newAccount.email}
                      onChange={(e) => handleInputChange(e, 'email')}
                      className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <input
                      type="password"
                      placeholder="Password"
                      value={newAccount.password}
                      onChange={(e) => handleInputChange(e, 'password')}
                      className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Child Details</h3>
                  {newAccount.children.map((child, index) => (
                    <div key={index} className="space-y-2 border-b pb-4 mb-4">
                      <h4 className="text-md font-medium">Child #{index + 1}</h4>
                      <input
                        type="text"
                        placeholder="Name"
                        value={child.name}
                        onChange={(e) => handleInputChange(e, 'name', index)}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <select
                        value={child.year}
                        onChange={(e) => handleInputChange(e, 'year', index)}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        {['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5', 'Year 6'].map((year) => (
                          <option key={year} value={year}>{year}</option>
                        ))}
                      </select>
                      <input
                        type="text"
                        placeholder="Username"
                        value={child.username}
                        onChange={(e) => handleInputChange(e, 'username', index)}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <input
                        type="text"
                        placeholder="PIN (6-digit)"
                        value={child.pin}
                        maxLength={6}
                        onChange={(e) => handleInputChange(e, 'pin', index)}
                        className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      {index > 0 && (
                        <button
                          onClick={() => handleRemoveChild(index)}
                          className="text-red-600 hover:underline text-sm"
                        >
                          Remove Child
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    onClick={handleAddChild}
                    className="text-blue-600 hover:underline font-medium"
                  >
                    + Add Child
                  </button>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">License</h3>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="license"
                        checked={newAccount.license.type === 'FREE_TRIAL'} // Compare with enum value
                        onChange={() => handleLicenseChange('FREE_TRIAL', 14)} // Pass enum value
                      />
                      <span>Free Trial —</span>
                      <input
                        type="number"
                        value={newAccount.license.duration ?? 14} // Use default if null
                        disabled={newAccount.license.type !== 'FREE_TRIAL'} // Disable if not trial
                        onChange={(e) => handleLicenseChange('FREE_TRIAL', parseInt(e.target.value, 10))} // Pass enum value
                        className={`w-16 border rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 ${newAccount.license.type !== 'FREE_TRIAL' ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                      />
                      <span>days</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="license"
                        checked={newAccount.license.type === 'STANDARD_PLAN'} // Compare with enum value
                        onChange={() => handleLicenseChange('STANDARD_PLAN')} // Pass enum value
                      />
                      <span>Standard Plan</span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4 mt-6">
                <button
                  onClick={() => setIsAddAccountDialogOpen(false)}
                  className="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400 font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveAccount}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        )}

        {activeMenu === 'learning' && (
          <div className="bg-white rounded-lg shadow p-6">
            {/* Learning Content */}
            <h1 className="text-2xl font-bold mb-6">Learning</h1>

            {/* Incomplete Quizzes Section */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-4">Incomplete Quizzes (All Students)</h2>
              {loadingAdminIncompleteQuizzes && <p>Loading incomplete quizzes...</p>}
              {adminIncompleteQuizzesError && <p className="text-red-500">Error loading incomplete quizzes: {adminIncompleteQuizzesError}</p>}
              {!loadingAdminIncompleteQuizzes && adminIncompleteQuizzes.length === 0 && (
                <p>No incomplete quizzes found.</p>
              )}
              {!loadingAdminIncompleteQuizzes && adminIncompleteQuizzes.length > 0 && (
                <div className="space-y-4">
                  {adminIncompleteQuizzes.map((quiz) => (
                    <div key={quiz.id} className="p-4 bg-gray-50 rounded-lg border-l-4 border-yellow-500">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">{quiz.childName} - {quiz.subject} {quiz.unit ? `Unit ${quiz.unit}` : ''}</div>
                          <div className="text-sm text-gray-500 mt-1">
                            {quiz.quizType} Quiz - Attempted {quiz.attemptedQuestions} of {quiz.totalQuestions} questions
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          Started: {new Date(quiz.startTime).toLocaleString()}
                          <br />
                          Elapsed: {Math.floor(quiz.elapsedTimeSeconds / 60)}m {quiz.elapsedTimeSeconds % 60}s
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>


            {loadingAdminAnalytics && (
              <div className="flex items-center justify-center p-6 bg-gray-50 rounded-lg mb-6">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                <p>Loading analytics data...</p>
              </div>
            )}

            {adminAnalyticsError && (
              <div className="p-4 mb-6 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start">
                  <div className="text-red-600 mr-3">⚠️</div>
                  <div>
                    <h3 className="font-medium text-red-800">Error loading analytics</h3>
                    <p className="text-red-700 mt-1">{adminAnalyticsError}</p>
                    <p className="text-sm text-red-600 mt-2">Please try refreshing the page or contact support if the issue persists.</p>
                  </div>
                </div>
              </div>
            )}

            {/* Translation Analytics Section */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-4">Translation Analytics (All Students)</h2>

              {!adminTranslationAnalytics && !loadingAdminAnalytics && !adminAnalyticsError && (
                <p className="text-gray-500 italic">No translation data available.</p>
              )}

              {adminTranslationAnalytics && (
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <p className="mb-3">Total Translations: <span className="font-semibold">{adminTranslationAnalytics.translationCount || 0}</span></p>

                  {adminTranslationAnalytics.commonWords && adminTranslationAnalytics.commonWords.length > 0 ? (
                    <>
                      <h3 className="text-lg font-medium mt-4 mb-2">Common Translated Words:</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {adminTranslationAnalytics.commonWords.slice(0, 10).map((word: any, index: number) => (
                          <div key={index} className="bg-gray-50 p-2 rounded">
                            <span className="font-medium">{word.word}</span> <span className="text-gray-500">({word.count})</span>
                          </div>
                        ))}
                      </div>
                    </>
                  ) : (
                    <p className="text-gray-500 italic">No common words data available.</p>
                  )}
                </div>
              )}
            </div>

            {/* Knowledge Analytics Section */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-4">Knowledge Analytics (All Students)</h2>

              {!adminKnowledgeAnalytics && !loadingAdminAnalytics && !adminAnalyticsError && (
                <p className="text-gray-500 italic">No knowledge analytics data available.</p>
              )}

              {adminKnowledgeAnalytics && (
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <p className="mb-3">Total Quiz Attempts: <span className="font-semibold">{adminKnowledgeAnalytics.attemptCount || 0}</span></p>

                  {adminKnowledgeAnalytics.subjectAttemptCounts && Object.keys(adminKnowledgeAnalytics.subjectAttemptCounts).length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-lg font-medium mb-2">Attempts by Subject:</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {Object.entries(adminKnowledgeAnalytics.subjectAttemptCounts).map(([subject, count]: [string, any]) => (
                          <div key={subject} className="bg-gray-50 p-2 rounded">
                            <span className="font-medium">{subject}</span>: {count}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {adminKnowledgeAnalytics.unitAttemptCounts && Object.keys(adminKnowledgeAnalytics.unitAttemptCounts).length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-lg font-medium mb-2">Attempts by Unit:</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {Object.entries(adminKnowledgeAnalytics.unitAttemptCounts).map(([unit, count]: [string, any]) => (
                          <div key={unit} className="bg-gray-50 p-2 rounded">
                            <span className="font-medium">{unit}</span>: {count}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {adminKnowledgeAnalytics.subjectSuccessRates && adminKnowledgeAnalytics.subjectSuccessRates.length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-lg font-medium mb-2">Subject Success Rates:</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {adminKnowledgeAnalytics.subjectSuccessRates.map((item: any, index: number) => (
                          <div key={index} className="bg-gray-50 p-2 rounded">
                            <span className="font-medium">{item.subject}</span>: {item.successRate.toFixed(2)}%
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {adminKnowledgeAnalytics.unitSuccessRates && adminKnowledgeAnalytics.unitSuccessRates.length > 0 && (
                    <div className="mt-4">
                      <h3 className="text-lg font-medium mb-2">Unit Success Rates:</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {adminKnowledgeAnalytics.unitSuccessRates.map((item: any, index: number) => (
                          <div key={index} className="bg-gray-50 p-2 rounded">
                            <span className="font-medium">{item.unit}</span>: {item.successRate.toFixed(2)}%
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>


            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold flex items-center">
                <span className="mr-2">📚</span>
                Curriculum Structure
              </h1>

              <div className="flex items-center space-x-4">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleFileUpload}
                  ref={fileInputRef}
                  className="hidden"
                  id="syllabus-upload"
                />
                <label
                  htmlFor="syllabus-upload"
                  className="cursor-pointer bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center"
                >
                  <span className="mr-2">📄</span>
                  Upload Syllabus
                </label>
              </div>
            </div>

            {uploadStatus && (
              <div className={`mb-6 p-4 rounded-md ${
                uploadStatus.type === 'success'
                  ? 'bg-green-50 text-green-800 border border-green-200'
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {uploadStatus.message}
              </div>
            )}

            <div className="space-y-4">
              {years.sort((a, b) => a.yearNumber - b.yearNumber).map((year) => (
                <div key={year.id} className="border border-gray-200 rounded-lg">
                  <button
                    onClick={() => toggleYear(year.id)}
                    className="w-full px-4 py-3 flex items-center text-left hover:bg-gray-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                  >
                    <ChevronIcon expanded={expandedYears.includes(year.id)} />
                    <h2 className="text-xl font-semibold text-gray-800 flex items-center ml-2">
                      <span className="mr-2">📅</span>
                      Year {year.yearNumber}
                    </h2>
                  </button>

                  {expandedYears.includes(year.id) && (
                    <div className="p-4 space-y-4">
                      {year.subjects.map((subject) => (
                        <div key={subject.id} className="ml-6 border border-gray-100 rounded-lg">
                          <button
                            onClick={() => toggleSubject(subject.id)}
                            className="w-full px-4 py-2 flex items-center text-left hover:bg-gray-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                          >
                            <ChevronIcon expanded={expandedSubjects.includes(subject.id)} />
                            <h3 className="text-lg font-medium text-gray-700 flex items-center ml-2">
                              {subject.name === 'Science' && '🧪'}
                              {subject.name === 'Mathematics' && '🔢'}
                              {subject.name === 'English' && '📚'}
                              <span className="ml-2">{subject.name}</span>
                            </h3>
                          </button>

                          {expandedSubjects.includes(subject.id) && (
                            <div className="p-4">
                              <ul className="space-y-2 ml-8">
                                {subject.units
                                  .sort((a, b) => a.unitNumber - b.unitNumber)
                                  .map((unit) => (
                                    <li key={unit.id} className="text-gray-600 hover:text-gray-900">
                                      <div className="font-medium">Unit {unit.unitNumber}: {unit.topicEn}</div>
                                      <div className="text-sm text-gray-500">{unit.topicZh}</div>
                                    </li>
                                  ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ))}

                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeMenu === 'questions' && (
           <AdminQuestionsPage />
        )}

        {activeMenu === 'notes' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Notes Management</h1>
                <Link href="/admin/notes-viewer" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 font-semibold flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View All Notes
                </Link>
              </div>
            </div>

            <NotesUploader />
          </div>
        )}

        {activeMenu === 'generator' && (
          <QuestionGenerator />
        )}
      </main>
    </div>
  );
}
