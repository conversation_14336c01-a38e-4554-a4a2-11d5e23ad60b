-- CreateTable
CREATE TABLE "TG_StudentMastery" (
    "scope" VARCHAR(10) NOT NULL,
    "scopeId" INTEGER NOT NULL,
    "studentId" INTEGER NOT NULL,
    "currentTp" INTEGER NOT NULL,
    "confidence" VARCHAR(10) NOT NULL,
    "computedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "TG_StudentMastery_pkey" PRIMARY KEY ("studentId","scope","scopeId")
);

-- AddForeignKey
ALTER TABLE "TG_StudentMastery" ADD CONSTRAINT "TG_StudentMastery_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "TG_Child"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
