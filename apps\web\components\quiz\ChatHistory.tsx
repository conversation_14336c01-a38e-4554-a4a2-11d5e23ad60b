import React, { useEffect, useRef, useState } from 'react';
import ChatBubble, { ChatMessage } from './ChatBubble';

interface ChatHistoryProps {
  messages: ChatMessage[];
  displayLanguage: 'en' | 'zh';
}

const ChatHistory: React.FC<ChatHistoryProps> = ({ messages, displayLanguage }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [localMessages, setLocalMessages] = useState<ChatMessage[]>([]);

  // Debug: Log messages when they change
  useEffect(() => {
    console.log('🔍 ChatHistory received messages:', messages);

    // Store messages in localStorage for debugging
    try {
      localStorage.setItem('debug_chat_history_messages', JSON.stringify(messages));
      console.log('💾 Saved ChatHistory messages to localStorage');
    } catch (error) {
      console.error('Error saving ChatHistory messages to localStorage:', error);
    }

    // Update local state with the new messages
    setLocalMessages(messages);

    // Log the count of translation messages
    const translationMessages = messages.filter(msg =>
      msg.content.includes('Translation of "') ||
      msg.content.includes('翻译 "') ||
      msg.content.includes('Terjemahan untuk "')
    );

    console.log('🔍 ChatHistory translation messages:', {
      count: translationMessages.length,
      messages: translationMessages
    });
  }, [messages]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [localMessages]);

  // Try to recover messages from localStorage on mount
  useEffect(() => {
    try {
      const savedMessages = localStorage.getItem('debug_ai_tutor_messages_state');
      if (savedMessages && messages.length === 0) {
        console.log('🔄 Recovering messages from localStorage');
        const parsedMessages = JSON.parse(savedMessages);
        if (Array.isArray(parsedMessages) && parsedMessages.length > 0) {
          setLocalMessages(parsedMessages);
        }
      }
    } catch (error) {
      console.error('Error recovering messages from localStorage:', error);
    }
  }, [messages.length]);

  // Determine which messages to display
  const displayMessages = localMessages.length > 0 ? localMessages : messages;

  return (
    <div className="bg-blue-100 rounded-lg p-2 h-full min-h-[300px] overflow-y-auto flex flex-col">
      {displayMessages.length === 0 ? (
        <div className="text-center text-gray-500 italic my-auto">
          {displayLanguage === 'en'
            ? 'Ask me anything about the question!'
            : '问我任何关于问题的事情！'}
        </div>
      ) : (
        <>
          {displayMessages.map((message) => (
            <ChatBubble
              key={message.id}
              message={message}
              displayLanguage={displayLanguage}
            />
          ))}
          <div ref={messagesEndRef} />

          {/* Debug info */}
          <div className="text-xs text-gray-400 mt-2 border-t pt-2">
            <p>Messages from props: {messages.length}</p>
            <p>Local messages: {localMessages.length}</p>
            <p>Displayed messages: {displayMessages.length}</p>
            <p>Translation messages: {displayMessages.filter(msg =>
              msg.content.includes('Translation of "') ||
              msg.content.includes('翻译 "') ||
              msg.content.includes('Terjemahan untuk "')
            ).length}</p>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatHistory;
