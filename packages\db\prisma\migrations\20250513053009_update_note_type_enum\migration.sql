/*
  Warnings:

  - The values [FILE] on the enum `NoteType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "NoteType_new" AS ENUM ('PDF', 'IMAGE', 'TEXT', 'MARKDOWN', 'OTHER');
ALTER TABLE "TG_Notes" ALTER COLUMN "contentType" DROP DEFAULT;
ALTER TABLE "TG_Notes" ALTER COLUMN "contentType" TYPE "NoteType_new" USING ("contentType"::text::"NoteType_new");
ALTER TYPE "NoteType" RENAME TO "NoteType_old";
ALTER TYPE "NoteType_new" RENAME TO "NoteType";
DROP TYPE "NoteType_old";
ALTER TABLE "TG_Notes" ALTER COLUMN "contentType" SET DEFAULT 'OTHER';
COMMIT;

-- AlterTable
ALTER TABLE "TG_Notes" ALTER COLUMN "contentType" SET DEFAULT 'OTHER';
