/**
 * StorageDriver interface for file storage abstraction
 * This allows for swapping storage implementations (local disk, S3, etc.)
 */
export interface StorageDriver {
  /**
   * Save a file to storage
   * @param file Buffer containing file data
   * @param key Unique identifier/path for the file
   * @param mime MIME type of the file
   * @returns Promise resolving to the public URL of the saved file
   */
  save(file: Buffer, key: string, mime: string): Promise<string>;

  /**
   * Delete a file from storage
   * @param key Unique identifier/path of the file to delete
   */
  delete(key: string): Promise<void>;
}
