import React, { useState } from 'react';
import { useQuizV2 } from './QuizV2Provider';

interface FlagButtonProps {
  questionId: number;
  selectedAnswer: string;
}

const FlagButton: React.FC<FlagButtonProps> = ({ questionId, selectedAnswer }) => {
  const { attempt, displayLanguage } = useQuizV2();
  const [isFlagging, setIsFlagging] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [flagged, setFlagged] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const quizAttemptId = attempt?.quizAttempt?.id;

  // Get text based on language
  const getText = (en: string, zh: string, ms: string) => {
    if (displayLanguage === 'en') return en;
    if (displayLanguage === 'zh') return zh;
    return ms; // Malay
  };

  const handleFlagClick = () => {
    setShowConfirmation(true);
  };

  const handleConfirm = async () => {
    try {
      setIsFlagging(true);
      const response = await fetch('/api/flag-question', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          questionId,
          quizAttemptId,
          submittedKey: selectedAnswer,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to flag question');
      }

      setFlagged(true);
      setShowConfirmation(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsFlagging(false);
    }
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  if (flagged) {
    return (
      <div className="text-center py-2 text-sm text-green-600">
        {getText(
          'Thank you for your feedback!',
          '感谢您的反馈！',
          'Terima kasih atas maklum balas anda!'
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      {error && (
        <div className="text-red-500 text-sm mb-2">{error}</div>
      )}

      {!showConfirmation ? (
        <button
          onClick={handleFlagClick}
          className="absolute top-0 right-0 p-2 text-gray-500 hover:text-red-500 transition-colors"
          title={getText('Flag this question', '标记此问题', 'Tandakan soalan ini')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clipRule="evenodd" />
          </svg>
        </button>
      ) : (
        <div className="absolute top-0 right-0 bg-white shadow-md rounded-lg p-3 z-10 w-64">
          <p className="text-sm mb-3">
            {getText(
              'Do you want to report this question as incorrect?',
              '您想要报告这个问题不正确吗？',
              'Adakah anda ingin melaporkan soalan ini sebagai tidak betul?'
            )}
          </p>
          <div className="flex justify-end space-x-2">
            <button
              onClick={handleCancel}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded"
              disabled={isFlagging}
            >
              {getText('Cancel', '取消', 'Batal')}
            </button>
            <button
              onClick={handleConfirm}
              className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
              disabled={isFlagging}
            >
              {isFlagging
                ? getText('Submitting...', '提交中...', 'Menghantar...')
                : getText('Submit', '提交', 'Hantar')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default FlagButton;
