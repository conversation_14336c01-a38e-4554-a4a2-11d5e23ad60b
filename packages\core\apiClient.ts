/**
 * Shared API client for making requests to the Fastify API server
 */

const getApiUrl = () => {
  // In browser environment, use the public environment variable
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
  }
  
  // In server environment, use internal URL or fallback
  return process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
};

export const api = async <T>(path: string, opts?: RequestInit): Promise<T> => {
  const url = `${getApiUrl()}${path}`;
  
  const response = await fetch(url, {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...opts?.headers,
    },
    ...opts,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`API Error ${response.status}: ${errorText}`);
  }

  return response.json() as Promise<T>;
};

export default api;
