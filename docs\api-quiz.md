# Quiz API Endpoints

This document provides documentation for the quiz-related API endpoints in the My Quiz App application.

## Create Quiz

### Create Practice Quiz

Creates a new practice quiz for the authenticated child.

- **URL**: `/api/quiz/create-practice`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: CHILD

#### Request Body

```json
{
  "subjectId": 1,
  "yearId": 1,
  "unitId": 2  // Optional, if not provided, questions from all units will be included
}
```

#### Response

```json
{
  "id": 123,
  "childId": 1,
  "subjectId": 1,
  "unitId": 2,
  "questionIds": ["1", "2", "3", "4", "5"],
  "currentQuestionIndex": 0,
  "quizType": "MASTERY",
  "status": "ACTIVE",
  "metadata": {
    "configSnapshot": {
      "numQuestions": 5,
      "allowTranslate": true,
      "allowHints": true,
      "allowAiTutor": true
    }
  },
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

## Get Quiz

### Get Quiz by Attempt ID

Retrieves a quiz by its attempt ID.

- **URL**: `/api/quiz/{attemptId}`
- **Method**: `GET`
- **Authentication Required**: Yes
- **Role Required**: CHILD

#### Response

```json
{
  "quizAttempt": {
    "id": 123,
    "childId": 1,
    "subjectId": 1,
    "unitId": 2,
    "questionIds": ["1", "2", "3", "4", "5"],
    "currentQuestionIndex": 0,
    "quizType": "MASTERY",
    "status": "ACTIVE",
    "metadata": {
      "configSnapshot": {
        "numQuestions": 5,
        "allowTranslate": true,
        "allowHints": true,
        "allowAiTutor": true
      }
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  "questions": [
    {
      "id": 1,
      "type": "MULTIPLE_CHOICE",
      "promptEn": "What is 2+2?",
      "promptZh": "2+2等于几?",
      "tpLevel": 1,
      "choices": [
        {
          "id": 1,
          "key": "A",
          "textEn": "3",
          "textZh": "3"
        },
        {
          "id": 2,
          "key": "B",
          "textEn": "4",
          "textZh": "4"
        }
      ]
    }
  ],
  "isCompleted": false
}
```

## Submit Answer

### Submit Answer

Submits an answer for a quiz question.

- **URL**: `/api/submit-answer`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: CHILD

#### Request Body

```json
{
  "quizAttemptId": 123,
  "questionId": 1,
  "submittedKey": "B",
  "submittedText": "4",
  "submittedJson": null,
  "childId": 1,
  "quizType": "mastery",
  "attemptNumber": 1
}
```

#### Response

```json
{
  "isCorrect": true,
  "correctAnswer": {
    "key": "B",
    "text": "4"
  },
  "explanation": "2+2=4 is a basic addition fact."
}
```

## Complete Quiz

### Complete Quiz

Marks a quiz as completed.

- **URL**: `/api/quiz/complete`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: CHILD

#### Request Body

```json
{
  "quizAttemptId": 123,
  "score": 5,
  "totalQuestions": 5,
  "childId": 1
}
```

#### Response

```json
{
  "id": 123,
  "childId": 1,
  "subjectId": 1,
  "unitId": 2,
  "questionIds": ["1", "2", "3", "4", "5"],
  "currentQuestionIndex": 5,
  "quizType": "MASTERY",
  "status": "COMPLETED",
  "score": 5,
  "metadata": {
    "configSnapshot": {
      "numQuestions": 5,
      "allowTranslate": true,
      "allowHints": true,
      "allowAiTutor": true
    }
  },
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z",
  "completedAt": "2023-01-01T00:10:00.000Z"
}
```

## Cancel Quiz

### Cancel Quiz

Cancels an active quiz.

- **URL**: `/api/quiz/cancel`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: CHILD

#### Request Body

```json
{
  "quizAttemptId": 123
}
```

#### Response

```json
{
  "id": 123,
  "childId": 1,
  "subjectId": 1,
  "unitId": 2,
  "questionIds": ["1", "2", "3", "4", "5"],
  "currentQuestionIndex": 2,
  "quizType": "MASTERY",
  "status": "CANCELLED",
  "metadata": {
    "configSnapshot": {
      "numQuestions": 5,
      "allowTranslate": true,
      "allowHints": true,
      "allowAiTutor": true
    }
  },
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:05:00.000Z"
}
```

## Log Quiz Attempt

### Log Quiz Attempt

Logs a quiz attempt event.

- **URL**: `/api/log-quiz-attempt`
- **Method**: `POST`
- **Authentication Required**: Yes
- **Role Required**: CHILD

#### Request Body

```json
{
  "quizAttemptId": 123,
  "currentQuestionIndex": 2,
  "questionId": 2,
  "submittedAnswer": "B",
  "isCorrect": true,
  "childId": 1,
  "score": 2,
  "totalQuestions": 5,
  "quizType": "mastery",
  "questionIds": ["1", "2", "3", "4", "5"]
}
```

#### Response

```json
{
  "success": true,
  "message": "Quiz attempt logged successfully"
}
```

## AI Tutor

### Get AI Tutor Response

Gets a response from the AI tutor for a quiz question.

- **URL**: `/api/ai-tutor`
- **Method**: `POST`
- **Authentication Required**: No
- **Role Required**: None

#### Request Body

```json
{
  "question": "What is 2+2?",
  "language": "en",
  "userQuestion": "Can you explain this to me?"
}
```

#### Response

```json
{
  "response": "2+2 equals 4. This is a basic addition fact where we combine 2 units with another 2 units to get a total of 4 units.",
  "tokenUsage": {
    "prompt": 123,
    "completion": 45,
    "total": 168
  }
}
```
