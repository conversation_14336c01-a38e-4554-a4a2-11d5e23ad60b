// Test component to switch between roles
import { useState, useEffect } from 'react'
import Head from 'next/head'
import { useRouter } from 'next/router'; // Import useRouter
import Dashboard from '../components/Dashboard'
import ParentPortal from '../components/ParentPortal'
import { useSession, signIn, signOut } from 'next-auth/react'
import { Role } from '@prisma/client'

// Test component to switch between roles
function RoleSwitcher({ onSwitch }: { onSwitch: (role: Role) => void }) {
  return (
    <div className="fixed top-4 right-4 z-50 bg-white p-4 rounded-lg shadow-lg">
      <h3 className="text-sm font-bold mb-2">Test Mode: Switch Role</h3>
      <div className="space-x-2">
        <button
          onClick={() => onSwitch(Role.CHILD)}
          className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Student View
        </button>
        <button
          onClick={() => onSwitch(Role.PARENT)}
          className="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Parent View
        </button>
      </div>
    </div>
  );
}

export default function Home() {
  const { data: session, status } = useSession();
  const router = useRouter(); // Initialize useRouter

  // Function to switch roles for testing
  const switchRole = async (role: Role) => {
    // Sign out current session
    await signOut({ redirect: false });

    // Mock credentials based on role
    const credentials = role === Role.CHILD
      ? { username: 'johndoe', pin: '1234' }  // Mock child credentials
      : { email: '<EMAIL>', password: 'password123' };  // Mock parent credentials

    // Sign in with new role
    await signIn('credentials', {
      ...credentials,
      redirect: false
    });
  };

  // Handle redirects based on authentication status and user role
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    } else if (status === 'authenticated' && session?.user) {
      const user = session.user;
      if (user.role === Role.CHILD) {
        router.push('/student-dashboard');
      }
      // Parent users stay on this page to see the ParentPortal
    }
  }, [status, session, router]);

  // Show loading state while session is loading or redirecting
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, show loading while redirecting
  if (status === 'unauthenticated') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  const user = session?.user;

  // If child user, show loading while redirecting
  if (user?.role === Role.CHILD) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/*<RoleSwitcher onSwitch={switchRole} />*/}
      {user?.role === Role.PARENT ? (
        <ParentPortal onAssignHomework={() => {/* Implement homework assignment */}} />
      ) : (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Access Denied</h1>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </div>
        </div>
      )}
    </>
  );
}
