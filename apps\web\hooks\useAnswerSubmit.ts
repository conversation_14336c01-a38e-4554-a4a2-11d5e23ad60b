import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Question } from '../types/quiz';

interface SubmitAnswerParams {
  attemptId: number;
  quizType: 'MASTERY' | 'TEST' | 'QUICK';
  question: Question;
  selected: string;
  timeSpent: number;
}

interface SubmitAnswerResult {
  correct: boolean;
  last: boolean;
  studentAnswer: any;
  quizAttempt: any;
  score?: number;
}

/**
 * Hook for submitting answers to quiz questions
 * @returns Functions and state for submitting answers
 */
export function useAnswerSubmit() {
  const { data: session } = useSession();
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Prepares the submission data based on question type
   */
  const prepareSubmissionData = (params: SubmitAnswerParams) => {
    const { attemptId, question, selected, quizType } = params;
    const questionType = question.type.toString();

    // Base submission data
    const submissionData: any = {
      quizAttemptId: attemptId,
      questionId: question.id,
      childId: session?.user?.id ? Number(session.user.id) : 1,
      quizType: quizType.toLowerCase(), // Convert to lowercase to match the expected format in the API
    };

    // Add the appropriate field based on question type
    if (questionType === 'MULTIPLE_CHOICE' || questionType === 'MULTIPLE_CHOICE_IMAGE' || questionType === 'TRUE_FALSE') {
      // For single choice questions
      submissionData.submittedKey = selected;
    } else if (questionType === 'MULTI_CHOICE') {
      // For multi-select questions
      try {
        // Assuming selected is a JSON string of selected keys
        const selectedKeys = JSON.parse(selected);
        submissionData.submittedJson = { keys: selectedKeys };
      } catch (error) {
        console.error('Error parsing multi-choice answer:', error);
        submissionData.submittedJson = { keys: [] };
      }
    } else if (questionType === 'MATCHING') {
      // For matching questions
      try {
        // Assuming selected is a JSON string of pairs
        const pairs = JSON.parse(selected);
        submissionData.submittedJson = { pairs };
      } catch (error) {
        console.error('Error parsing matching answer:', error);
        submissionData.submittedJson = { pairs: {} };
      }
    } else if (questionType === 'SEQUENCING') {
      // For sequencing questions
      try {
        // Assuming selected is a JSON string of the sequence
        const sequence = JSON.parse(selected);
        submissionData.submittedJson = { order: sequence };
      } catch (error) {
        console.error('Error parsing sequencing answer:', error);
        submissionData.submittedJson = { order: [] };
      }
    } else {
      // For text-based questions (SHORT_ANSWER, FILL_IN_THE_BLANK, LONG_ANSWER)
      submissionData.submittedText = selected;
    }

    return submissionData;
  };

  /**
   * Submits an answer to the API
   * @param params Parameters for submitting an answer
   * @returns Promise resolving to the submission result
   */
  const submit = async (params: SubmitAnswerParams): Promise<SubmitAnswerResult> => {
    setSubmitting(true);
    setError(null);

    try {
      const submissionData = prepareSubmissionData(params);

      // Submit the answer to the API endpoint
      const response = await fetch('/api/submit-answer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        throw new Error(`Error submitting answer: ${response.statusText}`);
      }

      const result = await response.json();

      return {
        correct: result.correct,
        last: result.isLastQuestion,
        studentAnswer: result.studentAnswer,
        quizAttempt: result.quizAttempt,
        score: result.score
      };
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error submitting answer'));
      throw err;
    } finally {
      setSubmitting(false);
    }
  };

  /**
   * Fetches an explanation from the AI tutor for incorrect answers in mastery mode
   * @param question The current question
   * @param incorrectAnswer The incorrect answer submitted by the student
   * @param displayLanguage The display language ('en' or 'zh')
   * @returns Promise resolving to the AI tutor explanation
   */
  const fetchAiTutorExplanation = async (
    question: Question,
    incorrectAnswer: string,
    displayLanguage: 'en' | 'zh'
  ): Promise<string> => {
    try {
      // TODO: Get actual year and subject
      const year = 'Year 5'; // Placeholder
      const subject = 'Science'; // Placeholder

      // Find the full text of the incorrect answer
      let incorrectAnswerText = incorrectAnswer || '';
      let correctAnswerText = question.answer || '';

      // For multiple choice questions, get the full text of the answers
      const questionType = question.type.toString();
      if (questionType === 'MULTIPLE_CHOICE' || questionType === 'MULTIPLE_CHOICE_IMAGE' || questionType === 'TRUE_FALSE') {
        // Determine which language to use based on the question's original language
        const useEnglish = question.originalLanguage === 'EN';
        const useMalay = question.originalLanguage === 'MS';
        const useChinese = question.originalLanguage === 'ZH' || (!useEnglish && !useMalay);

        // Find the choice that matches the incorrect answer key
        const incorrectChoice = question.choices?.find(choice => choice.key === incorrectAnswer);
        if (incorrectChoice) {
          if (useEnglish) {
            incorrectAnswerText = `${incorrectChoice.key}. ${incorrectChoice.textEn}`;
          } else if (useChinese) {
            incorrectAnswerText = `${incorrectChoice.key}. ${incorrectChoice.textZh}`;
          } else {
            // If Malay is available, use it, otherwise fall back to English
            incorrectAnswerText = `${incorrectChoice.key}. ${incorrectChoice.textEn}`;
          }
        }

        // Find the choice that matches the correct answer key
        const correctChoice = question.choices?.find(choice => choice.key === question.answer);
        if (correctChoice) {
          if (useEnglish) {
            correctAnswerText = `${correctChoice.key}. ${correctChoice.textEn}`;
          } else if (useChinese) {
            correctAnswerText = `${correctChoice.key}. ${correctChoice.textZh}`;
          } else {
            // If Malay is available, use it, otherwise fall back to English
            correctAnswerText = `${correctChoice.key}. ${correctChoice.textEn}`;
          }
        }
      }

      const response = await fetch('/api/ai-tutor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          year,
          subject,
          question,
          correctAnswer: correctAnswerText,
          incorrectAnswer: incorrectAnswerText,
          language: displayLanguage,
          topicEn: question.topic,
        }),
      });

      if (!response.ok) {
        throw new Error(`Error fetching AI tutor explanation: ${response.statusText}`);
      }

      const data = await response.json();
      return data.explanation + ' ' + data.hints;
    } catch (error: any) {
      console.error('AI Tutor API error:', error);
      return `Error fetching explanation: ${error.message}`;
    }
  };

  return {
    submit,
    fetchAiTutorExplanation,
    submitting,
    error
  };
}

export default useAnswerSubmit;
