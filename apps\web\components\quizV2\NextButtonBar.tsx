import React from 'react';
import { useRouter } from 'next/router';
import { useQuizV2 } from './QuizV2Provider';
import FlagButton from './FlagButton';

export default function NextButtonBar() {
  const router = useRouter();
  const {
    questions,
    currentIndex,
    selectedAnswer,
    checkAnswer,
    moveToNextQuestion,
    submitting,
    isFinished,
    errorCount,
    explanation,
    setExplanation,
    allowHints,
    fetchHint,
    displayLanguage,
    isLoadingHint,
    isAnswerChecked,
    isAnswerCorrect,
    helpEnabled
  } = useQuizV2();

  // Get the current question
  const currentQuestion = questions[currentIndex];

  // Button is disabled if no answer is selected or submission is in progress
  const isButtonDisabled = !selectedAnswer || submitting;

  // Determine if hint button should be shown - only in PRIMARY phase
  const canShowHint = helpEnabled && allowHints && errorCount > 0 && !explanation && !isAnswerChecked;

  // Determine button text based on state and language
  const getButtonText = () => {
    if (submitting) {
      return displayLanguage === 'en' ? 'SUBMITTING...' :
             displayLanguage === 'ms' ? 'MENGHANTAR...' : '提交中...';
    }

    if (isFinished) {
      return displayLanguage === 'en' ? 'RETURN TO DASHBOARD' :
             displayLanguage === 'ms' ? 'KEMBALI KE PAPAN PEMUKA' : '返回仪表板';
    }

    // If answer has been checked
    if (isAnswerChecked) {
      // Use 'CONTINUE' for both correct and incorrect answers
      return displayLanguage === 'en' ? 'CONTINUE' :
             displayLanguage === 'ms' ? 'TERUSKAN' : '继续';
    }

    // Default state - checking an answer
    return displayLanguage === 'en' ? 'CHECK' :
           displayLanguage === 'ms' ? 'SEMAK' : '检查';
  };

  // Determine button class based on state
  const getButtonClass = () => {
    let baseClass = "w-full py-4 px-4 font-bold text-white rounded-xl transition-all";

    if (isButtonDisabled) {
      return `${baseClass} bg-gray-300 text-gray-500 cursor-not-allowed`;
    }

    if (isFinished) {
      return `${baseClass} bg-green-500 hover:bg-green-600 border-b-4 border-green-700`;
    }

    // Default blue button for all other states (check, continue)
    return `${baseClass} bg-[#0A8CBF] hover:bg-[#04B2D9] border-b-4 border-[#0F5FA6]`;
  };

  // Handle button click
  const handleButtonClick = async () => {
    if (isFinished) {
      // Return to dashboard
      router.push('/student-dashboard');
      return;
    }

    if (!currentQuestion || !selectedAnswer || submitting) return;

    try {
      // If answer has been checked
      if (isAnswerChecked) {
        // Move to the next question regardless of whether the answer was correct or not
        moveToNextQuestion();
      } else {
        // Check the answer
        await checkAnswer(currentQuestion, selectedAnswer);
      }
    } catch (error) {
      console.error('Error handling button click:', error);
    }
  };

  // Handle hint button click
  const handleHintClick = async () => {
    if (!currentQuestion || !allowHints || !helpEnabled) return;

    try {
      console.log('Fetching hint for question:', currentQuestion.id);
      await fetchHint(currentQuestion.id);
    } catch (error) {
      console.error('Error fetching hint:', error);
      // Show a default hint if the API call fails
      setExplanation('Try reading the question carefully and think about what it\'s asking.');
    }
  };

  return (
    <div className="p-5 border-t-2 border-gray-200">
      {/* Show feedback for correct answer */}
      {isAnswerChecked && isAnswerCorrect && (
        <div className="mb-4 p-4 bg-[#e0f7fa] border border-[#0F5FA6] rounded-lg text-[#0F5FA6] relative">
          <h4 className="font-bold mb-2">
            <span className="text-[#0A8CBF] font-bold">
              {displayLanguage === 'en' ? 'Correct!' :
               displayLanguage === 'ms' ? 'Betul!' : '正确!'}
            </span>
          </h4>
          <p>
            {displayLanguage === 'en' ? 'Well done! You got the right answer.' :
             displayLanguage === 'ms' ? 'Bagus! Anda mendapat jawapan yang betul.' : '做得好！你答对了。'}
          </p>

          {/* Flag button for correct answers */}
          {currentQuestion && (
            <div className="absolute top-0 right-0">
              <FlagButton questionId={currentQuestion.id} selectedAnswer={selectedAnswer} />
            </div>
          )}
        </div>
      )}

      {/* Show feedback for incorrect answer */}
      {isAnswerChecked && isAnswerCorrect === false && (
        <div className="mb-4 p-4 bg-[#ffeff0] border border-red-500 rounded-lg text-red-800 relative">
          <h4 className="font-bold mb-2">
            <span className="text-red-600 font-bold">
              {displayLanguage === 'en' ? 'Incorrect.' :
               displayLanguage === 'ms' ? 'Salah.' : '不正确。'}
            </span>
          </h4>

          {/* Show explanation directly under the incorrect message */}
          <div className="mt-3 p-3 bg-white/70 rounded-md text-gray-800 border-l-4 border-yellow-500">
            <p className="italic">
              {explanation ? (
                <span>{explanation}</span>
              ) : (
                <span>
                  {displayLanguage === 'en'
                    ? 'Review the question carefully and note the correct answer highlighted in green.'
                    : displayLanguage === 'ms'
                      ? 'Semak soalan dengan teliti dan perhatikan jawapan yang betul yang ditonjolkan dengan warna hijau.'
                      : '仔细复习问题，注意以绿色突出显示的正确答案。'}
                </span>
              )}
            </p>
          </div>


          {/* Flag button for incorrect answers */}
          {currentQuestion && (
            <div className="absolute top-0 right-0">
              <FlagButton questionId={currentQuestion.id} selectedAnswer={selectedAnswer} />
            </div>
          )}
        </div>
      )}

      {/* Show explanation if available but not for incorrect answers (those are shown inline) */}
      {explanation && !isAnswerChecked && (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-gray-800">
          <h4 className="font-bold mb-2">
            {displayLanguage === 'en' ? 'Explanation:' :
             displayLanguage === 'ms' ? 'Penjelasan:' : '解释:'}
          </h4>
          <p>{explanation}</p>
        </div>
      )}

      <div className="flex items-center gap-2">
        {/* Hint button */}
        {canShowHint && (
          <button
            onClick={handleHintClick}
            disabled={isLoadingHint}
            className={`text-white px-4 py-3 rounded-xl font-bold border-b-4 flex-shrink-0 ${
              isLoadingHint
                ? 'bg-[#04B2D9] border-[#0F5FA6] cursor-wait'
                : 'bg-[#0A8CBF] hover:bg-[#04B2D9] border-[#0F5FA6]'
            }`}
          >
            {isLoadingHint
              ? (displayLanguage === 'en' ? 'LOADING...' :
                 displayLanguage === 'ms' ? 'MEMUATKAN...' : '加载中...')
              : (displayLanguage === 'en' ? 'HINT' :
                 displayLanguage === 'ms' ? 'PETUNJUK' : '提示')
            }
          </button>
        )}

        {/* Next button */}
        <button
          className={getButtonClass()}
          onClick={handleButtonClick}
          disabled={isButtonDisabled}
        >
          {getButtonText()}
        </button>
      </div>

      {/* Show a "Take a break" button */}
      {!isFinished && (
        <div className="mt-3 text-center">
          <button
            onClick={() => router.push('/student-dashboard')}
            className="text-sm text-blue-600 hover:underline"
          >
            {displayLanguage === 'en' ? 'Take a break' :
             displayLanguage === 'ms' ? 'Rehat sebentar' : '休息一下'}
          </button>
        </div>
      )}
    </div>
  );
}
