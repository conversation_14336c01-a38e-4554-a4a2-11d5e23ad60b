import React from 'react';
import { QuestionType, Language } from '@prisma/client';
import { QuestionSpec } from '../../types/question';
import { Keywords } from '../../types/quiz';
import HighlightedText from './HighlightedText';

export interface BaseRendererProps<T extends QuestionType> {
  questionId: number;
  promptEn: string;
  promptZh: string;
  promptMs?: string;
  originalPrompt?: string; // Added for language metadata support
  originalLanguage?: Language; // Added to determine which language keywords to use
  promptMediaUrl?: string;
  promptMediaAlt?: string;
  spec: QuestionSpec<T>;
  keywords?: Keywords; // Added for keyword highlighting
  onAnswerChange: (answer: string) => void;
  selectedAnswer: string;
  displayLanguage: 'en' | 'zh' | 'ms';
  showKeywords?: boolean; // Added to control keyword highlighting
  onMouseUp?: (e: React.MouseEvent) => void;
  isIncorrect?: boolean; // Added to indicate if the current answer is incorrect
  isCorrect?: boolean; // Added to indicate if the current answer is correct
  submittedAnswer?: string; // Added to track the last submitted answer
  disableAnswerSelection?: boolean; // Added to disable answer selection when a correct answer has been submitted
}

export interface RendererWrapperProps {
  children: React.ReactNode;
  onMouseUp?: (e: React.MouseEvent) => void;
}

export const RendererWrapper: React.FC<RendererWrapperProps> = ({
  children,
  onMouseUp
}) => {
  return (
    <div
      className="space-y-4"
      onMouseUp={onMouseUp}
      onClick={(e) => e.stopPropagation()}
      style={{ pointerEvents: 'auto', userSelect: 'text' }}
    >
      {children}
    </div>
  );
};

export const PromptMedia: React.FC<{
  url: string;
  alt?: string;
  style?: 'default' | 'large' | 'small';
}> = ({
  url,
  alt = '',
  style = 'default'
}) => {
  const sizeClasses = {
    default: 'max-w-full max-h-64',
    large: 'max-w-full max-h-96',
    small: 'max-w-48 max-h-48'
  };

  return (
    <div className="flex justify-center my-4">
      <img
        src={url}
        alt={alt}
        className={`${sizeClasses[style]} rounded object-contain`}
      />
    </div>
  );
};
